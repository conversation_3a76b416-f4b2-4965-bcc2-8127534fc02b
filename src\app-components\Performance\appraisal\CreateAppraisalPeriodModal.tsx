import React, { useState, useEffect } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';

// Importing Shadcn UI components (adjust the import paths as needed)
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { X } from 'lucide-react'; // Close icon from Lucide React

// Validation schema using Yup
const validationSchema = Yup.object().shape({
  name: Yup.string().required('Appraisal name is required'),
  start_date: Yup.date().required('Start date is required'),
  end_date: Yup.date()
    .required('End date is required')
    .min(Yup.ref('start_date'), 'End date must be after start date'),
  cycle_type: Yup.string()
    .required('Cycle type is required')
    .oneOf(['ANNUAL', 'SEMI_ANNUAL', 'QUARTERLY'], 'Invalid cycle type'),
  description: Yup.string().required('Description is required'),
});

interface CreateAppraisalModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: (period: any) => void;
}

export const CreateAppraisalModal = ({ open, onClose, onSuccess }: CreateAppraisalModalProps) => {
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  const { token, user } = useSelector((state: RootState) => state.auth);
  const employee_no = user?.employee_no;

  const formik = useFormik({
    initialValues: {
      name: '',
      start_date: '',
      end_date: '',
      cycle_type: 'ANNUAL',
      description: '',
      is_active: false,
      created_by: employee_no || 'EMP/WN/0001',
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        const payload = { ...values, created_by: employee_no || 'EMP/WN/0001' };

        const response = await axios.post(`${BASE_URL}/hrm/appraisal-periods`, payload, {
          headers: { Authorization: `Token ${token}` },
        });

        setSnackbar({
          open: true,
          message: '🎉 Appraisal period created successfully!',
          severity: 'success',
        });
        onSuccess(response.data);
        setTimeout(onClose, 1500);
      } catch (error) {
        let message = '🚨 Error creating appraisal period';
        if (axios.isAxiosError(error)) {
          message = `⚠️ ${error.response?.data?.message || message}`;
        }
        setSnackbar({ open: true, message, severity: 'error' });
      } finally {
        setLoading(false);
      }
    },
  });

  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  useEffect(() => {
    if (!open) {
      formik.resetForm();
    }
  }, [open]);

  return (
    <>
      <Dialog open={open} onOpenChange={(openVal) => !openVal && onClose()}>
        <DialogContent>
          <div className="p-6 bg-white rounded-lg shadow-lg">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold">Create New Appraisal Period</h3>
              {/* <button onClick={onClose} className="p-2">
                <X size={24} />
              </button> */}
            </div>

            <div className="grid gap-6">
              {/* Appraisal Name */}
              <div>
                <Label htmlFor="name">Appraisal Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  placeholder="Enter appraisal name"
                  className="w-full"
                />
                {formik.touched.name && formik.errors.name && (
                  <p className="text-red-500 text-sm">{formik.errors.name}</p>
                )}
              </div>

              {/* Cycle Type */}
              <div>
                <Label htmlFor="cycle_type">Cycle Type *</Label>
                <Select
                  value={formik.values.cycle_type}
                  onValueChange={(value) => formik.setFieldValue('cycle_type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select cycle type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ANNUAL">Annual</SelectItem>
                    <SelectItem value="SEMI_ANNUAL">Semi-Annual</SelectItem>
                    <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
                {formik.touched.cycle_type && formik.errors.cycle_type && (
                  <p className="text-red-500 text-sm">{formik.errors.cycle_type}</p>
                )}
              </div>

              {/* Start Date */}
              <div>
                <Label htmlFor="start_date">Start Date *</Label>
                <Input
                  id="start_date"
                  type="date"
                  name="start_date"
                  value={formik.values.start_date}
                  onChange={formik.handleChange}
                  className="w-full"
                />
                {formik.touched.start_date && formik.errors.start_date && (
                  <p className="text-red-500 text-sm">{formik.errors.start_date}</p>
                )}
              </div>

              {/* End Date */}
              <div>
                <Label htmlFor="end_date">End Date *</Label>
                <Input
                  id="end_date"
                  type="date"
                  name="end_date"
                  value={formik.values.end_date}
                  onChange={formik.handleChange}
                  className="w-full"
                />
                {formik.touched.end_date && formik.errors.end_date && (
                  <p className="text-red-500 text-sm">{formik.errors.end_date}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formik.values.description}
                  onChange={formik.handleChange}
                  placeholder="Enter description"
                  rows={4}
                  className="w-full"
                />
                {formik.touched.description && formik.errors.description && (
                  <p className="text-red-500 text-sm">{formik.errors.description}</p>
                )}
              </div>

              {/* Active Period */}
              <div>
                <Label>Mark as Active</Label>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_active"
                    checked={formik.values.is_active}
                    onChange={formik.handleChange}
                    className="mr-2"
                  />
                  <span>Active</span>
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center mt-6">
              <Button onClick={onClose} disabled={loading}>
                Cancel
              </Button>
              <Button
                onClick={() => formik.handleSubmit()}
                disabled={loading || !formik.isValid}
                className="bg-green-600 text-white"
              >
                {loading ? 'Loading...' : 'Create Appraisal Period'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Snackbar */}
      {snackbar.open && (
        <div className="fixed top-4 right-4 z-50">
          <div
            className={`p-4 rounded shadow ${
              snackbar.severity === 'success' ? 'bg-green-500' : 'bg-red-500'
            } text-white`}
          >
            <span className="mr-2">
              {snackbar.severity === 'success' ? '🎉' : '⚠️'}
            </span>
            <span>{snackbar.message}</span>
            <button onClick={handleCloseSnackbar} className="ml-4">
              <X size={16} />
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default CreateAppraisalModal;
