import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Search,
  Filter,
  Download,
  Users,
  UsersRound,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  X,
  FileText
} from 'lucide-react';
import * as XLSX from 'xlsx';

// Types
interface TeamPerformanceData {
  team_id: number;
  team_name: string;
  department_name: string;
  total_employees: number;
  completed_appraisals: number;
  pending_appraisals: number;
  average_self_rating: number;
  average_supervisor_rating: number;
  completion_rate: number;
  team_lead: string;
  team_assistant?: string;
  top_performers: number;
  needs_improvement: number;
}

interface Team {
  id: number;
  name: string;
  description: string;
  group_head: string;
  group_assistant: string;
  group_hr: string;
  group_status_active: boolean;
  organisation: number;
  parent_group: number | null;
}

interface Department {
  id: number;
  name: string;
  title?: string;
}

interface TeamFilters {
  team?: string;
  department?: string;
  search?: string;
  date_from?: string;
  date_to?: string;
  min_completion_rate?: number;
  performance_threshold?: string;
}

interface TeamPerformanceTabProps {
  onDataChange?: (data: TeamPerformanceData[]) => void;
}

const TeamPerformanceTab: React.FC<TeamPerformanceTabProps> = ({ onDataChange }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State
  const [data, setData] = useState<TeamPerformanceData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [filters, setFilters] = useState<TeamFilters>({});
  const [isExporting, setIsExporting] = useState(false);
  
  // Debounced filters for API calls
  const [debouncedFilters, setDebouncedFilters] = useState<TeamFilters>({});

  // Simple debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 500);

    return () => clearTimeout(timer);
  }, [filters]);

  // Fetch teams and departments
  useEffect(() => {
    const fetchMetadata = async () => {
      if (!token) return;

      try {
        // Fetch teams (organization groups)
        const teamsRes = await fetch(`${BASE_URL}/users/organization_groups`, {
          headers: { Authorization: `Token ${token}` },
        });

        if (teamsRes.ok) {
          const teamsData = await teamsRes.json();
          setTeams(teamsData);
        }

        // Fetch departments
        const deptRes = await fetch(`${BASE_URL}/users/departments`, {
          headers: { Authorization: `Token ${token}` },
        });

        if (deptRes.ok) {
          const deptData = await deptRes.json();
          setDepartments(deptData);
        }
      } catch (err) {
        console.error('Error fetching metadata:', err);
      }
    };

    fetchMetadata();
  }, [token]);

  // Fetch team performance data
  const fetchTeamPerformance = useCallback(async (filterParams: TeamFilters) => {
    if (!token) return;

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filterParams.team && filterParams.team !== 'all') {
        params.append('team', filterParams.team);
      }
      if (filterParams.department && filterParams.department !== 'all') {
        params.append('department', filterParams.department);
      }
      if (filterParams.date_from) params.append('date_from', filterParams.date_from);
      if (filterParams.date_to) params.append('date_to', filterParams.date_to);

      // Fetch appraisal data
      const appraisalUrl = `${BASE_URL}/appraisal_report/${params.toString() ? `?${params.toString()}` : ''}`;
      const appraisalResponse = await fetch(appraisalUrl, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!appraisalResponse.ok) {
        throw new Error(`HTTP error! status: ${appraisalResponse.status}`);
      }

      const appraisalResult = await appraisalResponse.json();
      
      // Fetch employee job info to get team mappings
      const jobInfoResponse = await fetch(`${BASE_URL}/users/employee-job-info-details`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!jobInfoResponse.ok) {
        throw new Error('Failed to fetch employee job info');
      }

      const jobInfoData = await jobInfoResponse.json();
      
      // Process and aggregate data by team
      const teamStats = aggregateByTeam(appraisalResult.results || [], jobInfoData, teams, departments);
      
      // Apply client-side filters
      const filteredData = applyTeamFilters(teamStats, filterParams);
      
      setData(filteredData);
      
      // Notify parent component of data change
      if (onDataChange) {
        onDataChange(filteredData);
      }
      
    } catch (err: any) {
      console.error('Error fetching team performance data:', err);
      setError(err.message || 'Failed to fetch team performance data');
      toast({
        title: "Error",
        description: "Failed to fetch team performance data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [token, toast, onDataChange, teams, departments]);

  // Aggregate appraisal data by team
  const aggregateByTeam = (appraisalData: any[], jobInfoData: any[], teams: Team[], departments: Department[]): TeamPerformanceData[] => {
    // Create team and department maps
    const teamMap = new Map(teams.map(t => [t.id, t]));
    const deptMap = new Map(departments.map(d => [d.id, d]));
    
    // For this example, we'll simulate team assignments since the API might not have direct team mappings
    // In a real implementation, you would have employee-team mappings
    const employeeTeamMap = new Map();
    
    // Simulate team assignments based on department (this would be replaced with actual team data)
    jobInfoData.forEach((job, index) => {
      const teamId = (index % teams.length) + 1; // Simple simulation
      employeeTeamMap.set(job.employee_no, teamId);
    });

    // Group appraisal data by team
    const teamGroups = new Map<number, any[]>();
    
    appraisalData.forEach(record => {
      const teamId = employeeTeamMap.get(record.employeeid_id);
      if (teamId && teamMap.has(teamId)) {
        if (!teamGroups.has(teamId)) {
          teamGroups.set(teamId, []);
        }
        teamGroups.get(teamId)!.push(record);
      }
    });

    // Calculate statistics for each team
    const teamStats: TeamPerformanceData[] = [];
    
    teamGroups.forEach((records, teamId) => {
      const team = teamMap.get(teamId);
      if (!team) return;

      // Get department name (assuming teams belong to departments)
      const deptName = deptMap.get(team.organisation)?.name || 'Unknown Department';

      // Get unique employees in this team
      const uniqueEmployees = new Set(records.map(r => r.employeeid_id));
      const totalEmployees = uniqueEmployees.size;

      // Calculate completion statistics
      const completedEmployees = new Set();
      const pendingEmployees = new Set();
      
      records.forEach(record => {
        if (record.status === 'Completed') {
          completedEmployees.add(record.employeeid_id);
        } else {
          pendingEmployees.add(record.employeeid_id);
        }
      });

      // Calculate average ratings
      const selfRatings = records
        .filter(r => r.total_emp_self_rating_score !== null)
        .map(r => r.total_emp_self_rating_score);
      const supervisorRatings = records
        .filter(r => r.total_supervisor_rating_score !== null)
        .map(r => r.total_supervisor_rating_score);

      const avgSelfRating = selfRatings.length > 0 
        ? selfRatings.reduce((a, b) => a + b, 0) / selfRatings.length 
        : 0;
      const avgSupervisorRating = supervisorRatings.length > 0 
        ? supervisorRatings.reduce((a, b) => a + b, 0) / supervisorRatings.length 
        : 0;

      // Calculate performance categories
      const topPerformers = supervisorRatings.filter(rating => rating >= 80).length;
      const needsImprovement = supervisorRatings.filter(rating => rating < 60).length;

      teamStats.push({
        team_id: teamId,
        team_name: team.name,
        department_name: deptName,
        total_employees: totalEmployees,
        completed_appraisals: completedEmployees.size,
        pending_appraisals: pendingEmployees.size,
        average_self_rating: Math.round(avgSelfRating * 100) / 100,
        average_supervisor_rating: Math.round(avgSupervisorRating * 100) / 100,
        completion_rate: Math.round((completedEmployees.size / totalEmployees) * 100),
        team_lead: team.group_head || 'Not Assigned',
        team_assistant: team.group_assistant,
        top_performers: topPerformers,
        needs_improvement: needsImprovement,
      });
    });

    return teamStats.sort((a, b) => b.completion_rate - a.completion_rate);
  };

  // Apply client-side filters
  const applyTeamFilters = (data: TeamPerformanceData[], filterParams: TeamFilters): TeamPerformanceData[] => {
    return data.filter(record => {
      // Search filter
      if (filterParams.search) {
        const searchTerm = filterParams.search.toLowerCase();
        const searchableText = [
          record.team_name,
          record.department_name,
          record.team_lead,
          record.team_assistant,
        ].join(' ').toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      // Completion rate filter
      if (filterParams.min_completion_rate && record.completion_rate < filterParams.min_completion_rate) {
        return false;
      }

      // Performance threshold filter
      if (filterParams.performance_threshold) {
        const threshold = parseFloat(filterParams.performance_threshold);
        if (record.average_supervisor_rating < threshold) {
          return false;
        }
      }

      return true;
    });
  };

  // Fetch data when debounced filters change
  useEffect(() => {
    if (teams.length > 0 && departments.length > 0) {
      fetchTeamPerformance(debouncedFilters);
    }
  }, [debouncedFilters, fetchTeamPerformance, teams, departments]);

  // Filter handlers
  const handleFilterChange = (key: keyof TeamFilters, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value || undefined
    }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const refreshData = () => {
    fetchTeamPerformance(debouncedFilters);
  };

  return {
    data,
    loading,
    error,
    teams,
    departments,
    filters,
    setFilters,
    isExporting,
    setIsExporting,
    debouncedFilters,
    fetchTeamPerformance,
    handleFilterChange,
    clearFilters,
    refreshData,
    aggregateByTeam,
    applyTeamFilters
  };
};

export default TeamPerformanceTab;
