// src/components/auth-components/EmploymentTypesForm.tsx
"use client";

import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import { createEmploymentType } from "@/redux/features/setup/setupSlice";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface EmploymentTypeForm {
  id: number;
  name: string;
  description: string;
  prefix: string;
  suffix: string;
}

interface EmploymentTypesFormProps {
  onNext: () => void;
}

const EmploymentTypesForm: React.FC<EmploymentTypesFormProps> = ({ onNext }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { organization, empTypesLoading, empTypesError, employmentTypes } = useSelector(
    (state: RootState) => state.setup
  );
  const { toast } = useToast();

  const [forms, setForms] = useState<EmploymentTypeForm[]>([
    { id: Date.now(), name: "", description: "", prefix: "", suffix: "" },
  ]);

  const [alertOpen, setAlertOpen] = useState(false);
  const [alertTitle, setAlertTitle] = useState("");
  const [alertDescription, setAlertDescription] = useState("");

  useEffect(() => {
    if (empTypesError) {
      setAlertTitle("Error");
      setAlertDescription(empTypesError);
      setAlertOpen(true);
    }
  }, [empTypesError]);

  const handleAddRow = () => {
    setForms((prev) => [
      ...prev,
      { id: Date.now(), name: "", description: "", prefix: "", suffix: "" },
    ]);
  };

  const handleRemoveRow = (id: number) => {
    setForms((prev) => prev.filter((row) => row.id !== id));
  };

  const handleRowChange = (
    id: number,
    field: "name" | "description" | "prefix" | "suffix",
    value: string
  ) => {
    setForms((prev) =>
      prev.map((row) => (row.id === id ? { ...row, [field]: value } : row))
    );
  };

  const handleSaveAll = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!organization) {
      toast({
        title: "Organization not found",
        description: "Please create an organization first",
        variant: "destructive",
      });
      return;
    }
    let savedCount = 0;
    for (const form of forms) {
      if (form.name.trim() !== "") {
        const result = await dispatch(
          createEmploymentType({
            name: form.name,
            description: form.description,
            emp_no_prefix: form.prefix,
            emp_no_suffix: form.suffix,
            organisation: organization.id,
          })
        );
        if (result.meta.requestStatus === "fulfilled") savedCount++;
      }
    }
    setAlertTitle("Employment Types Saved");
    setAlertDescription(`Total added: ${savedCount}`);
    setAlertOpen(true);
    setForms([{ id: Date.now(), name: "", description: "", prefix: "", suffix: "" }]);
  };

  const handleAlertClose = () => {
    setAlertOpen(false);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Add Employment Types</h2>
      <form onSubmit={handleSaveAll} className="space-y-4">
        {forms.map((form) => (
          <div key={form.id} className="flex flex-col gap-2 border p-2 rounded">
            <div>
              <Label>Name</Label>
              <Input
                value={form.name}
                onChange={(e) => handleRowChange(form.id, "name", e.target.value)}
                placeholder="e.g. Full-Time"
                required
              />
            </div>
            <div>
              <Label>Description</Label>
              <Input
                value={form.description}
                onChange={(e) => handleRowChange(form.id, "description", e.target.value)}
                placeholder="Short description"
              />
            </div>
            <div>
              <Label>Emp No Prefix</Label>
              <Input
                value={form.prefix}
                onChange={(e) => handleRowChange(form.id, "prefix", e.target.value)}
                placeholder="e.g. FT"
              />
            </div>
            <div>
              <Label>Emp No Suffix</Label>
              <Input
                value={form.suffix}
                onChange={(e) => handleRowChange(form.id, "suffix", e.target.value)}
                placeholder="e.g. US"
              />
            </div>
            {forms.length > 1 && (
              <Button variant="secondary" onClick={() => handleRemoveRow(form.id)}>
                Remove
              </Button>
            )}
          </div>
        ))}
        <div className="flex gap-2">
          <Button type="button" onClick={handleAddRow}>
            + Add Another
          </Button>
          <Button type="submit" disabled={empTypesLoading}>
            {empTypesLoading ? "Saving..." : "Save All Employment Types"}
          </Button>
        </div>
      </form>
      <div className="mt-4">
        <h3 className="font-medium">Existing Employment Types</h3>
        <ul className="list-disc pl-6">
          {employmentTypes.map((et) => (
            <li key={et.id}>{et.name}</li>
          ))}
        </ul>
      </div>
      <Button onClick={onNext} disabled={employmentTypes.length === 0}>
        Next: User
      </Button>
      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{alertTitle}</AlertDialogTitle>
            <AlertDialogDescription>{alertDescription}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleAlertClose}>OK</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default EmploymentTypesForm;
