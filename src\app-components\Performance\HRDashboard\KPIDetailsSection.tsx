import React, { useState } from 'react';
import { 
  ChevronDown, 
  ChevronUp, 
  Loader2, 
  Info 
} from 'lucide-react';
import { KpiDetail } from './types';

interface KPIDetailsSectionProps {
  detailedKpis: KpiDetail[];
  loadingKpis: boolean;
}

const KPIDetailsSection: React.FC<KPIDetailsSectionProps> = ({ 
  detailedKpis, 
  loadingKpis 
}) => {
  const [expandedKpis, setExpandedKpis] = useState<Set<number>>(new Set());

  const handleToggleKpiExpand = (kpiId: number) => {
    setExpandedKpis(prev => {
      const newSet = new Set(prev);
      if (newSet.has(kpiId)) {
        newSet.delete(kpiId);
      } else {
        newSet.add(kpiId);
      }
      return newSet;
    });
  };

  const handleToggleAllKpis = (expand: boolean) => {
    if (expand) {
      setExpandedKpis(new Set(detailedKpis.map(kpi => kpi.id)));
    } else {
      setExpandedKpis(new Set());
    }
  };

  // Render KPI details with updated structure
    const renderKpiDetails = (kpi: KpiDetail) => {
      const isExpanded = expandedKpis.has(kpi.id);
      
      return (
        <div key={kpi.id} className="border border-gray-200 rounded-lg overflow-hidden">
          {/* KPI Header - Always visible */}
          <div 
            className={`flex justify-between items-center p-3 cursor-pointer ${
              isExpanded ? 'bg-green-50 border-b border-gray-200' : 'bg-white'
            }`}
            onClick={() => handleToggleKpiExpand(kpi.id)}
          >
            <div className="flex-1">
              <h4 className="font-medium text-gray-800">{kpi.what}</h4>
              <div className="flex flex-wrap text-sm text-gray-500 mt-1 gap-4">
                <span>Total: {kpi.total_marks} marks</span>
                <span>Employee: {kpi.employee_rating || 0} marks</span>
                <span>Supervisor: {kpi.supervisor_rating || 0} marks</span>
                {kpi.manager_rating !== null && <span>Manager: {kpi.manager_rating || 0} marks</span>}
              </div>
            </div>
            <div className="text-gray-500">
              {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
            </div>
          </div>
          
          {/* KPI Details - Visible when expanded */}
          {isExpanded && (
            <div className="p-4 bg-white">
              {/* KPI Type Specific Details */}
              {kpi.kpi_type === 'MIB Target' ? (
                <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded border border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-1">MIB Target:</h5>
                    <p className="text-sm text-gray-600 font-semibold">
                      {kpi.MIB_target !== null ? kpi.MIB_target : 'No target specified'}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded border border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-1">MIB Achieved:</h5>
                    <p className="text-sm text-gray-600 font-semibold">
                      {kpi.MIB_Achieved !== null ? kpi.MIB_Achieved : 'Not reported'}
                    </p>
                  </div>
                </div>
              ) : kpi.kpi_type === 'Sales Target' ? (
                <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded border border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-1">Sales Target:</h5>
                    <p className="text-sm text-gray-600 font-semibold">
                      {kpi.Sales_target !== null ? kpi.Sales_target : 'No target specified'}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded border border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-1">Sales Achieved:</h5>
                    <p className="text-sm text-gray-600 font-semibold">
                      {kpi.Sales_Achieved !== null ? kpi.Sales_Achieved : 'Not reported'}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="mb-4">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">How to Achieve:</h5>
                  <ul className="list-disc list-inside pl-2 space-y-1">
                    {kpi.hows.map((how, idx) => (
                      <li key={idx} className="text-sm text-gray-600">{how}</li>
                    ))}
                  </ul>
                </div>
              )}
  
              {/* Comments Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                <div className="bg-gray-50 p-3 rounded border border-gray-200">
                  <h5 className="text-sm font-medium text-gray-700 mb-1">Employee Comments:</h5>
                  <p className="text-sm text-gray-600">{kpi.employee_comments}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded border border-gray-200">
                  <h5 className="text-sm font-medium text-gray-700 mb-1">Supervisor Comments:</h5>
                  <p className="text-sm text-gray-600">{kpi.supervisor_comments || 'No comments provided'}</p>
                </div>
              </div>
              
              {/* Optional Manager Comments */}
              {kpi.manager_comments && kpi.manager_comments.trim() !== '' && (
                <div className="mt-3">
                  <div className="bg-gray-50 p-3 rounded border border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-1">Manager Comments:</h5>
                    <p className="text-sm text-gray-600">{kpi.manager_comments}</p>
                  </div>
                </div>
              )}
              
              {/* Optional Extra Comments */}
              {kpi.extra_comments && kpi.extra_comments.trim() !== '' && (
                <div className="mt-3">
                  <div className="bg-gray-50 p-3 rounded border border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-1">Additional Comments:</h5>
                    <p className="text-sm text-gray-600">{kpi.extra_comments}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      );
    };

  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800">KPI Details</h3>
        <div className="flex space-x-2">
          <button 
            onClick={() => handleToggleAllKpis(true)}
            className="text-xs px-2 py-1 bg-green-50 text-green-700 rounded border border-green-200 hover:bg-green-100"
          >
            Expand All
          </button>
          <button 
            onClick={() => handleToggleAllKpis(false)}
            className="text-xs px-2 py-1 bg-gray-50 text-gray-700 rounded border border-gray-200 hover:bg-gray-100"
          >
            Collapse All
          </button>
        </div>
      </div>
      
      {loadingKpis ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 size={24} className="animate-spin text-green-600 mr-2" />
          <span className="text-gray-600">Loading KPI details...</span>
        </div>
      ) : detailedKpis.length === 0 ? (
        <div className="text-center py-6 text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
          <Info size={24} className="mx-auto mb-2 text-gray-400" />
          <p>No KPI details available for this appraisal.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {detailedKpis.map((kpi) => renderKpiDetails(kpi))}
        </div>
      )}
    </div>
  );
};

export default KPIDetailsSection;