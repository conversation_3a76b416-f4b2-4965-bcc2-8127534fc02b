import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import {
  Search,
  Filter,
  Download,
  FileText,
  Users,
  UserCheck,
  AlertCircle,
  RefreshCw,
  X,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import * as XLSX from 'xlsx';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

// Types
interface AppraisalRecord {
  id: number;
  employeeid_id: string;
  supervisor_id: string;
  status: string;
  final_supervisor_comments: string | null;
  total_supervisor_rating_score: number | null;
  final_hr_comments: string | null;
  total_emp_self_rating_score: number | null;
  first_name: string;
  last_name: string;
  employee_comments: string;
  supervisor_comments: string;
  supervisor_rating: number;
  employee_rating: number;
  what_id: number;
  MIB_target: number | null;
  MIB_achieved: number | null;
  Sales_target: number | null;
  Sales_achieved: number | null;
  what: string;
  how: string;
}

interface GroupedKPI {
  what_id: number;
  what: string;
  employee_rating: number;
  supervisor_rating: number;
  employee_comments: string;
  supervisor_comments: string;
  how_items: {
    how: string;
    MIB_target: number | null;
    MIB_achieved: number | null;
    Sales_target: number | null;
    Sales_achieved: number | null;
  }[];
}

interface GroupedAppraisalRecord {
  employeeid_id: string;
  first_name: string;
  last_name: string;
  supervisor_id: string;
  status: string;
  total_kpis: number;
  total_supervisor_rating_score: number | null;
  total_emp_self_rating_score: number | null;
  final_supervisor_comments: string | null;
  final_hr_comments: string | null;
  grouped_kpis: GroupedKPI[];
  expanded?: boolean;
}

interface Department {
  id: number;
  name: string;
  title?: string;
}

interface Team {
  id: number;
  name: string;
  description: string;
  group_head: string;
  group_assistant: string;
  group_hr: string;
  group_status_active: boolean;
  organisation: number;
  parent_group: number | null;
}

interface IndividualFilters {
  employee_no?: string;
  department?: string;
  team?: string;
  status?: string;
  search?: string;
  date_from?: string;
  date_to?: string;
}

interface IndividualPerformanceTabProps {
  onDataChange?: (data: GroupedAppraisalRecord[]) => void;
}

const IndividualPerformanceTab: React.FC<IndividualPerformanceTabProps> = ({ onDataChange }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State
  const [data, setData] = useState<GroupedAppraisalRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [filters, setFilters] = useState<IndividualFilters>({});
  const [isExporting, setIsExporting] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Debounced filters for API calls
  const [debouncedFilters, setDebouncedFilters] = useState<IndividualFilters>({});

  // Simple debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 500);

    return () => clearTimeout(timer);
  }, [filters]);

  // Fetch departments and teams
  useEffect(() => {
    const fetchMetadata = async () => {
      if (!token) return;

      try {
        // Fetch departments
        const deptRes = await fetch(`${BASE_URL}/users/departments`, {
          headers: { Authorization: `Token ${token}` },
        });

        if (deptRes.ok) {
          const deptData = await deptRes.json();
          setDepartments(deptData);
        }

        // Fetch teams (organization groups)
        const teamsRes = await fetch(`${BASE_URL}/users/organization_groups`, {
          headers: { Authorization: `Token ${token}` },
        });

        if (teamsRes.ok) {
          const teamsData = await teamsRes.json();
          setTeams(teamsData);
        }
      } catch (err) {
        console.error('Error fetching metadata:', err);
      }
    };

    fetchMetadata();
  }, [token]);

  // Fetch appraisal data
  const fetchAppraisalData = useCallback(async (filterParams: IndividualFilters) => {
    if (!token) return;

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filterParams.employee_no) params.append('employee_no', filterParams.employee_no);
      if (filterParams.department && filterParams.department !== 'all') params.append('department', filterParams.department);
      if (filterParams.team && filterParams.team !== 'all') params.append('team', filterParams.team);
      if (filterParams.date_from) params.append('date_from', filterParams.date_from);
      if (filterParams.date_to) params.append('date_to', filterParams.date_to);

      const url = `${BASE_URL}/appraisal_report/${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      // Group records by employee ID
      const groupedData = groupRecordsByEmployee(result.results || []);
      
      // Apply client-side filters
      const filteredData = applyClientSideFilters(groupedData, filterParams);
      
      setData(filteredData);
      setTotalResults(result['Total Results'] || result.count || filteredData.length);
      
      // Notify parent component of data change
      if (onDataChange) {
        onDataChange(filteredData);
      }
      
    } catch (err: any) {
      console.error('Error fetching appraisal data:', err);
      setError(err.message || 'Failed to fetch appraisal data');
      toast({
        title: "Error",
        description: "Failed to fetch appraisal data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [token, toast, onDataChange]);

  // Group records by employee ID and then by what_id
  const groupRecordsByEmployee = (records: AppraisalRecord[]): GroupedAppraisalRecord[] => {
    const grouped = records.reduce((acc, record) => {
      const key = record.employeeid_id;

      if (!acc[key]) {
        acc[key] = {
          employeeid_id: record.employeeid_id,
          first_name: record.first_name,
          last_name: record.last_name,
          supervisor_id: record.supervisor_id,
          status: record.status,
          total_kpis: 0,
          total_supervisor_rating_score: record.total_supervisor_rating_score,
          total_emp_self_rating_score: record.total_emp_self_rating_score,
          final_supervisor_comments: record.final_supervisor_comments,
          final_hr_comments: record.final_hr_comments,
          grouped_kpis: [],
        };
      }

      // Find existing KPI group or create new one
      let kpiGroup = acc[key].grouped_kpis.find(kpi => kpi.what_id === record.what_id);

      if (!kpiGroup) {
        kpiGroup = {
          what_id: record.what_id,
          what: record.what,
          employee_rating: record.employee_rating,
          supervisor_rating: record.supervisor_rating,
          employee_comments: record.employee_comments,
          supervisor_comments: record.supervisor_comments,
          how_items: [],
        };
        acc[key].grouped_kpis.push(kpiGroup);
      }

      // Add the "how" item to this KPI group (only if not already added)
      const existingHow = kpiGroup.how_items.find(item => item.how === record.how);
      if (!existingHow) {
        kpiGroup.how_items.push({
          how: record.how,
          MIB_target: record.MIB_target,
          MIB_achieved: record.MIB_achieved,
          Sales_target: record.Sales_target,
          Sales_achieved: record.Sales_achieved,
        });
      }

      return acc;
    }, {} as Record<string, GroupedAppraisalRecord>);

    // Update total KPI count for each employee
    Object.values(grouped).forEach(employee => {
      employee.total_kpis = employee.grouped_kpis.length;
    });

    return Object.values(grouped);
  };

  // Apply client-side filters
  const applyClientSideFilters = (data: GroupedAppraisalRecord[], filterParams: IndividualFilters): GroupedAppraisalRecord[] => {
    return data.filter(record => {
      // Status filter
      if (filterParams.status && filterParams.status !== 'all' && record.status !== filterParams.status) {
        return false;
      }

      // Search filter (searches in name, employee ID, comments, KPIs)
      if (filterParams.search) {
        const searchTerm = filterParams.search.toLowerCase();
        const searchableText = [
          record.first_name,
          record.last_name,
          record.employeeid_id,
          record.final_supervisor_comments,
          record.final_hr_comments,
          ...record.grouped_kpis.map(kpi => kpi.what),
          ...record.grouped_kpis.flatMap(kpi => kpi.how_items.map(item => item.how)),
        ].join(' ').toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      return true;
    });
  };

  return { 
    data, 
    loading, 
    error, 
    departments, 
    teams, 
    filters, 
    setFilters, 
    isExporting, 
    setIsExporting, 
    totalResults, 
    currentPage, 
    setCurrentPage, 
    itemsPerPage, 
    debouncedFilters, 
    fetchAppraisalData, 
    groupRecordsByEmployee, 
    applyClientSideFilters 
  };
};

export default IndividualPerformanceTab;
