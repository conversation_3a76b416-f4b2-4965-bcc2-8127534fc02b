import React from 'react';
import { useLocation } from 'react-router-dom'; // Import useLocation to access state

import { Screen } from '@/app-components/layout/screen';
import HRKpiApprovalSection from '../../app-components/Performance/HRDashboard/HRKpiApprovalSection';

const HRKpiApprovalPage = () => {
  // Access the location object to retrieve state passed from the previous page
  const location = useLocation();
  const { customKpis } = location.state || {};  // Destructure customKpis from location.state

  return (
    <Screen>
      <HRKpiApprovalSection initialCustomKpis={customKpis || []} />
    </Screen>
  );
};

export default HRKpiApprovalPage;
