"use client";

import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";

import PipelineBarChart from "../charts/PipelineBarChart";
import { JobVacancy } from "../types";
import { BASE_URL } from "@/config";

interface PipelineAnalyticsProps {
  selectedJobId: string | null;
  setSelectedJobId: React.Dispatch<React.SetStateAction<string | null>>;
}

const PipelineAnalytics: React.FC<PipelineAnalyticsProps> = ({
  selectedJobId,
  setSelectedJobId,
}) => {
  const navigate = useNavigate();
  const [vacancies, setVacancies] = useState<JobVacancy[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchVacancies = async () => {
      setLoading(true);
      try {
        const res = await fetch(`${BASE_URL}/hrm/vacancies`, {
          headers: { "Content-Type": "application/json" },
        });
        if (!res.ok) throw new Error("Failed to fetch vacancies");
        const data: JobVacancy[] = await res.json();
        setVacancies(data);
      } catch (err: any) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchVacancies();
  }, []);

  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 p-4 rounded shadow">
        <p className="text-lg font-semibold mb-2">Job Requisitions</p>
        {loading ? (
          <p>Loading requisitions...</p>
        ) : (
          <ul className="divide-y">
            {vacancies.map((job) => (
              <li
                key={job.id}
                className={`p-2 cursor-pointer rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
                  selectedJobId === job.id.toString() ? "bg-gray-200 dark:bg-gray-600" : ""
                }`}
                onClick={() => setSelectedJobId(job.id.toString())}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-semibold">{job.job_details.job_title}</p>
                    <p className="text-xs text-gray-500">{job.job_details.job_code}</p>
                  </div>
                  <span className="text-xl">&rarr;</span>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
      <div className="bg-white dark:bg-gray-800 p-4 rounded shadow min-h-[300px]">
        {selectedJobId ? (
          <PipelineBarChart vacancyId={Number(selectedJobId)} />
        ) : (
          <p>Select a requisition to view its pipeline.</p>
        )}
      </div>
      <div className="text-right">
        <Button onClick={() => navigate("/recruitment/jobrequisition")}>
          Create Job Requisition
        </Button>
      </div>
    </div>
  );
};

export default PipelineAnalytics;
