import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { BASE_URL } from "../../config";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  RefreshCw,
  Pencil,
  Trash2,
  Plus,
  CheckCircle2,
  Search,
  ArrowUpDown,
  AlertCircle,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

// Define interfaces for employee permission, employee and permission
interface EmployeePermission {
  id: number;
  employee_no: string;
  permission: number;
}

interface Employee {
  employee_no: string;
  username: string;
}

interface Permission {
  id: number;
  permission_name: string;
}

// Form schema for employee permission assignment
const employeePermissionSchema = z.object({
  employee_no: z.string().min(1, "Employee is required"),
  permission: z.number(),
});

export default function EmployeePermissionsManagement() {
  const navigate = useNavigate();
  const { token } = useSelector((state: RootState) => state.auth);
  const [employeePermissions, setEmployeePermissions] = useState<EmployeePermission[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentEmployeePermission, setCurrentEmployeePermission] = useState<EmployeePermission | null>(null);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState<{ key: keyof EmployeePermission; direction: "ascending" | "descending" } | null>(null);
  const [filteredEmployeePermissions, setFilteredEmployeePermissions] = useState<EmployeePermission[]>([]);

  // Initialize react-hook-form
  const form = useForm<z.infer<typeof employeePermissionSchema>>({
    resolver: zodResolver(employeePermissionSchema),
    defaultValues: {
      employee_no: "",
      permission: 0,
    },
  });

  // Reset form when dialog closes
  useEffect(() => {
    if (!isAddDialogOpen && !isEditDialogOpen) {
      form.reset();
      setFormError("");
    }
  }, [isAddDialogOpen, isEditDialogOpen, form]);

  // Fetch employee permissions
  const fetchEmployeePermissions = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await axios.get(`${BASE_URL}/users/employee-permissions`, {
        headers: { Authorization: `Token ${token}` },
      });
      setEmployeePermissions(response.data);
      setFilteredEmployeePermissions(response.data);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching employee permissions:", err);
      setError("Failed to load employee permissions. Please try again.");
      setLoading(false);
    }
  };

  // Fetch employees for dropdown
  const fetchEmployees = async () => {
    try {
      const response = await axios.get(`${BASE_URL}/users/all-employees`, {
        headers: { Authorization: `Token ${token}` },
      });
      setEmployees(response.data);
    } catch (err) {
      console.error("Error fetching employees:", err);
    }
  };

  // Fetch permissions for dropdown
  const fetchPermissions = async () => {
    try {
      const response = await axios.get(`${BASE_URL}/users/permissions`, {
        headers: { Authorization: `Token ${token}` },
      });
      setPermissions(response.data);
    } catch (err) {
      console.error("Error fetching permissions:", err);
    }
  };

  useEffect(() => {
    if (token) {
      fetchEmployeePermissions();
      fetchEmployees();
      fetchPermissions();
    }
  }, [token]);

  // Filtering and sorting
  useEffect(() => {
    let result = [...employeePermissions];
    if (searchTerm) {
      result = result.filter((ep) => {
        const employee = employees.find((emp) => emp.employee_no === ep.employee_no);
        const permission = permissions.find((perm) => perm.id === ep.permission);
        return (
          (employee?.username.toLowerCase().includes(searchTerm.toLowerCase()) ?? false) ||
          (permission?.permission_name.toLowerCase().includes(searchTerm.toLowerCase()) ?? false)
        );
      });
    }
    if (sortConfig !== null) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.key] ?? "";
        const bValue = b[sortConfig.key] ?? "";
        if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
        return 0;
      });
    }
    setFilteredEmployeePermissions(result);
  }, [employeePermissions, searchTerm, sortConfig, employees, permissions]);

  const requestSort = (key: keyof EmployeePermission) => {
    let direction: "ascending" | "descending" = "ascending";
    if (sortConfig && sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  // Handle form submission for adding assignment
  const onSubmitAdd = async (data: z.infer<typeof employeePermissionSchema>) => {
    try {
      setIsSubmitting(true);
      setFormError("");
      await axios.post(`${BASE_URL}/users/employee-permissions`, data, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchEmployeePermissions();
      setIsAddDialogOpen(false);
      setSuccessMessage("Permission assigned successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err: any) {
      console.error("Error assigning permission:", err);
      setFormError(err.response?.data?.message || "Failed to assign permission. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission for editing assignment
  const onSubmitEdit = async (data: z.infer<typeof employeePermissionSchema>) => {
    if (!currentEmployeePermission) return;
    try {
      setIsSubmitting(true);
      setFormError("");
      await axios.patch(`${BASE_URL}/users/employee-permissions/${currentEmployeePermission.id}`, data, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchEmployeePermissions();
      setIsEditDialogOpen(false);
      setSuccessMessage("Permission assignment updated successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err: any) {
      console.error("Error updating permission assignment:", err);
      setFormError(err.response?.data?.message || "Failed to update assignment. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deletion of an assignment
  const handleDelete = async () => {
    if (!deleteId) return;
    try {
      setIsSubmitting(true);
      await axios.delete(`${BASE_URL}/users/employee-permissions/${deleteId}`, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchEmployeePermissions();
      setDeleteId(null);
      setSuccessMessage("Permission assignment deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err: any) {
      console.error("Error deleting permission assignment:", err);
      setError(err.response?.data?.message || "Failed to delete assignment. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Populate form for editing an assignment
  const handleEdit = (ep: EmployeePermission) => {
    setCurrentEmployeePermission(ep);
    form.reset({
      employee_no: ep.employee_no,
      permission: ep.permission,
    });
    setIsEditDialogOpen(true);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSortConfig(null);
  };

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink href="/" className="text-gray-500 hover:text-gray-700">
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block" />
        <BreadcrumbItem>
          <BreadcrumbLink href="/dashboard" className="text-gray-500 hover:text-gray-700">
            Dashboard
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Employee Permissions</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  if (loading && employeePermissions.length === 0) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-gray-600">Loading employee permissions...</p>
          </div>
        </div>
      </Screen>
    );
  }

  if (error && employeePermissions.length === 0) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="bg-red-50 border border-red-200 p-6 rounded-lg max-w-md text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-red-700 mb-2">Error Loading Assignments</h2>
            <p className="text-gray-700">{error}</p>
            <button
              onClick={() => fetchEmployeePermissions()}
              className="mt-4 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
            >
              <RefreshCw className="w-4 h-4 inline-block mr-2" />
              Retry
            </button>
          </div>
        </div>
      </Screen>
    );
  }

  // Helper functions to display employee and permission names
  const getEmployeeName = (employee_no: string) => {
    const emp = employees.find((e) => e.employee_no === employee_no);
    return emp ? emp.username : employee_no;
  };

  const getPermissionName = (permissionId: number) => {
    const perm = permissions.find((p) => p.id === permissionId);
    return perm ? perm.permission_name : permissionId;
  };

  return (
    <Screen headerContent={breadcrumb}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Plus className="w-7 h-7 mr-2 text-green-600" />
              Employee Permissions Management
            </h1>
            <p className="text-gray-600 mt-1">Assign and manage permissions for employees.</p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700">
                <Plus className="w-4 h-4 mr-2" />
                Assign Permission
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Assign New Permission</DialogTitle>
                <DialogDescription>
                  Select an employee and assign a permission.
                </DialogDescription>
              </DialogHeader>
              {formError && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                  <p className="text-red-600 text-sm">{formError}</p>
                </div>
              )}
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmitAdd)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="employee_no"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Employee</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value} defaultValue="">
                            <SelectTrigger>
                              <SelectValue placeholder="Select an employee" />
                            </SelectTrigger>
                            <SelectContent>
                              {employees.map((emp) => (
                                <SelectItem key={emp.employee_no} value={emp.employee_no}>
                                  {emp.username}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="permission"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Permission</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(val) => field.onChange(Number(val))}
                            value={field.value ? field.value.toString() : ""}
                            defaultValue=""
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select a permission" />
                            </SelectTrigger>
                            <SelectContent>
                              {permissions.map((perm) => (
                                <SelectItem key={perm.id} value={perm.id.toString()}>
                                  {perm.permission_name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Assigning...
                        </>
                      ) : (
                        "Assign Permission"
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
        {successMessage && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-center">
            <CheckCircle2 className="w-5 h-5 text-green-600 mr-3" />
            <p className="text-green-600">{successMessage}</p>
          </div>
        )}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col lg:flex-row gap-4 lg:items-center justify-between">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search assignments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Assignments ({filteredEmployeePermissions.length})</span>
              {loading && (
                <div className="flex items-center text-sm text-gray-500">
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Refreshing data...
                </div>
              )}
            </CardTitle>
            <CardDescription>
              Overview of employee permission assignments.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredEmployeePermissions.length === 0 ? (
              <div className="text-center py-8">
                <Plus className="w-12 h-12 mx-auto text-gray-400 mb-3" />
                <h3 className="text-lg font-medium text-gray-900">No assignments found</h3>
                <p className="text-gray-500 mt-1">
                  {searchTerm ? "Try adjusting your search filters" : "Assign a permission to an employee to get started"}
                </p>
                {searchTerm && (
                  <Button variant="outline" onClick={clearFilters} className="mt-4">
                    Clear Filters
                  </Button>
                )}
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => requestSort("employee_no")}
                      >
                        <div className="flex items-center">
                          Employee
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => requestSort("permission")}
                      >
                        <div className="flex items-center">
                          Permission
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEmployeePermissions.map((ep) => (
                      <TableRow key={ep.id}>
                        <TableCell className="font-medium">{getEmployeeName(ep.employee_no)}</TableCell>
                        <TableCell>{getPermissionName(ep.permission)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Dialog
                              open={isEditDialogOpen && currentEmployeePermission?.id === ep.id}
                              onOpenChange={setIsEditDialogOpen}
                            >
                              <DialogTrigger asChild>
                                <Button variant="outline" size="icon" onClick={() => handleEdit(ep)}>
                                  <Pencil className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-md">
                                <DialogHeader>
                                  <DialogTitle>Edit Assignment</DialogTitle>
                                  <DialogDescription>
                                    Update assignment for {getEmployeeName(ep.employee_no)}
                                  </DialogDescription>
                                </DialogHeader>
                                {formError && (
                                  <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                                    <p className="text-red-600 text-sm">{formError}</p>
                                  </div>
                                )}
                                <Form {...form}>
                                  <form onSubmit={form.handleSubmit(onSubmitEdit)} className="space-y-4">
                                    <FormField
                                      control={form.control}
                                      name="employee_no"
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Employee</FormLabel>
                                          <FormControl>
                                            <Select onValueChange={field.onChange} value={field.value} defaultValue="">
                                              <SelectTrigger>
                                                <SelectValue placeholder="Select an employee" />
                                              </SelectTrigger>
                                              <SelectContent>
                                                {employees.map((emp) => (
                                                  <SelectItem key={emp.employee_no} value={emp.employee_no}>
                                                    {emp.username}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />
                                    <FormField
                                      control={form.control}
                                      name="permission"
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Permission</FormLabel>
                                          <FormControl>
                                            <Select
                                              onValueChange={(val) => field.onChange(Number(val))}
                                              value={field.value ? field.value.toString() : ""}
                                              defaultValue=""
                                            >
                                              <SelectTrigger>
                                                <SelectValue placeholder="Select a permission" />
                                              </SelectTrigger>
                                              <SelectContent>
                                                {permissions.map((perm) => (
                                                  <SelectItem key={perm.id} value={perm.id.toString()}>
                                                    {perm.permission_name}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />
                                    <DialogFooter>
                                      <Button type="submit" disabled={isSubmitting}>
                                        {isSubmitting ? (
                                          <>
                                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                            Updating...
                                          </>
                                        ) : (
                                          "Update Assignment"
                                        )}
                                      </Button>
                                    </DialogFooter>
                                  </form>
                                </Form>
                              </DialogContent>
                            </Dialog>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="outline" size="icon" onClick={() => setDeleteId(ep.id)}>
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action will permanently delete the assignment.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel onClick={() => setDeleteId(null)}>
                                    Cancel
                                  </AlertDialogCancel>
                                  <AlertDialogAction onClick={handleDelete} disabled={isSubmitting}>
                                    {isSubmitting ? (
                                      <>
                                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Screen>
  );
}
