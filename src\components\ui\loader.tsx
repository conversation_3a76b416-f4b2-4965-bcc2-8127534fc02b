"use client";

import React from "react";
import { Loader2 } from "lucide-react";

interface LoaderProps {
  /**
   * The size of the loader icon. Can be a number (in pixels) or a string.
   * @default 24
   */
  size?: number | string;
  /**
   * The color of the loader icon.
   * @default "currentColor"
   */
  color?: string;
}

export const Loader: React.FC<LoaderProps> = ({ size = 24, color = "currentColor" }) => {
  return (
    <Loader2
      className="animate-spin"
      size={size}
      color={color}
    />
  );
};
