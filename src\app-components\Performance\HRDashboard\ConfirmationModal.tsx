// src/components/ConfirmationModal.tsx

import React from 'react';

interface ConfirmationModalProps {
  pendingAction: 'approve' | 'reject';
  setConfirmModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  handleApproveRejectRecord: (action: 'approve' | 'reject') => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  pendingAction,
  setConfirmModalVisible,
  handleApproveRejectRecord,
}) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-xl overflow-hidden">
        <div className="bg-gradient-to-r from-green-600 to-green-800 text-white px-6 py-4">
          <h2 className="text-xl font-semibold">Confirm Action</h2>
        </div>
        <div className="p-6">
          <p className="text-gray-700 mb-6">
            Are you sure you want to {pendingAction === 'approve' ? 'approve' : 'reject'} this appraisal? 
            This action cannot be undone.
          </p>
          <div className="flex justify-end space-x-4">
            <button
              onClick={() => setConfirmModalVisible(false)}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                setConfirmModalVisible(false);
                handleApproveRejectRecord(pendingAction!);
              }}
              className={`px-4 py-2 text-white rounded-md ${
                pendingAction === 'approve' 
                ? 'bg-green-600 hover:bg-green-700' 
                : 'bg-red-600 hover:bg-red-700'
              }`}
            >
              Confirm
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
