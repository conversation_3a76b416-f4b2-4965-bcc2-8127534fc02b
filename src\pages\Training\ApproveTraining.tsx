"use client";

import { useEffect, useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { TrainingApproval } from "../../app-components/training/TrainingApproval";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { BASE_URL } from "@/config";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

interface ApproveTrainingProps {
  contentId?: number; // or string, adjust as needed
}

export default function ApproveTraining({ contentId }: ApproveTrainingProps) {
  const token = useSelector((state: RootState) => state.auth.token);

  const [training, setTraining] = useState({
    name: "",
    description: "",
    evaluationLink: "",
    evaluationFile: null as File | null,
  });

  // Fetch training content details if we have a contentId
  useEffect(() => {
    if (!contentId) return;

    const fetchData = async () => {
      try {
        const response = await fetch(
          `${BASE_URL}/hrm/training-content-details/${contentId}`
        );
        if (!response.ok) {
          throw new Error("Failed to fetch training content");
        }
        const data = await response.json();
        setTraining({
          name: data.title || "",
          description: data.description || "",
          evaluationLink: data.file || "",
          evaluationFile: null,
        });
      } catch (error) {
        console.error(error);
      }
    };

    fetchData();
  }, [contentId]);

  // Approve the training
  const handleApprove = async () => {
    if (!contentId) return;
    try {
      const response = await fetch(
        `${BASE_URL}/hrm/training-content-details/${contentId}/approve_content`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            title: training.name,
            description: training.description,
            // status: 'approved' if needed, or the server sets it automatically
          }),
        }
      );
      if (!response.ok) {
        throw new Error("Failed to approve training");
      }
      const result = await response.json();
      console.log("Training approved:", result);
    } catch (error) {
      console.error(error);
    }
  };

  // Reject the training
  const handleReject = async () => {
    if (!contentId) return;
    try {
      const response = await fetch(
        `${BASE_URL}/hrm/training-content-details/${contentId}`,
        {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            title: training.name,
            description: training.description,
            status: "rejected",
          }),
        }
      );
      if (!response.ok) {
        throw new Error("Failed to reject training");
      }
      const result = await response.json();
      console.log("Training rejected:", result);
    } catch (error) {
      console.error(error);
    }
  };

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Approve Training
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  return (
    <Screen headerContent={breadcrumb}>
      <TrainingApproval
        training={training}
        onApprove={handleApprove}
        onReject={handleReject}
      />
    </Screen>
  );
}
