"use client";

import React from "react";

interface InfoChipProps {
  icon: React.ReactElement;
  label: string;
}

export const InfoChip: React.FC<InfoChipProps> = ({ icon, label }) => {
  return (
    <div className="flex items-center space-x-2 rounded-full border px-2 py-1 text-sm text-muted-foreground">
      <div className="text-primary">{icon}</div>
      <span>{label}</span>
    </div>
  );
};
