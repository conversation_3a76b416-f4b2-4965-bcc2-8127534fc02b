"use client";
import React, { useState, useEffect, ChangeEvent } from "react";
import { motion, AnimatePresence } from "framer-motion";
import axios from "axios";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { BASE_URL } from "../../../config";

// Shadcn UI (Dialog, Button, Input, etc.)
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import KpiBulkUpload from "./KpiBulkUpload";
import { KpiBulkData } from "./KpiBulkUpload";

// Lucide-react icons
import {
  Save,
  Plus,
  CheckSquare,
  Users,
  Briefcase,
  ArrowRight,
  ArrowDown,
  ArrowLeft,
  ClipboardList,
  AlertTriangle,
  Loader2,
  Search,
  X,
} from "lucide-react";


// Types for our internal state
export interface KPIWhat {
  what: string;
  total_marks: number;
  hows: string[];
  errors?: {
    what?: string;
    total_marks?: string;
    hows?: (string | undefined)[];
  };
}

export interface AssignmentTarget {
  type: "employee" | "job";
  ids: (string | number)[];
}

interface KpiWizardModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: (message: any) => void;
  token?: string | null;
  employees?: any[];
  jobPositions?: any[];
}
const KpiWizardModal: React.FC<KpiWizardModalProps> = ({ open, onClose }) => {
  const { token, user } = useSelector((state: RootState) => state.auth);
  const [activeStep, setActiveStep] = useState(0);
  const [kpis, setKpis] = useState<KPIWhat[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [jobs, setJobs] = useState<any[]>([]);
  const [assignment, setAssignment] = useState<AssignmentTarget>({
    type: "employee",
    ids: [],
  });
  const [loading, setLoading] = useState(false);
  const [assignmentError, setAssignmentError] = useState("");
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error" | "info",
  });

  // Search and filter states for manual entry
  const [searchTerm, setSearchTerm] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState("all");
  const [departments, setDepartments] = useState<any[]>([]);
  // Define the steps with corresponding icons
  const steps = [
    { label: "Define KPIs & Metrics", icon: <CheckSquare size={20} /> },
    { label: "Assign Targets", icon: <Users size={20} /> },
    { label: "Review & Submit", icon: <Briefcase size={20} /> },
  ];

  // Enhanced search functions
  const getDepartmentName = (departmentId: number) => {
    const department = departments.find(dept => dept.id === departmentId);
    return department ? department.name : "Unknown Department";
  };

  const searchEmployees = (emp: any, searchTerm: string) => {
    if (!searchTerm) return true;

    const search = searchTerm.toLowerCase().trim();
    const deptName = getDepartmentName(emp.department_id).toLowerCase();

    return (
      emp.name.toLowerCase().includes(search) ||
      emp.employee_no.toLowerCase().includes(search) ||
      deptName.includes(search) ||
      `${emp.name} ${emp.employee_no}`.toLowerCase().includes(search)
    );
  };

  const searchJobs = (job: any, searchTerm: string) => {
    if (!searchTerm) return true;

    const search = searchTerm.toLowerCase().trim();

    return (
      job.title.toLowerCase().includes(search) ||
      job.code.toLowerCase().includes(search) ||
      `${job.title} ${job.code}`.toLowerCase().includes(search)
    );
  };

  // Fetch options when the modal is open
  useEffect(() => {
    if (open) {
      const fetchOptions = async () => {
        try {
          const [empRes, jobRes, deptRes] = await Promise.all([
            axios.get(`${BASE_URL}/users/all-employees`, {
              headers: { Authorization: `Token ${token}` },
            }),
            axios.get(`${BASE_URL}/hrm/job-positions`, {
              headers: { Authorization: `Token ${token}` },
            }),
            axios.get(`${BASE_URL}/users/departments`, {
              headers: { Authorization: `Token ${token}` },
            }),
          ]);
          setEmployees(
            empRes.data.map((e: any) => ({
              id: e.employee_no,
              name: `${e.first_name} ${e.last_name}`,
              email: e.email,
              employee_no: e.employee_no,
              department_id: e.department_id,
            }))
          );

          setJobs(
            jobRes.data.map((j: any) => ({
              id: j.id,
              title: j.job_title,
              code: j.job_code,
            }))
          );

          setDepartments(deptRes.data);
        } catch (err) {
          handleApiError(err, "Failed to fetch data. Please try again later.");
        }
      };
      fetchOptions();
    }
  }, [open, token]);

  // Validate the current step before proceeding
  const validateStep = (): boolean => {
    if (activeStep === 0) {
      const newKpis = kpis.map((kpi) => {
        const errors: any = {};
        if (!kpi.what.trim()) errors.what = "KPI description is required";
        if (kpi.total_marks <= 0)
          errors.total_marks = "Marks must be positive";
        if (kpi.hows.some((h) => !h.trim()))
          errors.hows = kpi.hows.map((h) => (!h.trim() ? "Metric is required" : ""));
        return { ...kpi, errors };
      });
      setKpis(newKpis);
      return newKpis.every((k) => !k.errors || Object.keys(k.errors).length === 0);
    }
    if (activeStep === 1) {
      if (assignment.ids.length === 0) {
        setAssignmentError("Please select at least one target");
        return false;
      }
      setAssignmentError("");
      return true;
    }
    return true;
  };

  // Handlers for adding KPIs and metrics
  const handleAddKpi = () => {
    setKpis([
      ...kpis,
      { what: "", total_marks: 0, hows: [""], errors: {} },
    ]);
  };

  const handleAddMetric = (kpiIndex: number) => {
    const newKpis = [...kpis];
    newKpis[kpiIndex].hows.push("");
    setKpis(newKpis);
  };

  const handleKpiChange = (
    index: number,
    field: "what" | "total_marks",
    value: any
  ) => {
    setKpis(prevKpis => 
      prevKpis.map((kpi, i) => {
        if (i !== index) return kpi;
        
        // Create a new errors object without the field
        const updatedErrors = kpi.errors 
          ? { ...kpi.errors } 
          : undefined;
        
        if (updatedErrors) {
          delete updatedErrors[field as keyof typeof updatedErrors];
        }
        
        return {
          ...kpi,
          [field]: value,
          errors: updatedErrors
        };
      })
    );
  };

  const handleMetricChange = (
    kpiIndex: number,
    metricIndex: number,
    value: string
  ) => {
    const newKpis = [...kpis];
    newKpis[kpiIndex].hows[metricIndex] = value;
    if (newKpis[kpiIndex].errors?.hows) {
      newKpis[kpiIndex].errors!.hows[metricIndex] = "";
    }
    setKpis(newKpis);
  };

  // Submit handler: posts the KPI whats and hows then assigns them
  const handleSubmit = async () => {
    if (!validateStep()) return;
  
    try {
      setLoading(true);
  
      // Use only the kpis state which already contains both manual and bulk uploaded KPIs
      const validKpis = kpis
      .filter(kpi => kpi.what.trim() && kpi.hows.some(h => h.trim()))
      .map(kpi => ({
        what: kpi.what,
        total_marks: kpi.total_marks,
        hows: kpi.hows.filter(Boolean) as string[]
      }));

    if (validKpis.length === 0) {
      throw new Error("No valid KPIs to submit");
    }
  
      // Create the KPIs (whats) and their metrics (hows)
      const createdKpis = await Promise.all(
        validKpis.map(async (kpi) => {
          const whatRes = await axios.post(
            `${BASE_URL}/hrm/kpi-whats`,
            {
              Kpi_type: "default",
              what: kpi.what,
              total_marks: kpi.total_marks,
              created_by: user?.employee_no,
            },
            { headers: { Authorization: `Token ${token}` } }
          );
      
          const hows = await Promise.all(
            kpi.hows.map(async (how) => {
              const res = await axios.post(
                `${BASE_URL}/hrm/kpi-hows`,
                { how, whats: whatRes.data.id, created_by: user?.employee_no },
                { headers: { Authorization: `Token ${token}` } }
              );
              return res.data;
            })
          );
          return { what: whatRes.data, hows };
        })
      );
  
      // Assign the created KPIs to employees or job positions
      const assignmentPromises = createdKpis.map((createdKpi) => {
        const kpiId = createdKpi.what.id;
        if (assignment.type === "employee") {
          return Promise.all(
            assignment.ids.map((employeeId) =>
              axios.post(
                `${BASE_URL}/hrm/kpi-to-employee`,
                { employeeid: employeeId, kpi: kpiId },
                { headers: { Authorization: `Token ${token}` } }
              )
            )
          );
        } else {
          return Promise.all(
            assignment.ids.map((jobId) => {
              const job = jobs.find((j) => String(j.id) === String(jobId));
              if (!job) {
                throw new Error(`Job with ID ${jobId} not found.`);
              }
              return axios.post(
                `${BASE_URL}/hrm/kpi-to-jobpositions`,
                { job_code: job.code, kpi: kpiId },
                { headers: { Authorization: `Token ${token}` } }
              );
            })
          );
        }
      });
  
      await Promise.all(assignmentPromises);
  
      // Show success message
      setSnackbar({
        open: true,
        message: "🎉 KPIs created and assigned successfully!",
        severity: "success",
      });
  
      // Close the modal if embedded
      onClose();
    } catch (err) {
      handleApiError(
        err,
        "Failed to save KPIs. Please check your network connection and try again."
      );
    } finally {
      setLoading(false);
    }
  };
  
  // Generic API error handler
  const handleApiError = (error: unknown, defaultMessage: string) => {
    let message = defaultMessage;
    if (axios.isAxiosError(error)) {
      message = error.response?.data?.detail || error.message;
      if (error.response?.data) {
        message = Object.entries(error.response.data)
          .flatMap(([key, val]) =>
            Array.isArray(val) ? val : [`${key}: ${val}`]
          )
          .join("\n");
      }
    }
    setSnackbar({ open: true, message: `🚨 ${message}`, severity: "error" });
  };

  // A simple close handler (you could extend to block backdrop clicks if needed)
  const handleClose = () => {
    onClose();
  };

  return (
    <>
      {open && (
        <Dialog open={open} onOpenChange={handleClose}>
          <DialogContent className="max-w-4xl w-full p-6 rounded-3xl bg-background overflow-auto max-h-[80vh]">
            {/* Header */}
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center gap-4">
                <ClipboardList className="h-10 w-10 text-primary" />
                <div>
                  <h2 className="text-2xl font-semibold">
                    KPI Management Section
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Create measurable focus areas and assign to teams
                  </p>
                </div>
              </div>
              <button onClick={onClose} className="p-2">
                {/* <X className="h-6 w-6" /> */}
              </button>
            </div>

            {/* Stepper */}
            <div className="mt-4 flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full ${
                      activeStep >= index
                        ? "bg-primary text-white"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {step.icon}
                  </div>
                  <span className="mt-2 text-sm font-semibold">
                    {step.label}
                  </span>
                </div>
              ))}
            </div>

            {/* Step Content */}
            <div className="mt-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeStep}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {activeStep === 0 && (
                    <div>
                      <Tabs defaultValue="manual">
                        <TabsList className="grid grid-cols-2 w-full">
                          <TabsTrigger value="manual">Manual Entry</TabsTrigger>
                          <TabsTrigger value="bulk">Bulk Upload</TabsTrigger>
                        </TabsList>
                      <TabsContent value="manual">
                      <Button
                        onClick={handleAddKpi}
                        className="mb-3 bg-gradient-to-r from-primary to-secondary"
                      >
                        <Plus className="mr-2 h-4 w-4" /> Add KPI (WHATS)
                      </Button>
                      {kpis.map((kpi, kpiIndex) => (
                        <div
                          key={kpiIndex}
                          className="p-4 mb-3 rounded-2xl bg-card shadow hover:shadow-lg transition"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="bg-primary text-white rounded px-2 py-1 text-xs font-semibold">
                              KPI {kpiIndex + 1}
                            </span>
                            <button
                              onClick={() =>
                                setKpis(kpis.filter((_, i) => i !== kpiIndex))
                              }
                              className="text-destructive"
                            >
                              <X className="h-5 w-5" />
                            </button>
                          </div>

                          <div className="mb-2">
                            <Label htmlFor={`kpi-what-${kpiIndex}`}>
                              KPI (Whats) Description *
                            </Label>
                            <textarea
                              id={`kpi-what-${kpiIndex}`}
                              value={kpi.what}
                              onChange={(e) =>
                                handleKpiChange(
                                  kpiIndex,
                                  "what",
                                  e.target.value
                                )
                              }
                              rows={3}
                              className={`mt-1 block w-full rounded border p-2 ${
                                kpi.errors?.what
                                  ? "border-destructive"
                                  : "border-gray-300"
                              }`}
                              placeholder="Clear, measurable objective"
                            />
                            {kpi.errors?.what && (
                              <p className="mt-1 text-xs text-destructive">
                                {kpi.errors.what}
                              </p>
                            )}
                          </div>

                          <div className="mb-3">
                            <Label htmlFor={`kpi-marks-${kpiIndex}`}>
                              Total Marks *
                            </Label>
                            <Input
                              id={`kpi-marks-${kpiIndex}`}
                              type="number"
                              value={kpi.total_marks || ""}
                              onChange={(e) =>
                                handleKpiChange(
                                  kpiIndex,
                                  "total_marks",
                                  Number(e.target.value)
                                )
                              }
                              className={`mt-1 w-48 ${
                                kpi.errors?.total_marks ? "border-destructive" : ""
                              }`}
                              placeholder="Value between 1-100"
                              min={1}
                              max={100}
                            />
                            {kpi.errors?.total_marks && (
                              <p className="mt-1 text-xs text-destructive">
                                {kpi.errors.total_marks}
                              </p>
                            )}
                          </div>

                          <div className="mb-2">
                            <div className="flex items-center justify-between">
                              <h3 className="text-lg font-semibold">
                                Performance Metrics (Hows)
                              </h3>
                              <Button
                                onClick={() => handleAddMetric(kpiIndex)}
                                className="bg-gradient-to-r from-primary to-secondary"
                                size="sm"
                              >
                                <Plus className="mr-1 h-4 w-4" /> Add Metric (Hows)
                              </Button>
                            </div>
                            {kpi.hows.map((how, howIndex) => (
                              <div
                                key={howIndex}
                                className="flex items-center gap-2 p-1 mt-2 bg-muted rounded"
                              >
                                <ArrowRight
                                  className={`h-5 w-5 ${
                                    kpi.errors?.hows &&
                                    kpi.errors.hows[howIndex]
                                      ? "text-destructive"
                                      : "text-muted-foreground"
                                  }`}
                                />
                                <Input
                                  value={how}
                                  onChange={(e) =>
                                    handleMetricChange(
                                      kpiIndex,
                                      howIndex,
                                      e.target.value
                                    )
                                  }
                                  placeholder={`Hows ${howIndex + 1} *`}
                                  className={`flex-1 ${
                                    kpi.errors?.hows &&
                                    kpi.errors.hows[howIndex]
                                      ? "border-destructive"
                                      : ""
                                  }`}
                                />
                                {howIndex > 0 && (
                                  <button
                                    onClick={() => {
                                      const newKpis = [...kpis];
                                      newKpis[kpiIndex].hows = newKpis[
                                        kpiIndex
                                      ].hows.filter((_, i) => i !== howIndex);
                                      setKpis(newKpis);
                                    }}
                                    className="text-destructive"
                                  >
                                    <X className="h-5 w-5" />
                                  </button>
                                )}
                              </div>
                            ))}
                            {kpi.errors?.hows &&
                              typeof kpi.errors.hows === "string" && (
                                <p className="mt-1 text-xs text-destructive">
                                  {kpi.errors.hows}
                                </p>
                              )}
                          </div>
                        </div>
                      ))}
                    </TabsContent>
                    {/* Bulk Upload Tab */}
                    <TabsContent value="bulk">
                        <KpiBulkUpload 
                          employees={employees}
                          jobs={jobs}
                          onSuccess={(newKpis, assignment) => {
                            // Completely replace existing KPIs with the uploaded ones
                            setKpis(newKpis.map(kpi => ({
                              what: kpi.what,
                              total_marks: kpi.total_marks,
                              hows: kpi.hows.filter(Boolean) as string[],
                              errors: {}
                            })));
                            setAssignment(assignment);
                            setActiveStep(2);
                          }}
                          embedded={true}
                          open={true} 
                          onClose={() => {}}
                        />
                      </TabsContent>
                    </Tabs>
                    </div>
                  )}
                  {activeStep === 1 && (
                    <div>
                      <div className="mb-4">
                        <Label>Assignment Type</Label>
                        <Select
                          value={assignment.type}
                          onValueChange={(val: "employee" | "job") => {
                            setAssignment({
                              type: val,
                              ids: [],
                            });
                            setSearchTerm("");
                            setDepartmentFilter("all");
                          }}
                        >
                          <SelectTrigger className="w-full mt-1">
                            <SelectValue placeholder="Select assignment type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="employee">Employees</SelectItem>
                            <SelectItem value="job">Job Positions</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Search and Filter Section */}
                      <div className="space-y-3 mb-4">
                        <div className="flex gap-2">
                          <div className="relative flex-1">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                              type="text"
                              placeholder={`Search ${assignment.type === "employee" ? "employees by name, employee number, or department" : "job positions by title or code"}`}
                              className="pl-10 pr-10 h-10"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                            />
                            {searchTerm && (
                              <button
                                type="button"
                                onClick={() => setSearchTerm("")}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                          {/* Department Filter */}
                          {assignment.type === "employee" && departments.length > 0 && (
                            <Select
                              onValueChange={val => setDepartmentFilter(val)}
                              defaultValue="all"
                            >
                              <SelectTrigger className="w-[200px]">
                                <SelectValue placeholder="Filter by Department" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All Departments</SelectItem>
                                {departments
                                  .filter(dept => dept.department_status_active)
                                  .sort((a, b) => a.name.localeCompare(b.name))
                                  .map(dept =>(
                                    <SelectItem key={dept.id} value={dept.id.toString()}>
                                      {dept.name}
                                    </SelectItem>
                                  )
                                  )}
                              </SelectContent>
                            </Select>
                          )}
                        </div>

                        {/* Search Results Counter */}
                        {(searchTerm || departmentFilter !== "all") && (
                          <div className="text-xs text-muted-foreground">
                            {(() => {
                              const filteredCount = assignment.type === "employee" ?
                                employees.filter(emp => {
                                  const departmentMatches = departmentFilter === "all" ||
                                    emp.department_id.toString() === departmentFilter;
                                  const searchMatches = searchEmployees(emp, searchTerm);
                                  return departmentMatches && searchMatches;
                                }).length :
                                jobs.filter(job => searchJobs(job, searchTerm)).length;

                              const totalCount = assignment.type === "employee" ? employees.length : jobs.length;

                              return `Showing ${filteredCount} of ${totalCount} ${assignment.type === "employee" ? "employees" : "job positions"}`;
                            })()}
                          </div>
                        )}
                      </div>

                      {/* Target Selection */}
                      <div className="mb-3">
                        <div className="flex items-center justify-between mb-2">
                          <Label>
                            Select {assignment.type === "employee" ? "Employees" : "Job Positions"}
                          </Label>
                          <div className="flex gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const filteredItems = assignment.type === "employee" ?
                                  employees
                                    .filter(emp => {
                                      const departmentMatches = departmentFilter === "all" ||
                                        emp.department_id.toString() === departmentFilter;
                                      const searchMatches = searchEmployees(emp, searchTerm);
                                      return departmentMatches && searchMatches;
                                    })
                                    .map(emp => emp.id.toString()) :
                                  jobs
                                    .filter(job => searchJobs(job, searchTerm))
                                    .map(job => job.id.toString());
                                setAssignment(prev => ({ ...prev, ids: filteredItems }));
                              }}
                            >
                              Select All Filtered
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setAssignment(prev => ({ ...prev, ids: [] }))}
                            >
                              Clear All
                            </Button>
                          </div>
                        </div>
                        <select
                          multiple
                          value={assignment.ids.map(String)}
                          onChange={(e: ChangeEvent<HTMLSelectElement>) => {
                            const selected = Array.from(
                              e.target.selectedOptions
                            ).map((opt) => opt.value);
                            setAssignment((prev) => ({ ...prev, ids: selected }));
                          }}
                          className={`mt-1 block w-full h-60 rounded border border-gray-300 p-2 bg-background ${
                            assignmentError ? "border-destructive" : ""
                          }`}
                        >
                          {(() => {
                            const filteredItems = assignment.type === "employee" ?
                              employees
                                .filter(emp => {
                                  const departmentMatches = departmentFilter === "all" ||
                                    emp.department_id.toString() === departmentFilter;
                                  const searchMatches = searchEmployees(emp, searchTerm);
                                  return departmentMatches && searchMatches;
                                })
                                .sort((a, b) => a.name.localeCompare(b.name))
                                .map((option) => {
                                  const deptName = getDepartmentName(option.department_id);
                                  return (
                                    <option key={option.id} value={option.id}>
                                      {`${option.name} - ${deptName} (${option.employee_no})`}
                                    </option>
                                  );
                                }) :
                              jobs
                                .filter(job => searchJobs(job, searchTerm))
                                .sort((a, b) => a.title.localeCompare(b.title))
                                .map((option) => (
                                  <option key={option.id} value={option.id}>
                                    {`${option.title} (${option.code})`}
                                  </option>
                                ));

                            if (filteredItems.length === 0) {
                              return (
                                <option disabled className="text-muted-foreground italic">
                                  No {assignment.type === "employee" ? "employees" : "job positions"} found matching your search
                                </option>
                              );
                            }

                            return filteredItems;
                          })()}
                        </select>
                        <div className="flex items-center justify-between mt-2">
                          <p className="text-xs text-muted-foreground">
                            {assignment.ids.length} {assignment.type === "employee" ? "employees" : "job positions"} selected
                            {assignment.type === "employee" && (
                              <span className="ml-2 text-blue-600">
                                • Hold Ctrl/Cmd to select multiple
                              </span>
                            )}
                          </p>
                          {assignment.ids.length > 0 && (
                            <p className="text-xs text-green-600 font-medium">
                              ✓ Ready to assign KPIs
                            </p>
                          )}
                        </div>
                        {assignmentError && (
                          <p className="mt-1 text-xs text-destructive">
                            {assignmentError}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {activeStep === 2 && (
                    <div>
                      <h3 className="text-xl font-semibold mb-3">
                        📋 Review Summary
                      </h3>
                      {kpis.map((kpi, index) => (
                        <div
                          key={index}
                          className="p-4 mb-4 rounded bg-card shadow"
                        >
                          <div className="flex items-center gap-2 mb-3">
                            <span className="bg-primary text-white rounded px-2 py-1 text-xs font-semibold">
                              KPI (Whats) {index + 1}
                            </span>
                            <span className="bg-secondary text-white rounded px-2 py-1 text-xs">
                              {kpi.total_marks} Marks
                            </span>
                            <p className="ml-auto text-sm text-muted-foreground">
                              {kpi.hows.length} Metrics Defined
                            </p>
                          </div>

                          <div className="mb-4">
                            <h4 className="text-lg text-primary font-semibold">
                              {kpi.what}
                            </h4>

                            <div className="mt-2">
                              <Label className="mb-1">
                                Performance Metrics (Hows)
                              </Label>
                              <select
                                multiple
                                className="mt-1 block w-full rounded border border-gray-300 p-2 bg-muted"
                              >
                                {kpi.hows.map((how, i) => (
                                  <option key={i} value={how}>
                                    {how || `Metric ${i + 1}`}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </div>

                          <hr className="mb-3" />

                          <div>
                            <Label className="mb-1">✨ Assigned To:</Label>
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 mt-1 max-h-96 overflow-y-auto pr-1">
                              {assignment.type === "employee"
                                ? employees
                                    .filter((e) =>
                                      assignment.ids.includes(String(e.id))
                                    )
                                    .map((emp) => (
                                      <div
                                        key={emp.id}
                                        className="p-3 bg-muted rounded border border-gray-200 hover:shadow hover:border-primary hover:bg-primary/10 transition"
                                      >
                                        <div className="flex items-start gap-3">
                                          <div className="w-2 h-2 rounded-full bg-green-500 mt-1 flex-shrink-0"></div>
                                          <div className="flex-1 min-w-0">
                                            <div className="flex items-center gap-1 mb-0.5">
                                              <p className="text-sm font-semibold text-primary whitespace-nowrap overflow-hidden text-ellipsis">
                                                {emp.name}
                                              </p>
                                              <span className="bg-primary/10 text-primary rounded px-2 py-0.5 text-xs font-medium">
                                                #{emp.employee_no}
                                              </span>
                                            </div>
                                            <p className="text-xs text-muted-foreground whitespace-nowrap overflow-hidden text-ellipsis">
                                              {emp.email}
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                    ))
                                : jobs
                                    .filter((j) =>
                                      assignment.ids.includes(String(j.id))
                                    )
                                    .map((job) => (
                                      <div
                                        key={job.id}
                                        className="p-4 bg-muted rounded border border-gray-200 hover:shadow transition"
                                      >
                                        <div className="flex items-center gap-2">
                                          <div className="bg-secondary rounded-full w-10 h-10 flex items-center justify-center">
                                            <Briefcase className="h-6 w-6 text-white" />
                                          </div>
                                          <div className="flex-1 overflow-hidden">
                                            <h5 className="text-sm font-semibold whitespace-nowrap overflow-hidden text-ellipsis">
                                              {job.title}
                                            </h5>
                                            <p className="text-xs text-muted-foreground">
                                              Code: {job.code}
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Action Buttons */}
            <div className="mt-4 flex items-center justify-between bg-muted p-4 rounded">
              <Button
                onClick={() =>
                  activeStep > 0 ? setActiveStep(activeStep - 1) : onClose()
                }
                disabled={loading}
                variant="outline"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />{" "}
                {activeStep === 0 ? "Cancel" : "Back"}
              </Button>
              <Button
                onClick={() => {
                  if (activeStep < steps.length - 1) {
                    if (!validateStep()) return;
                    setActiveStep(activeStep + 1);
                  } else {
                    handleSubmit();
                  }
                }}
                disabled={loading || (activeStep === 0 && kpis.length === 0)}
                className="px-8 font-semibold bg-gradient-to-r from-primary to-secondary hover:opacity-90"
              >
                {loading ? (
                  <Loader2 className="animate-spin h-5 w-5" />
                ) : activeStep < steps.length - 1 ? (
                  <>
                    Next Step <ArrowDown className="ml-2 h-5 w-5" />
                  </>
                ) : (
                  <>
                    Finalize Creation <Save className="ml-2 h-5 w-5" />
                  </>
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Snackbar / Toast */}
      {snackbar.open && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className="p-4 rounded shadow bg-green-500 text-white flex items-center gap-2">
            {snackbar.severity === "error" && (
              <AlertTriangle className="h-5 w-5" />
            )}
            <p className="whitespace-pre-line">{snackbar.message}</p>
            <button
              onClick={() =>
                setSnackbar((prev) => ({ ...prev, open: false }))
              }
              className="ml-4"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default KpiWizardModal;
