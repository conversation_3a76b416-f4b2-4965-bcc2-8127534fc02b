"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useNavigate } from "react-router-dom";
import { BASE_URL } from "@/config";
import { useDebounce } from "@/hooks/use-debounce";
import { useTheme } from "@/hooks/use-theme";

// Icons
import {
  Search,
  Calendar as CalendarIcon,
  Filter,
  X,
  Download,
  RefreshCw,
  User,
  FileText,
  Clock,
  AlertTriangle,
  CheckCircle,
  UserCheck
} from "lucide-react";

// Shadcn/UI components
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";

interface EmployeeBio {
  id: number;
  first_name: string;
  middle_name: string | null;
  last_name: string;
  employee_no: string;
}

interface EmployeeContractInfo {
  id: number;
  employee_no: string;
  contract_type: string;
  contract_start_date: string;
  current_contract_end: string | null;
  end_of_probation_date: string | null;
  on_pip: boolean;
}

interface ProbationReportData {
  employee_no: string;
  full_name: string;
  contract_start_date: string;
  end_of_probation_date: string | null;
  current_contract_end: string | null;
  on_pip: boolean;
  probation_status: "active" | "expiring_soon" | "completed" | "overdue";
  days_until_probation_end: number | null;
  is_current_employee: boolean;
}

interface ProbationReportsFilters {
  search?: string;
  probation_status?: string;
  on_pip?: string;
  end_of_probation_date?: string;
  ordering?: string;
}

const PROBATION_STATUS_OPTIONS = [
  { value: "active", label: "Active Probation" },
  { value: "expiring_soon", label: "Expiring Soon (30 days)" },
  { value: "completed", label: "Probation Completed" },
  { value: "overdue", label: "Overdue" }
];

// Badge variants for filter pills
const BADGE_VARIANTS = [
  "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100",
  "bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100",
  "bg-yellow-100 text-yellow-900 dark:bg-yellow-900 dark:text-yellow-100",
  "bg-red-100 text-red-900 dark:bg-red-900 dark:text-red-100",
  "bg-purple-100 text-purple-900 dark:bg-purple-900 dark:text-purple-100",
];

// Helper function to calculate probation status
const getProbationStatus = (
  probationEndDate: string | null, 
  contractEndDate: string | null
): { status: "active" | "expiring_soon" | "completed" | "overdue", daysUntilEnd: number | null } => {
  if (!probationEndDate) return { status: "active", daysUntilEnd: null };
  
  const today = new Date();
  const probationEnd = new Date(probationEndDate);
  const diffTime = probationEnd.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    // Check if contract is still active
    if (contractEndDate) {
      const contractEnd = new Date(contractEndDate);
      if (contractEnd > today) {
        return { status: "completed", daysUntilEnd: diffDays };
      }
    }
    return { status: "overdue", daysUntilEnd: diffDays };
  } else if (diffDays <= 30) {
    return { status: "expiring_soon", daysUntilEnd: diffDays };
  } else {
    return { status: "active", daysUntilEnd: diffDays };
  }
};

export default function ProbationReports() {
  const { token } = useSelector((s: RootState) => s.auth);
  const navigate = useNavigate();
  const { theme } = useTheme();

  // State
  const [filters, setFilters] = useState<ProbationReportsFilters>({});
  const [probationEndDate, setProbationEndDate] = useState<Date>();
  const [reportData, setReportData] = useState<ProbationReportData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);

  // Debounce the filters to prevent excessive API calls
  const debouncedFilters = useDebounce(filters, 500);

  // Fetch probation report data
  const fetchProbationReport = useCallback(async (filterParams: ProbationReportsFilters) => {
    if (!token) return setError("Missing token. Please log in.");
    setLoading(true);
    setError(null);
    try {
      // Fetch employee bio details and contract info in parallel
      const [bioRes, contractRes] = await Promise.all([
        fetch(`${BASE_URL}/users/employee-bio-details${filterParams.search ? `?search=${encodeURIComponent(filterParams.search)}` : ""}`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        }),
        fetch(`${BASE_URL}/users/employee-contract-info-details`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        }),
      ]);

      if (!bioRes.ok || !contractRes.ok) {
        throw new Error("Failed to fetch employee data");
      }

      const [bioData, contractData]: [EmployeeBio[], EmployeeContractInfo[]] = await Promise.all([
        bioRes.json(),
        contractRes.json(),
      ]);

      // Filter for probation contracts only and combine with bio data
      const probationContracts = contractData.filter(contract => contract.contract_type === "Probation");
      
      const combinedData: ProbationReportData[] = probationContracts
        .map(contract => {
          const bio = bioData.find(b => b.employee_no === contract.employee_no);
          if (!bio) return null;

          const { status, daysUntilEnd } = getProbationStatus(
            contract.end_of_probation_date, 
            contract.current_contract_end
          );

          // Determine if this is a current employee (contract hasn't ended)
          const isCurrentEmployee = !contract.current_contract_end || 
            new Date(contract.current_contract_end) > new Date();

          return {
            employee_no: bio.employee_no,
            full_name: `${bio.first_name}${bio.middle_name ? ` ${bio.middle_name}` : ""} ${bio.last_name}`,
            contract_start_date: contract.contract_start_date,
            end_of_probation_date: contract.end_of_probation_date,
            current_contract_end: contract.current_contract_end,
            on_pip: contract.on_pip,
            probation_status: status,
            days_until_probation_end: daysUntilEnd,
            is_current_employee: isCurrentEmployee,
          };
        })
        .filter(Boolean) as ProbationReportData[];

      // Apply filters
      let filteredData = combinedData;

      if (filterParams.probation_status && filterParams.probation_status !== "all") {
        filteredData = filteredData.filter(item => item.probation_status === filterParams.probation_status);
      }

      if (filterParams.on_pip && filterParams.on_pip !== "all") {
        const isPip = filterParams.on_pip === "true";
        filteredData = filteredData.filter(item => item.on_pip === isPip);
      }

      if (filterParams.end_of_probation_date) {
        filteredData = filteredData.filter(item => 
          item.end_of_probation_date && item.end_of_probation_date <= filterParams.end_of_probation_date!
        );
      }

      // Sort by probation end date (soonest first)
      filteredData.sort((a, b) => {
        if (!a.end_of_probation_date && !b.end_of_probation_date) return 0;
        if (!a.end_of_probation_date) return 1;
        if (!b.end_of_probation_date) return -1;
        return new Date(a.end_of_probation_date).getTime() - new Date(b.end_of_probation_date).getTime();
      });

      setReportData(filteredData);
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  }, [token]);

  // Auto-fetch when debounced filters change
  useEffect(() => {
    if (Object.keys(debouncedFilters).length > 0) {
      fetchProbationReport(debouncedFilters);
    }
  }, [debouncedFilters, fetchProbationReport]);

  // Filter management functions
  const setFilterValue = useCallback((k: keyof ProbationReportsFilters, v?: string) => {
    setFilters((prev) => ({ ...prev, [k]: v || "" }));
  }, []);

  const removeFilter = useCallback((k: keyof ProbationReportsFilters) => {
    setFilters((prev) => {
      const copy = { ...prev };
      delete copy[k];
      return copy;
    });
  }, []);

  // Handle date filter changes
  useEffect(() => {
    if (probationEndDate) {
      setFilterValue("end_of_probation_date", probationEndDate.toISOString().slice(0, 10));
    } else {
      removeFilter("end_of_probation_date");
    }
  }, [probationEndDate, setFilterValue, removeFilter]);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setFilters({});
    setReportData([]);
    setError(null);
    setProbationEndDate(undefined);
  }, []);

  // Export data to CSV (mock function)
  const handleExportData = useCallback(() => {
    if (reportData.length === 0) return;

    setIsExporting(true);
    setTimeout(() => {
      setIsExporting(false);
      // In a real implementation, you would generate and download a CSV here
      alert("Export functionality would download a CSV file in a real implementation");
    }, 1000);
  }, [reportData]);

  return (
    <Screen>
      {/* Header with breadcrumb and title */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <Breadcrumb className="mb-2">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/employees-list">Employees</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Probation Reports</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <h1 className="text-2xl font-bold tracking-tight">Probation Reports</h1>
          <p className="text-muted-foreground">Monitor employees on probation and their status</p>
        </div>

        <div className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                  className="h-9"
                >
                  <RefreshCw size={16} className="mr-1" />
                  Reset
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Clear all filters</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={handleExportData}
                  disabled={reportData.length === 0 || isExporting}
                  className="h-9"
                >
                  {isExporting ? (
                    <RefreshCw size={16} className="mr-1 animate-spin" />
                  ) : (
                    <Download size={16} className="mr-1" />
                  )}
                  Export
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export data to CSV</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            onClick={() => navigate("/employees-list")}
            className="h-9"
          >
            Back to Employees
          </Button>
        </div>
      </div>

      {/* Main content area */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters panel - 1 column on mobile, 1/4 on large screens */}
        <Card className="lg:col-span-1 h-fit">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Filters</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="h-8 px-2 text-muted-foreground"
              >
                <X size={16} className="mr-1" />
                Clear all
              </Button>
            </div>
            <CardDescription>
              Filters apply automatically
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-5">
            {/* Active filters */}
            {Object.entries(filters).filter(([_, v]) => !!v).length > 0 && (
              <div className="space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Active Filters</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(filters)
                    .filter(([_, v]) => !!v)
                    .map(([k, v], i) => (
                      <Badge
                        key={k}
                        className={`${BADGE_VARIANTS[i % BADGE_VARIANTS.length]} cursor-pointer transition-all hover:opacity-80`}
                        onClick={() => removeFilter(k as any)}
                      >
                        {k.replace(/_/g, ' ')}: {v}
                        <X size={12} className="ml-1" />
                      </Badge>
                    ))}
                </div>
              </div>
            )}

            {/* Search */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by employee name..."
                  value={filters.search || ""}
                  onChange={(e) => setFilterValue("search", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Probation Status filter */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Probation Status</Label>
              <Select
                value={filters.probation_status || "all"}
                onValueChange={(value) => setFilterValue("probation_status", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  {PROBATION_STATUS_OPTIONS.map((status) => (
                    <SelectItem key={status.value} value={status.value}>{status.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* PIP Status filter */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">PIP Status</Label>
              <Select
                value={filters.on_pip || "all"}
                onValueChange={(value) => setFilterValue("on_pip", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All PIP statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All PIP statuses</SelectItem>
                  <SelectItem value="true">On PIP</SelectItem>
                  <SelectItem value="false">Not on PIP</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date filter */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Probation End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={`w-full justify-start text-left font-normal ${!probationEndDate && "text-muted-foreground"}`}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {probationEndDate ? (
                      probationEndDate.toLocaleDateString()
                    ) : (
                      "Filter by probation end date"
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={probationEndDate}
                    onSelect={setProbationEndDate}
                    initialFocus
                  />
                  {probationEndDate && (
                    <div className="p-3 border-t border-border">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setProbationEndDate(undefined)}
                        className="w-full"
                      >
                        <X size={14} className="mr-1" />
                        Clear date
                      </Button>
                    </div>
                  )}
                </PopoverContent>
              </Popover>
            </div>
          </CardContent>
        </Card>

        {/* Results panel - 1 column on mobile, 3/4 on large screens */}
        <Card className="lg:col-span-3">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Probation Report</CardTitle>
              <Badge variant="outline" className="font-normal">
                {loading ? (
                  <span className="flex items-center">
                    <RefreshCw size={12} className="mr-1 animate-spin" /> Loading...
                  </span>
                ) : reportData.length > 0 ? (
                  `${reportData.length} probation records found`
                ) : (
                  "No results"
                )}
              </Badge>
            </div>
            <CardDescription>
              {Object.keys(filters).length > 0
                ? "Showing filtered results"
                : "Apply filters to see probation data"}
            </CardDescription>
          </CardHeader>

          <CardContent>
            {error && (
              <div className="mb-4 p-3 bg-destructive/10 text-destructive rounded-md">
                <p className="text-sm">{error}</p>
              </div>
            )}

            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="font-medium">Employee ID</TableHead>
                      <TableHead className="font-medium">Name</TableHead>
                      <TableHead className="font-medium">Contract Start</TableHead>
                      <TableHead className="font-medium">Probation End</TableHead>
                      <TableHead className="font-medium">Status</TableHead>
                      <TableHead className="font-medium">Days Left</TableHead>
                      <TableHead className="font-medium">PIP</TableHead>
                      <TableHead className="font-medium">Current Employee</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      // Loading skeleton
                      Array(5).fill(0).map((_, i) => (
                        <TableRow key={i}>
                          {Array(8).fill(0).map((_, j) => (
                            <TableCell key={j}>
                              <Skeleton className="h-4 w-full" />
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : reportData.length > 0 ? (
                      // Probation data
                      reportData.map((probation) => (
                        <TableRow key={probation.employee_no} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{probation.employee_no}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <User size={16} className="mr-2 text-muted-foreground" />
                              {probation.full_name}
                            </div>
                          </TableCell>
                          <TableCell>{probation.contract_start_date}</TableCell>
                          <TableCell>{probation.end_of_probation_date || "Not set"}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                probation.probation_status === "active" ? "default" :
                                probation.probation_status === "expiring_soon" ? "destructive" :
                                probation.probation_status === "completed" ? "outline" :
                                "secondary"
                              }
                              className="font-normal"
                            >
                              {probation.probation_status === "active" && <CheckCircle size={12} className="mr-1" />}
                              {probation.probation_status === "expiring_soon" && <AlertTriangle size={12} className="mr-1" />}
                              {probation.probation_status === "completed" && <UserCheck size={12} className="mr-1" />}
                              {probation.probation_status === "overdue" && <Clock size={12} className="mr-1" />}
                              {probation.probation_status.replace("_", " ")}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {probation.days_until_probation_end !== null ? (
                              <span className={
                                probation.days_until_probation_end < 0 ? "text-red-600" :
                                probation.days_until_probation_end <= 30 ? "text-yellow-600" : ""
                              }>
                                {probation.days_until_probation_end} days
                              </span>
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell>
                            {probation.on_pip ? (
                              <Badge variant="destructive" className="font-normal">
                                <AlertTriangle size={12} className="mr-1" />
                                On PIP
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="font-normal">
                                No
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {probation.is_current_employee ? (
                              <Badge variant="default" className="font-normal">
                                <CheckCircle size={12} className="mr-1" />
                                Current
                              </Badge>
                            ) : (
                              <Badge variant="secondary" className="font-normal">
                                Former
                              </Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      // No results
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <FileText size={24} className="mb-2" />
                            <p>No probation records found</p>
                            <p className="text-sm">Try adjusting your filters</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between border-t p-4">
            <p className="text-sm text-muted-foreground">
              {reportData.length > 0 && "Showing all available results"}
            </p>
          </CardFooter>
        </Card>
      </div>
    </Screen>
  );
}
