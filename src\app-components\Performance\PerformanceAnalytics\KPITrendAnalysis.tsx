import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, LineElement, PointElement, Title, Tooltip, Legend } from 'chart.js';
import { PerformanceData } from './PerformanceAnalytics';
import { AlertCircle } from 'lucide-react'; // Ensure this is imported

ChartJS.register(CategoryScale, LinearScale, LineElement, PointElement, Title, Tooltip, Legend);

interface KPITrendAnalysisProps {
  data: PerformanceData[];
}

const KPITrendAnalysis: React.FC<KPITrendAnalysisProps> = ({ data }) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  // Dummy Data for KPI Trend (to be replaced with actual API data)
  const generateDummyData = () => {
    const dummyData: PerformanceData[] = [];
    const departments = ['Sales', 'HR', 'IT', 'Finance'];
    for (let i = 0; i < 100; i++) {
      const department = departments[i % departments.length];
      dummyData.push({
        department_id: i % 5 + 1,
        employee_id: `EMP${i + 1}`,
        kpi_score: Math.floor(Math.random() * 101), // Random KPI score between 0-100
        appraisal_period: `2025-Q${(i % 4) + 1}`, // Random quarter
      });
    }
    return dummyData;
  };

  // Aggregating data for trend analysis
  const aggregateData = (data: PerformanceData[]) => {
    const kpiTrendData = data.reduce((acc, { appraisal_period, kpi_score }) => {
      if (!acc[appraisal_period]) {
        acc[appraisal_period] = [];
      }
      acc[appraisal_period].push(kpi_score);
      return acc;
    }, {} as Record<string, number[]>);

    const labels = Object.keys(kpiTrendData);
    const datasets = [
      {
        label: 'KPI Trend',
        data: labels.map((label) => {
          const scores = kpiTrendData[label];
          return scores.reduce((a, b) => a + b, 0) / scores.length;
        }),
        borderColor: 'rgba(75, 192, 192, 1)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        fill: true,
      },
    ];

    return { labels, datasets };
  };

  // Simulating data fetching (replace with real API call later)
  useEffect(() => {
    try {
      const dummyData = generateDummyData();
      const chartData = aggregateData(dummyData);
      setLoading(false); // Data loaded
    } catch (err) {
      setError('Failed to load KPI trend data');
      setLoading(false);
    }
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center">
        <div className="spinner-border animate-spin inline-block w-8 h-8 border-4 rounded-full border-t-green-600" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center text-red-500">
        <AlertCircle className="mr-2" />
        <p>{error}</p>
      </div>
    );
  }

  const chartData = aggregateData(data);

  return (
    <div className="bg-white shadow-md rounded-lg p-6">
      <h2 className="text-2xl font-semibold text-gray-700 mb-4">KPI Trend Analysis</h2>
      <div className="mb-6">
        <Line data={chartData} options={{
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            tooltip: {
              callbacks: {
                title: function(tooltipItem: any) {
                  return `Period: ${tooltipItem[0].label}`;
                },
                label: function(tooltipItem: any) {
                  return `Average KPI: ${tooltipItem.raw.toFixed(2)}`;
                },
              },
            },
          },
          scales: {
            x: {
              title: {
                display: true,
                text: 'Appraisal Period',
                color: '#4B5563',
                font: { size: 14, weight: 'bold' },
              },
            },
            y: {
              title: {
                display: true,
                text: 'KPI Score',
                color: '#4B5563',
                font: { size: 14, weight: 'bold' },
              },
              min: 0,
              max: 100,
              ticks: {
                stepSize: 20,
              },
            },
          },
        }} />
      </div>
    </div>
  );
};

export default KPITrendAnalysis;
