export interface EmployeeBioDetails {
  id?: number;
  employee_no: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  gender?: string;
  marital_status?: string;
  date_of_birth?: string;
  passport_number?: string;
  id_number?: string;
  date_of_first_appointment?: string;
}

export interface EmployeeContactInfo {
  id?: number;
  employee_no: string;
  postal_address?: string;
  home_phone_number?: string;
  alternate_phone_number?: string;   
  residential_address?: string;
  city?: string;
  county?: string;
  company_email?: string;
  country?: string;
  personal_email?: string;
  alternate_email?: string;         
  location_coordinates?: string;     
}


export interface EmployeeContractInfo {
  id?: number;
  employee_no: string;
  contract_type: string;
  contract_start_date: string;
  current_contract_end?: string | null;
  end_of_probation_date?: string | null;
  on_pip?: boolean;
}

export interface EmployeeImportantDates {
  id?: number;
  employee_no: string;
  date_of_current_appointment: string;
  date_of_leaveing?: string | null;
}

export interface JobInfo {
  id?: number;
  employee_no: string;
  category?: string;
  directorate?: string;
  job_title_code?: string;
  job_title?: string;
  teams_code?: number | null; // Changed to number as per API docs
  department?: number | null;
  work_location?: string;
  business_unit?: string;
}

export interface EmployeePaymentInfo {
  id?: number;
  employee_no: string;
  pension_scheme_join: string;
  salary: number;
  bonus: number;
  bank_name: string;
  account_number: string;
  payment_frequency: string;
  KRA_pin: string;
  tax_status: boolean;
  NHIF_SHIF_number: string;
  HELB_number: string;
  NSSF_number: string;
}

export interface EmployeeEducationalQualification {
  id?: number;
  employee_no: string;
  highear_qualificatins?: string; // typo, matches API field
  instution: string;
  year_of_graduation: string;
  category?: 'professional' | 'Certification';
}


export interface NextOfKin {
  name: string;
  relationship: string;
  contact_info: string;
}

export interface Dependant {
  name: string;
  relationship: string;
  contact_info: string;
}

export interface EmployeeNextOfKin {
  id?: number;
  employee_no: string;
  next_of_kin: NextOfKin[];
  dependants: Dependant[];
}

export interface EmployeeParentGuardian {
  id?: number;
  full_name: string;
  relationship: 'Father' | 'Mother' | 'Guardian' | 'Step-Father' | 'Step-Mother';
  date_of_birth?: string | null;
  occupation?: string | null;
  phone_number_or_email?: string | null;
  address?: string | null;
  is_alive: boolean;
  employee_no: string;
}

// For your big employee-creation flow, if needed
export interface EmployeeCreateData {
  userRegistration: {
    email: string;
    password: string;
    organisation_id: number | "";
    department_id: number | "";
    group_id: number | "";
  };
  bio: EmployeeBioDetails;
  contact: EmployeeContactInfo;
  contractInfo: EmployeeContractInfo;
  importantDates: EmployeeImportantDates;
  jobInfo: JobInfo;
  paymentInfo: EmployeePaymentInfo;
  educationalQualifications: EmployeeEducationalQualification[];
  nextOfKin: EmployeeNextOfKin;
}
