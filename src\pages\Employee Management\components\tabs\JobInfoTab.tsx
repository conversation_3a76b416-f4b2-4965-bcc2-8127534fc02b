import { useEffect, useState } from "react";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { JobInfo } from "../../types/EmployeeTypes";
import { updateOrCreateJobInfoRecord, fetchWorkLocations, fetchOrganizationGroups } from "../../utils/EmployeeRecords";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown, Briefcase } from "lucide-react";
import { cn } from "@/lib/utils";
import { TabLayout, FormGrid, FormField } from "../TabLayout";

import { BASE_URL } from "@/config";

interface JobInfoTabProps {
  employeeNo: string;
  onSaveSuccess?: () => void;
}

interface JobPosition {
  id: number;
  job_code: string;
  job_title: string;
}

interface Department {
  id: number;
  name: string;
  description: string;
}

interface WorkLocation {
  id: number;
  work_location: string;
  work_location_description?: string;
  work_location_status_active?: boolean;
  organisation?: number;
  work_location_head?: number;
  work_location_assistant?: number;
}

interface OrganizationGroup {
  id: number;
  name: string;
  description?: string;
  group_head?: string;
  group_status_active?: boolean;
}

export default function JobInfoTab({ employeeNo, onSaveSuccess }: JobInfoTabProps) {
  const token = useSelector((state: RootState) => state.auth.token);
  const [jobInfo, setJobInfo] = useState<JobInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [jobPositions, setJobPositions] = useState<JobPosition[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [workLocations, setWorkLocations] = useState<WorkLocation[]>([]);
  const [organizationGroups, setOrganizationGroups] = useState<OrganizationGroup[]>([]);
  const [jobPositionsLoading, setJobPositionsLoading] = useState(false);
  const [departmentsLoading, setDepartmentsLoading] = useState(false);
  const [workLocationsLoading, setWorkLocationsLoading] = useState(false);
  const [organizationGroupsLoading, setOrganizationGroupsLoading] = useState(false);
  const [openJobSelect, setOpenJobSelect] = useState(false);
  const [openDeptSelect, setOpenDeptSelect] = useState(false);
  const [openWorkLocationSelect, setOpenWorkLocationSelect] = useState(false);
  const [openTeamSelect, setOpenTeamSelect] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Fetch job info for the employee
  useEffect(() => {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }

    fetchJobInfo();
  }, [employeeNo, token]);

  // Fetch job positions
  useEffect(() => {
    if (!token) return;

    async function fetchJobPositions() {
      setJobPositionsLoading(true);
      try {
        const headers = { Authorization: `Token ${token}` };
        const res = await axios.get(`${BASE_URL}/hrm/job-positions`, { headers });
        setJobPositions(res.data);
      } catch (err) {
        console.error("Error fetching job positions:", err);
        toast.error("Failed to fetch job positions");
      } finally {
        setJobPositionsLoading(false);
      }
    }

    fetchJobPositions();
  }, [token]);

  // Fetch departments
  useEffect(() => {
    if (!token) return;

    async function fetchDepartments() {
      setDepartmentsLoading(true);
      try {
        const headers = { Authorization: `Token ${token}` };
        const res = await axios.get(`${BASE_URL}/users/departments`, { headers });
        setDepartments(res.data);
      } catch (err) {
        console.error("Error fetching departments:", err);
        toast.error("Failed to fetch departments");
      } finally {
        setDepartmentsLoading(false);
      }
    }

    fetchDepartments();
  }, [token]);

  // Fetch work locations
  useEffect(() => {
    if (!token) return;

    async function fetchWorkLocationsList() {
      setWorkLocationsLoading(true);
      try {
        const headers = { Authorization: `Token ${token}` };
        console.log("🔄 Fetching work locations from:", `${BASE_URL}/users/organization_work_locations`);
        const res = await axios.get(`${BASE_URL}/users/organization_work_locations`, { headers });
        console.log("✅ Work Locations API Response:", {
          status: res.status,
          dataType: Array.isArray(res.data) ? 'array' : typeof res.data,
          count: Array.isArray(res.data) ? res.data.length : 'N/A',
          data: res.data
        });

        if (Array.isArray(res.data) && res.data.length > 0) {
          console.log("📍 First work location structure:", res.data[0]);
          console.log("📍 Available fields in work location:", Object.keys(res.data[0]));
        }

        setWorkLocations(res.data);
      } catch (err) {
        console.error("❌ Error fetching work locations:", {
          message: err instanceof Error ? err.message : 'Unknown error',
          status: (err as any)?.response?.status,
          statusText: (err as any)?.response?.statusText,
          data: (err as any)?.response?.data,
          fullError: err
        });
        toast.error("Failed to fetch work locations");
      } finally {
        setWorkLocationsLoading(false);
      }
    }

    fetchWorkLocationsList();
  }, [token]);

  // Fetch organization groups
  useEffect(() => {
    if (!token) return;

    async function fetchOrganizationGroupsList() {
      setOrganizationGroupsLoading(true);
      try {
        const headers = { Authorization: `Token ${token}` };
        const res = await axios.get(`${BASE_URL}/users/organization_groups`, { headers });
        setOrganizationGroups(res.data);
      } catch (err) {
        console.error("Error fetching organization groups:", err);
        toast.error("Failed to fetch organization groups");
      } finally {
        setOrganizationGroupsLoading(false);
      }
    }

    fetchOrganizationGroupsList();
  }, [token]);

  // Function to fetch job info data
  async function fetchJobInfo() {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }

    setLoading(true);
    try {
      const headers = { Authorization: `Token ${token}` };
      const params = { employee_no: employeeNo };
      const res = await axios.get(`${BASE_URL}/users/employee-job-info-details`, { headers, params });

      if (Array.isArray(res.data) && res.data.length > 0) {
        // Find all records for this employee
        const records = res.data.filter(
          (item: JobInfo) => item.employee_no === employeeNo
        );

        if (records.length > 0) {
          // If we have multiple records, use the one with the highest ID (most recent)
          // or the one that matches our current ID if we have one
          let record = records[0];

          if (jobInfo && jobInfo.id) {
            // If we already have a record with an ID, try to find it in the returned records
            const existingRecord = records.find(r => r.id === jobInfo.id);
            if (existingRecord) {
              record = existingRecord;
            } else {
              // If we can't find our current ID, use the one with the highest ID
              record = records.reduce((prev, current) =>
                (prev.id > current.id) ? prev : current
              );
            }
          } else {
            // If we don't have a current ID, use the one with the highest ID
            record = records.reduce((prev, current) =>
              (prev.id > current.id) ? prev : current
            );
          }

          setJobInfo(record);
          return true;
        } else {
          setJobInfo({ employee_no: employeeNo } as JobInfo);
        }
      } else {
        setJobInfo({ employee_no: employeeNo } as JobInfo);
      }
    } catch (err) {
      console.error("Error fetching Job Info:", err);
      toast.error("Failed to fetch Job Info");
      setJobInfo({ employee_no: employeeNo } as JobInfo);
    } finally {
      setLoading(false);
    }
    return false;
  }

  async function handleSave() {
    if (!jobInfo || !token || !employeeNo) {
      toast.error("Missing required information");
      return;
    }

    setIsSaving(true);

    try {
      // Make sure employee_no is properly set and we're sending the correct ID
      const dataToSave: JobInfo = {
        ...jobInfo,
        employee_no: employeeNo,
        id: jobInfo.id, // Make sure we're keeping the existing ID if we have one
        // Ensure department is properly formatted (API might expect a number)
        department: jobInfo.department ? Number(jobInfo.department) : null,
        // Ensure teams_code is properly formatted (API expects a number)
        teams_code: jobInfo.teams_code ? Number(jobInfo.teams_code) : null
      };

      const updated = await updateOrCreateJobInfoRecord<JobInfo>(
        `${BASE_URL}/users/employee-job-info-details`,
        token,
        dataToSave
      );

      if (updated) {
        setJobInfo(updated);
        toast.success("Job information saved successfully");

        // Refresh the data to ensure we have the latest from the server
        setTimeout(() => {
          fetchJobInfo();
        }, 1000);

        // Call the onSaveSuccess callback if provided
        if (onSaveSuccess) {
          onSaveSuccess();
        }
      } else {
        toast.error("Failed to update job information");
      }
    } catch (err) {
      console.error("Error in handleSave:", err);
      toast.error("Failed to save Job Info");
    } finally {
      setIsSaving(false);
    }
  }

  function handleJobPositionSelection(jobPositionId: string) {
    const selectedJob = jobPositions.find(job => job.id.toString() === jobPositionId);
    if (selectedJob) {
      setJobInfo(prev => prev && {
        ...prev,
        job_title_code: selectedJob.job_code,
        job_title: selectedJob.job_title
      });
    }
    setOpenJobSelect(false);
  }

  function handleDepartmentSelection(departmentId: string) {
    const selectedDept = departments.find(dept => dept.id.toString() === departmentId);
    if (selectedDept) {
      setJobInfo(prev => prev && {
        ...prev,
        department: Number(selectedDept.id) // Ensure it's stored as a number
      });
    }
    setOpenDeptSelect(false);
  }

  function handleWorkLocationSelection(workLocationId: string) {
    const selectedLocation = workLocations.find(loc => loc.id.toString() === workLocationId);
    if (selectedLocation) {
      setJobInfo(prev => prev && {
        ...prev,
        work_location: selectedLocation.work_location
      });
    }
    setOpenWorkLocationSelect(false);
  }

  function handleTeamSelection(teamId: string) {
    const selectedTeam = organizationGroups.find(team => team.id.toString() === teamId);
    if (selectedTeam) {
      setJobInfo(prev => prev && {
        ...prev,
        teams_code: selectedTeam.id // Store the ID as a number
      });
    }
    setOpenTeamSelect(false);
  }

  if (loading) {
    return <div className="p-4">Loading Job Info...</div>;
  }
  if (!jobInfo) return null;

  return (
    <TabLayout
      title="Job Information"
      description="Manage employee position, department, and work details"
      employeeNo={employeeNo}
      loading={loading}
      actions={
        <Button
          onClick={handleSave}
          disabled={isSaving}
          className="flex items-center gap-2"
        >
          <Briefcase className="h-4 w-4" />
          {isSaving ? "Saving..." : "Save Job Info"}
        </Button>
      }
    >
      <FormGrid columns={2}>
        <FormField>
          <Label>Category</Label>
          <Input
            value={jobInfo.category || ""}
            onChange={(e) =>
              setJobInfo((prev) => prev && { ...prev, category: e.target.value })
            }
            placeholder="Enter category"
            className="transition-all duration-200"
          />
        </FormField>

        <FormField>
          <Label>Directorate</Label>
          <Input
            value={jobInfo.directorate || ""}
            onChange={(e) =>
              setJobInfo((prev) => prev && { ...prev, directorate: e.target.value })
            }
            placeholder="Enter directorate"
            className="transition-all duration-200"
          />
        </FormField>

        {/* Job Position Dropdown */}
        <FormField className="col-span-1 md:col-span-2">
          <Label>Job Title & Code</Label>
          <Popover open={openJobSelect} onOpenChange={setOpenJobSelect}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openJobSelect}
                className="w-full justify-between"
              >
                {jobInfo.job_title ?
                  `${jobInfo.job_title} (${jobInfo.job_title_code || ""})` :
                  "Select job position..."}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput placeholder="Search job positions..." />
                <CommandEmpty>
                  {jobPositionsLoading ? "Loading..." : "No job position found."}
                </CommandEmpty>
                <CommandGroup className="max-h-64 overflow-y-auto">
                  {jobPositions.map((job) => (
                    <CommandItem
                      key={job.id}
                      value={job.id.toString()}
                      onSelect={handleJobPositionSelection}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          jobInfo.job_title_code === job.job_code && jobInfo.job_title === job.job_title
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      {job.job_title} ({job.job_code})
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </FormField>

        <FormField>
          <Label>Job Title</Label>
          <Input
            value={jobInfo.job_title || ""}
            onChange={(e) =>
              setJobInfo((prev) => prev && { ...prev, job_title: e.target.value })
            }
            readOnly
            className="bg-muted/50"
          />
        </FormField>

        <FormField>
          <Label>Job Title Code</Label>
          <Input
            value={jobInfo.job_title_code || ""}
            onChange={(e) =>
              setJobInfo((prev) => prev && { ...prev, job_title_code: e.target.value })
            }
            readOnly
            className="bg-muted/50"
          />
        </FormField>

        {/* Team Code Dropdown */}
        <FormField>
          <Label>Team</Label>
          <Popover open={openTeamSelect} onOpenChange={setOpenTeamSelect}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openTeamSelect}
                className="w-full justify-between"
              >
                {organizationGroups.find(g => g.id === jobInfo.teams_code)?.name || "Select team..."}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput placeholder="Search teams..." />
                <CommandEmpty>
                  {organizationGroupsLoading ? "Loading..." : "No team found."}
                </CommandEmpty>
                <CommandGroup className="max-h-64 overflow-y-auto">
                  {organizationGroups.map((team) => (
                    <CommandItem
                      key={team.id}
                      value={team.id.toString()}
                      onSelect={handleTeamSelection}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          jobInfo.teams_code === team.id
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      {team.name} {team.description ? `- ${team.description}` : ''}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </FormField>

        {/* Work Location Dropdown */}
        <FormField>
          <Label>Work Location</Label>
          <Popover open={openWorkLocationSelect} onOpenChange={setOpenWorkLocationSelect}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openWorkLocationSelect}
                className="w-full justify-between"
              >
                {jobInfo.work_location || "Select work location..."}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput placeholder="Search work locations..." />
                <CommandEmpty>
                  {workLocationsLoading ? "Loading..." : "No work location found."}
                </CommandEmpty>
                <CommandGroup className="max-h-64 overflow-y-auto">
                  {workLocations.map((location) => (
                    <CommandItem
                      key={location.id}
                      value={location.id.toString()}
                      onSelect={handleWorkLocationSelection}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          jobInfo.work_location === location.work_location
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      {location.work_location} {location.work_location_description ? `- ${location.work_location_description}` : ''}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </FormField>

        <FormField>
          <Label>Business Unit</Label>
          <Input
            value={jobInfo.business_unit || ""}
            onChange={(e) =>
              setJobInfo((prev) => prev && { ...prev, business_unit: e.target.value })
            }
            placeholder="Enter business unit"
            className="transition-all duration-200"
          />
        </FormField>

        {/* Department Dropdown */}
        <FormField>
          <Label>Department</Label>
          <Popover open={openDeptSelect} onOpenChange={setOpenDeptSelect}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openDeptSelect}
                className="w-full justify-between"
              >
                {departments.find(d => d.id === jobInfo.department)?.name || "Select department..."}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput placeholder="Search departments..." />
                <CommandEmpty>
                  {departmentsLoading ? "Loading..." : "No department found."}
                </CommandEmpty>
                <CommandGroup className="max-h-64 overflow-y-auto">
                  {departments.map((dept) => (
                    <CommandItem
                      key={dept.id}
                      value={dept.id.toString()}
                      onSelect={handleDepartmentSelection}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          jobInfo.department === dept.id
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      {dept.name} - {dept.description}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </FormField>
      </FormGrid>
    </TabLayout>
  );
}