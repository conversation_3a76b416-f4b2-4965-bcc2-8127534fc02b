import React, { useState, useEffect, ChangeEvent } from "react";
import { motion, AnimatePresence } from "framer-motion";
import axios from "axios";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { BASE_URL } from "../../../config";

// Shadcn UI components
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Lucide-react icons
import {
  X,
  Save,
  Users,
  ArrowRight,
  ArrowDown,
  ArrowLeft,
  AlertTriangle,
  Loader2,
  Target,
  TrendingUp,
  Search
} from "lucide-react";

// Types
export interface SalesTarget {
  what: string;
  total_marks: number;
  sales_target: number;
  errors?: {
    total_marks?: string;
    sales_target?: string;
  };
}

export interface AssignmentTarget {
  ids: (string | number)[];
}

interface SalesTargetModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: (message: any) => void;
}

const SalesTargetModal: React.FC<SalesTargetModalProps> = ({ open, onClose, onSuccess }) => {
  const { token, user } = useSelector((state: RootState) => state.auth);
  const [activeStep, setActiveStep] = useState(0);
  const [salesTarget, setSalesTarget] = useState<SalesTarget>({
    what: "SALES TARGET",
    total_marks: 0,
    sales_target: 0,
    errors: {}
  });
  const [employees, setEmployees] = useState<any[]>([]);
  const [assignment, setAssignment] = useState<AssignmentTarget>({
    ids: [],
  });
  const [loading, setLoading] = useState(false);
  const [assignmentError, setAssignmentError] = useState("");
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error" | "info",
  });

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState("all");
  const [departments, setDepartments] = useState<any[]>([]);

  // Define the steps with corresponding icons
  const steps = [
    { label: "Define Sales Target", icon: <Target size={20} /> },
    { label: "Assign Employees", icon: <Users size={20} /> },
    { label: "Review & Submit", icon: <Save size={20} /> },
  ];

  // Enhanced search functions
  const getDepartmentName = (departmentId: number) => {
    const department = departments.find(dept => dept.id === departmentId);
    return department ? department.name : "Unknown Department";
  };

  const searchEmployees = (emp: any, searchTerm: string) => {
    if (!searchTerm) return true;

    const search = searchTerm.toLowerCase().trim();
    const deptName = getDepartmentName(emp.department_id).toLowerCase();

    return (
      emp.name.toLowerCase().includes(search) ||
      emp.employee_no.toLowerCase().includes(search) ||
      deptName.includes(search) ||
      `${emp.name} ${emp.employee_no}`.toLowerCase().includes(search)
    );
  };

  // Fetch employees and departments when the modal is open
  useEffect(() => {
    if (open) {
      const fetchData = async () => {
        try {
          const [empRes, deptRes] = await Promise.all([
            axios.get(`${BASE_URL}/users/all-employees`, {
              headers: { Authorization: `Token ${token}` },
            }),
            axios.get(`${BASE_URL}/users/departments`, {
              headers: { Authorization: `Token ${token}` },
            }),
          ]);

          setEmployees(
            empRes.data.map((e: any) => ({
              id: e.employee_no,
              name: `${e.first_name} ${e.last_name}`,
              email: e.email,
              employee_no: e.employee_no,
              department_id: e.department_id,
            }))
          );

          setDepartments(deptRes.data);
        } catch (err) {
          handleApiError(err, "Failed to fetch data. Please try again later.");
        }
      };
      fetchData();
    }
  }, [open, token]);

  // Validate the current step before proceeding
  const validateStep = (): boolean => {
    if (activeStep === 0) {
      const errors: any = {};
      if (salesTarget.total_marks <= 0) {
        errors.total_marks = "Marks must be positive";
      }
      if (salesTarget.sales_target <= 0) {
        errors.sales_target = "Sales Target must be positive";
      }
      
      setSalesTarget({ ...salesTarget, errors });
      return Object.keys(errors).length === 0;
    }
    if (activeStep === 1) {
      if (assignment.ids.length === 0) {
        setAssignmentError("Please select at least one employee");
        return false;
      }
      setAssignmentError("");
      return true;
    }
    return true;
  };

  // Handle Sales Target changes
  const handleSalesTargetChange = (
    field: keyof Omit<SalesTarget, "errors" | "what">,
    value: number
  ) => {
    const newSalesTarget = { ...salesTarget };
    newSalesTarget[field] = value;
    if (newSalesTarget.errors) {
      delete newSalesTarget.errors![field];
    }
    setSalesTarget(newSalesTarget);
  };

  // Submit handler
  const handleSubmit = async () => {
    if (!validateStep()) return;
  
    try {
      setLoading(true);
  
      // Create the Sales Target KPI
      const whatRes = await axios.post(
        `${BASE_URL}/hrm/kpi-whats`,
        {
          Kpi_type: "Sales Target", // Keep this the same as per your requirement
          what: "Sales Target", 
          Sales_target: salesTarget.sales_target, // Use the sales_target field here
          total_marks: salesTarget.total_marks,
          status: "approved",
          created_by: user?.employee_no,
        },
        { headers: { Authorization: `Token ${token}` } }
      );
  
      // Assign the created Sales Target to employees
      const assignmentPromises = assignment.ids.map((employeeId) =>
        axios.post(
          `${BASE_URL}/hrm/kpi-to-employee`,
          { employeeid: employeeId, kpi: whatRes.data.id },
          { headers: { Authorization: `Token ${token}` } }
        )
      );
  
      await Promise.all(assignmentPromises);
  
      // Show success message
      setSnackbar({
        open: true,
        message: "🎉 Sales Target created and assigned successfully!",
        severity: "success",
      });
  
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(`Sales Target created successfully with ID: ${whatRes.data.id}`);
      }
  
      // Close the modal
      onClose();
    } catch (err) {
      handleApiError(
        err,
        "Failed to save Sales Target. Please check your network connection and try again."
      );
    } finally {
      setLoading(false);
    }
  };
  
  // Generic API error handler
  const handleApiError = (error: unknown, defaultMessage: string) => {
    let message = defaultMessage;
    if (axios.isAxiosError(error)) {
      message = error.response?.data?.detail || error.message;
      if (error.response?.data) {
        message = Object.entries(error.response.data)
          .flatMap(([key, val]) =>
            Array.isArray(val) ? val : [`${key}: ${val}`]
          )
          .join("\n");
      }
    }
    setSnackbar({ open: true, message: `🚨 ${message}`, severity: "error" });
  };

  return (
    <>
      {open && (
        <Dialog open={open} onOpenChange={onClose}>
          <DialogContent className="max-w-4xl w-full p-6 rounded-3xl bg-background overflow-auto max-h-[80vh]">
            {/* Header */}
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center gap-4">
                <TrendingUp className="h-10 w-10 text-primary" />
                <div>
                  <h2 className="text-2xl font-semibold">
                    Sales Target Management
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Create and assign sales targets for employees
                  </p>
                </div>
              </div>
            </div>

            {/* Stepper */}
            <div className="mt-4 flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full ${
                      activeStep >= index
                        ? "bg-primary text-white"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    {step.icon}
                  </div>
                  <span className="mt-2 text-sm font-semibold">
                    {step.label}
                  </span>
                </div>
              ))}
            </div>

            {/* Step Content */}
            <div className="mt-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeStep}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {activeStep === 0 && (
                    <div className="p-4 mb-3 rounded-2xl bg-card shadow hover:shadow-lg transition">
                      <div className="flex items-center justify-between mb-2">
                        <span className="bg-primary text-white rounded px-2 py-1 text-xs font-semibold">
                          Sales Target
                        </span>
                      </div>

                      <div className="mb-4">
                        <Label htmlFor="sales-target-description">
                          Target Description
                        </Label>
                        <Input
                          id="sales-target-description"
                          value={salesTarget.what}
                          readOnly
                          className="mt-2 bg-muted font-semibold"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Sales Target description is auto-set
                        </p>
                      </div>

                      <div className="mb-4">
                        <Label htmlFor="sales-target-amount">
                          Sales Target Amount (KSh) *
                        </Label>
                        <div className="relative mt-2">
                          <Input
                            id="sales-target-amount"
                            type="number"
                            value={salesTarget.sales_target || ""}
                            onChange={(e) =>
                              handleSalesTargetChange(
                                "sales_target",
                                Number(e.target.value)
                              )
                            }
                            className={`pl-1 ${
                              salesTarget.errors?.sales_target ? "border-destructive" : ""
                            }`}
                            placeholder="Enter Sales Target amount in KSh"
                            min={1}
                          />
                        </div>
                        {salesTarget.errors?.sales_target && (
                          <p className="mt-1 text-xs text-destructive">
                            {salesTarget.errors.sales_target}
                          </p>
                        )}
                        <p className="text-xs text-muted-foreground mt-1">
                          This is the sales value (in KSh) that the employee is expected to achieve
                        </p>
                      </div>

                      <div className="mb-2">
                        <Label htmlFor="sales-target-marks">
                          Total Marks *
                        </Label>
                        <Input
                          id="sales-target-marks"
                          type="number"
                          value={salesTarget.total_marks || ""}
                          onChange={(e) =>
                            handleSalesTargetChange(
                              "total_marks",
                              Number(e.target.value)
                            )
                          }
                          className={`mt-2 w-48 ${
                            salesTarget.errors?.total_marks ? "border-destructive" : ""
                          }`}
                          placeholder="Value between 1-100"
                          min={1}
                          max={100}
                        />
                        {salesTarget.errors?.total_marks && (
                          <p className="mt-1 text-xs text-destructive">
                            {salesTarget.errors.total_marks}
                          </p>
                        )}
                        <p className="text-xs text-muted-foreground mt-1">
                          Weight of this Sales Target in the employee's overall performance evaluation
                        </p>
                      </div>
                    </div>
                  )}

                  {activeStep === 1 && (
                    <div>
                      {/* Search and Filter Section */}
                      <div className="space-y-3 mb-4">
                        <div className="flex gap-2">
                          <div className="relative flex-1">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                              type="text"
                              placeholder="Search employees by name, employee number, or department"
                              className="pl-10 pr-10 h-10"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                            />
                            {searchTerm && (
                              <button
                                type="button"
                                onClick={() => setSearchTerm("")}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                          {/* Department Filter */}
                          {departments.length > 0 && (
                            <Select
                              onValueChange={val => setDepartmentFilter(val)}
                              defaultValue="all"
                            >
                              <SelectTrigger className="w-[200px]">
                                <SelectValue placeholder="Filter by Department" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All Departments</SelectItem>
                                {departments
                                  .filter(dept => dept.department_status_active)
                                  .sort((a, b) => a.name.localeCompare(b.name))
                                  .map(dept =>(
                                    <SelectItem key={dept.id} value={dept.id.toString()}>
                                      {dept.name}
                                    </SelectItem>
                                  )
                                  )}
                              </SelectContent>
                            </Select>
                          )}
                        </div>

                        {/* Search Results Counter */}
                        {(searchTerm || departmentFilter !== "all") && (
                          <div className="text-xs text-muted-foreground">
                            {(() => {
                              const filteredCount = employees.filter(emp => {
                                const departmentMatches = departmentFilter === "all" ||
                                  emp.department_id.toString() === departmentFilter;
                                const searchMatches = searchEmployees(emp, searchTerm);
                                return departmentMatches && searchMatches;
                              }).length;

                              return `Showing ${filteredCount} of ${employees.length} employees`;
                            })()}
                          </div>
                        )}
                      </div>

                      {/* Employee Selection */}
                      <div className="mb-3">
                        <div className="flex items-center justify-between mb-2">
                          <Label>Select Employees</Label>
                          <div className="flex gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const filteredItems = employees
                                  .filter(emp => {
                                    const departmentMatches = departmentFilter === "all" ||
                                      emp.department_id.toString() === departmentFilter;
                                    const searchMatches = searchEmployees(emp, searchTerm);
                                    return departmentMatches && searchMatches;
                                  })
                                  .map(emp => emp.id.toString());
                                setAssignment({ ids: filteredItems });
                              }}
                            >
                              Select All Filtered
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setAssignment({ ids: [] })}
                            >
                              Clear All
                            </Button>
                          </div>
                        </div>
                        <select
                          multiple
                          value={assignment.ids.map(String)}
                          onChange={(e: ChangeEvent<HTMLSelectElement>) => {
                            const selected = Array.from(
                              e.target.selectedOptions
                            ).map((opt) => opt.value);
                            setAssignment({ ids: selected });
                          }}
                          className={`mt-1 block w-full h-60 rounded border border-gray-300 p-2 bg-background ${
                            assignmentError ? "border-destructive" : ""
                          }`}
                        >
                          {(() => {
                            const filteredItems = employees
                              .filter(emp => {
                                const departmentMatches = departmentFilter === "all" ||
                                  emp.department_id.toString() === departmentFilter;
                                const searchMatches = searchEmployees(emp, searchTerm);
                                return departmentMatches && searchMatches;
                              })
                              .sort((a, b) => a.name.localeCompare(b.name))
                              .map((employee) => {
                                const deptName = getDepartmentName(employee.department_id);
                                return (
                                  <option key={employee.id} value={employee.id}>
                                    {`${employee.name} - ${deptName} (${employee.employee_no})`}
                                  </option>
                                );
                              });

                            if (filteredItems.length === 0) {
                              return (
                                <option disabled className="text-muted-foreground italic">
                                  No employees found matching your search
                                </option>
                              );
                            }

                            return filteredItems;
                          })()}
                        </select>
                        <div className="flex items-center justify-between mt-2">
                          <p className="text-xs text-muted-foreground">
                            {assignment.ids.length} employees selected
                            <span className="ml-2 text-blue-600">
                              • Hold Ctrl/Cmd to select multiple
                            </span>
                          </p>
                          {assignment.ids.length > 0 && (
                            <p className="text-xs text-green-600 font-medium">
                              ✓ Ready to assign Sales Target
                            </p>
                          )}
                        </div>
                        {assignmentError && (
                          <p className="mt-1 text-xs text-destructive">
                            {assignmentError}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {activeStep === 2 && (
                    <div>
                      <h3 className="text-xl font-semibold mb-3">
                        📋 Review Summary
                      </h3>
                      <div className="p-4 mb-4 rounded bg-card shadow">
                        <div className="flex items-center gap-2 mb-3">
                          <span className="bg-primary text-white rounded px-2 py-1 text-xs font-semibold">
                            Sales Target
                          </span>
                          <span className="bg-secondary text-white rounded px-2 py-1 text-xs">
                            {salesTarget.total_marks} Marks
                          </span>
                        </div>

                        <div className="mb-4">
                          <h4 className="text-lg text-primary font-semibold">
                            {salesTarget.what}
                          </h4>
                          
                          <div className="mt-4 p-3 bg-muted rounded-lg">
                            <div className="flex items-center">
                              <ArrowRight className="h-5 w-5 text-muted-foreground mr-2" />
                              <div>
                                <span className="text-sm font-medium">Sales Target Amount:</span>
                                <span className="ml-2 text-lg font-semibold text-primary">
                                  KSh {salesTarget.sales_target.toLocaleString()}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <hr className="mb-3" />

                        <div>
                          <Label className="mb-1">✨ Assigned To:</Label>
                          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 mt-1 max-h-96 overflow-y-auto pr-1">
                            {employees
                              .filter((e) =>
                                assignment.ids.includes(String(e.id))
                              )
                              .map((emp) => (
                                <div
                                  key={emp.id}
                                  className="p-3 bg-muted rounded border border-gray-200 hover:shadow hover:border-primary hover:bg-primary/10 transition"
                                >
                                  <div className="flex items-start gap-3">
                                    <div className="w-2 h-2 rounded-full bg-green-500 mt-1 flex-shrink-0"></div>
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center gap-1 mb-0.5">
                                        <p className="text-sm font-semibold text-primary whitespace-nowrap overflow-hidden text-ellipsis">
                                          {emp.name}
                                        </p>
                                        <span className="bg-primary/10 text-primary rounded px-2 py-0.5 text-xs font-medium">
                                          #{emp.employee_no}
                                        </span>
                                      </div>
                                      <p className="text-xs text-muted-foreground whitespace-nowrap overflow-hidden text-ellipsis">
                                        {emp.email}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Action Buttons */}
            <div className="mt-4 flex items-center justify-between bg-muted p-4 rounded">
              <Button
                onClick={() =>
                  activeStep > 0 ? setActiveStep(activeStep - 1) : onClose()
                }
                disabled={loading}
                variant="outline"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />{" "}
                {activeStep === 0 ? "Cancel" : "Back"}
              </Button>
              <Button
                onClick={() => {
                  if (activeStep < steps.length - 1) {
                    if (!validateStep()) return;
                    setActiveStep(activeStep + 1);
                  } else {
                    handleSubmit();
                  }
                }}
                disabled={loading}
                className="px-8 font-semibold bg-gradient-to-r from-primary to-secondary hover:opacity-90"
              >
                {loading ? (
                  <Loader2 className="animate-spin h-5 w-5" />
                ) : activeStep < steps.length - 1 ? (
                  <>
                    Next Step <ArrowDown className="ml-2 h-5 w-5" />
                  </>
                ) : (
                  <>
                    Create Sales Target <Save className="ml-2 h-5 w-5" />
                  </>
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Snackbar / Toast */}
      {snackbar.open && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className={`p-4 rounded shadow ${
            snackbar.severity === "error" ? "bg-destructive" : "bg-green-500"
          } text-white flex items-center gap-2`}>
            {snackbar.severity === "error" && (
              <AlertTriangle className="h-5 w-5" />
            )}
            <p className="whitespace-pre-line">{snackbar.message}</p>
            <button
              onClick={() =>
                setSnackbar((prev) => ({ ...prev, open: false }))
              }
              className="ml-4"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default SalesTargetModal;