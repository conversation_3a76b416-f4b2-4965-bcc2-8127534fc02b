import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { BASE_URL } from '@/config';
import { RootState } from '@/redux/store';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  AlertCircle,
  Check,
  X,
  MessageCircle,
  Eye,
  Search,
} from 'lucide-react';
import HRAppraisalTable from './HRAppraisalTable';
import ActionModal from './ActionModal';
import ConfirmationModal from './ConfirmationModal';
import KPIDetailsSection from './KPIDetailsSection';
import { AppraisalViewModel, AppraisalPeriod, Employee, Department, AppraisalResult, KpiDetail, AppraisalPeriodForm } from './types';
import { Kpi } from '@/pages/Performance/KpiManagementPage';
import { KpiDetailModal } from '@/pages/Performance/KpiManagementPage';
import axios from 'axios';

interface HRAppraisalRecordProps {
  appraisalRecords: AppraisalViewModel[];
  employees: Employee[];
  departments: Department[];
  appraisalPeriods: AppraisalPeriod[];
}

const RECORDS_PER_PAGE = 10;

const HRAppraisalRecord: React.FC<HRAppraisalRecordProps> = ({
  appraisalRecords,
  employees,
  departments,
  appraisalPeriods
}) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { user } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [error, setError] = useState<string>('');
  const [selectedRecordId, setSelectedRecordId] = useState<number | null>(null);
  const [employeeDetails, setEmployeeDetails] = useState<any>({});
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedRecordDetails, setSelectedRecordDetails] = useState<AppraisalViewModel | null>(null);
  const [detailedKpis, setDetailedKpis] = useState<KpiDetail[]>([]);
  const [hrRemarks, setHrRemarks] = useState<string>('');
  const [wayForward, setWayForward] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingKpis, setLoadingKpis] = useState<boolean>(false);
  const [confirmModalVisible, setConfirmModalVisible] = useState<boolean>(false);
  const [pendingAction, setPendingAction] = useState<'approve' | 'reject' | null>(null);
  const [employeeContractInfo, setEmployeeContractInfo] = useState<any>([]);
  const [actionModalVisible, setActionModalVisible] = useState(false);
  const [selectedAction, setSelectedAction] = useState<any>('');
  const [kpiDetailOpen, setKpiDetailOpen] = useState(false);
  const [newAppraisalPeriod, setNewAppraisalPeriod] = useState<AppraisalPeriodForm>({
    name: '',
    start_date: '',
    end_date: '',
    cycle_type: 'ANNUAL',
    description: '',
    contract_type: '',
    on_pip: undefined,
    end_of_probation_date: null
  });
  // Search functionality
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredRecords, setFilteredRecords] = useState<AppraisalViewModel[]>(appraisalRecords);

  // Filter records based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredRecords(appraisalRecords);
    } else {
      const filtered = appraisalRecords.filter(record => {
        const employee = employees.find(emp => emp.employee_no === record.employeeid);
        const department = departments.find(dept => dept.id === employee?.department_id);

        const employeeName = employee ? `${employee.first_name} ${employee.last_name}`.toLowerCase() : '';
        const employeeNo = record.employeeid?.toLowerCase() || '';
        const departmentName = department?.name?.toLowerCase() || '';

        const search = searchTerm.toLowerCase();

        return employeeName.includes(search) ||
          employeeNo.includes(search) ||
          departmentName.includes(search);
      });
      setFilteredRecords(filtered);
    }
    setCurrentPage(1); // Reset to first page when searching
  }, [searchTerm, appraisalRecords, employees, departments]);

  const startIndex = (currentPage - 1) * RECORDS_PER_PAGE;
  const currentRecords = filteredRecords.slice(startIndex, startIndex + RECORDS_PER_PAGE);
  const totalPages = Math.ceil(filteredRecords.length / RECORDS_PER_PAGE);
  const [selectedKpis, setSelectedKpis] = useState<Kpi[]>([]);
  const handlePageChange = (page: number) => {
    if (page <= 0 || page > totalPages) return;
    setCurrentPage(page);
  };



  const fetchEmployeeDetails = async (employeeId: string) => {
    const employee = employees.find((emp) => emp.employee_no === employeeId);
    const department = departments.find((dept) => dept.id === employee?.department_id);

    setEmployeeDetails({
      firstName: employee?.first_name || "Unknown",
      lastName: employee?.last_name || "Unknown",
      employeeNo: employee?.employee_no || "N/A",
      departmentName: department?.name || "Unknown Department",
    });
  };

  const fetchEmployeeContractInfo = async (employeeNo: string) => {
    console.log("Fetching contract info for employee:", employeeNo);
    try {
      const res = await fetch(`${BASE_URL}/users/employee-contract-info-details?employee_no=${employeeNo}`, {
        headers: { Authorization: `Token ${token}` },
      });
      if (!res.ok) throw new Error('Failed to fetch contract details');
      const data = await res.json();
      console.log("Employee contract details:", data);
      
      // Filter the data to only include records for this specific employee
      const filteredData = Array.isArray(data) 
        ? data.filter(record => record.employee_no === employeeNo)
        : [];
      
      console.log("Filtered contract data for employee:", filteredData);
      return filteredData;
    } catch (error) {
      console.error("Error fetching contract info:", error);
      return [];
    }
  }


  const fetchOriginalKpis = async (employeeId: string) => {
    setLoadingKpis(true);
    try {
      const kpiToEmployeeRes = await axios.get(
        `${BASE_URL}/hrm/kpi-to-employee`,
        {
          params: { employee_no: employeeId },
          headers: { Authorization: `Token ${token}` },
        }
      );
      const kpiIds = kpiToEmployeeRes.data
        .filter((item: any) => item.employeeid === employeeId)
        .map((item: any) => item.kpi);

      if (kpiIds.length === 0) {
        setSelectedKpis([]);
        return;
      }
      const kpisWithDetails = await Promise.all(
        kpiIds.map(async (kpiId: number) => {
          const whatRes = await axios.get(
            `${BASE_URL}/hrm/kpi-whats/${kpiId}`,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );

          const kpiType = whatRes.data.Kpi_type;
          let hows = [];
          if (kpiType !== 'MIB Target' && kpiType !== 'Sales Target') {
            const howsRes = await axios.get(`${BASE_URL}/hrm/kpi-hows`, {
              params: { whats: kpiId },
              headers: { Authorization: `Token ${token}` },
            });
            hows = howsRes.data.map((h: any) => ({
              id: h.id,
              how: h.how,
            }));
          }

          return {
            id: whatRes.data.id,
            what: whatRes.data.what,
            total_marks: whatRes.data.total_marks,
            created_at: whatRes.data.created_at,
            hows: hows,
            MIB_target: whatRes.data.MIB_target,
            Sales_target: whatRes.data.Sales_target,
            kpi_type: kpiType || 'default'
          };
        })
      );

      setSelectedKpis(kpisWithDetails);
    } catch (err) {
      console.error("Error fetching original KPIs:", err);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load original KPIs',
      });
      setSelectedKpis([]);
    } finally {
      setLoadingKpis(false);
    }
  };

  const fetchDetailedKpis = async (employeeId: string, periodId: number | undefined) => {
    setLoadingKpis(true);
    setDetailedKpis([]);

    try {
      // Fetch appraisal results based on new API structure
      const periodFilterParam = periodId ? `&appraisal_period=${periodId}` : '';
      const kpiRes = await fetch(
        `${BASE_URL}/hrm/appraisal-results?employee=${employeeId}${periodFilterParam}`,
        {
          headers: { Authorization: `Token ${token}` },
        }
      );

      if (!kpiRes.ok) throw new Error('Failed to fetch appraisal results');
      const kpiData = await kpiRes.json();

      if (!kpiData.length) throw new Error('No KPIs found for this appraisal period');

      // Process each KPI with its details and hows
      const processedKpis = await Promise.all(
        kpiData.map(async (result: AppraisalResult) => {
          try {
            // Fetch KPI details
            const detailRes = await fetch(`${BASE_URL}/hrm/kpi-whats?id=${result.what}`, {
              headers: { Authorization: `Token ${token}` },
            });

            if (!detailRes.ok) throw new Error(`Failed to fetch KPI ${result.what} details`);
            const detailData = await detailRes.json();
            const kpiDetail = detailData[0];

            // Check KPI type and fetch appropriate details
            let processedHows: string[] = [];
            if (kpiDetail.Kpi_type !== 'MIB Target' && kpiDetail.Kpi_type !== 'Sales Target') {
              // Fetch how details for non-MIB/Sales Target KPIs
              const howRes = await fetch(`${BASE_URL}/hrm/kpi-hows?whats=${result.what}`, {
                headers: { Authorization: `Token ${token}` },
              });

              if (!howRes.ok) throw new Error(`Failed to fetch hows for KPI ${result.what}`);
              const howData = await howRes.json();
              processedHows = howData.map((h: any) => h.how);
            }

            return {
              id: result.id,
              what: kpiDetail?.what || 'No description',
              kpi_type: kpiDetail?.Kpi_type || 'default',
              total_marks: kpiDetail?.total_marks || 0,
              hows: processedHows,
              // Include both targets and achievements from the API
              MIB_target: kpiDetail?.MIB_target || null,
              MIB_Achieved: result.MIB_Achieved || null,
              Sales_target: kpiDetail?.Sales_target || null,
              Sales_Achieved: result.Sales_Achieved || null,
              // Include all rating data from the new API structure
              employee_rating: result.employee_rating,
              supervisor_rating: result.supervisor_rating,
              manager_rating: result.manager_rating || null,
              extra_rating: result.extra_rating || null,
              // Include all comments
              employee_comments: result.employee_comments || 'No comments provided',
              supervisor_comments: result.supervisor_comments || '',
              manager_comments: result.manager_comments || '',
              extra_comments: result.extra_comments || '',
            } as KpiDetail;
          } catch (error) {
            console.error(`Error processing KPI ${result.what}:`, error);
            return null;
          }
        })
      );

      const validKpis = processedKpis.filter(kpi => kpi !== null) as KpiDetail[];
      setDetailedKpis(validKpis);

    } catch (err: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to load KPI details',
      });
    } finally {
      setLoadingKpis(false);
    }
  };


  const handleProbationAction = () => {
    if (!employeeContractInfo || employeeContractInfo.length === 0) return [];
    const { contract_type, on_pip } = employeeContractInfo[0];

    const baseActions = [];


    if (contract_type === 'Permanent' && !on_pip) {
      baseActions.push(
        {
          label: 'Create New Appraisal Period',
          type: 'NEW_PERIOD',
          fields: ['name', 'start_date', 'end_date', 'cycle_type', 'description'],
          contractChange: false,
          autoPopulateWayForward: 'New appraisal period has been created.'
        }
      );
    }

    if (on_pip) {
      baseActions.push(
        {
          label: 'Extend PIP Period',
          type: 'EXTEND_PIP',
          fields: ['name', 'start_date', 'end_date', 'cycle_type', 'description'],
          contractChange: true,
          contractUpdate: { on_pip: true },
          autoPopulateWayForward: 'Performance Improvement Plan has been extended based on current assessment.'
        },
        {
          label: 'Remove from PIP',
          type: 'REMOVE_PIP',
          fields: ['name', 'start_date', 'end_date', 'cycle_type', 'description'],
          contractChange: true,
          contractUpdate: { on_pip: false },
          autoPopulateWayForward: 'Employee has successfully completed the Performance Improvement Plan.'
        }
      );
    } else {

      baseActions.push(
        {
          label: 'Initiate PIP',
          type: 'INITIATE_PIP',
          fields: ['name', 'start_date', 'end_date', 'cycle_type', 'description'],
          contractChange: true,
          contractUpdate: { on_pip: true },
          autoPopulateWayForward: 'Employee has been placed on Performance Improvement Plan.'
        }
      );
    }

    if (contract_type === 'Probation') {
      baseActions.push(
        {
          label: 'Convert to Permanent',
          type: 'CONVERT_PERMANENT',
          fields: ['name', 'start_date', 'end_date', 'cycle_type', 'contract_type', 'end_of_probation_date', 'description'],
          contractChange: true,
          contractUpdate: {
            contract_type: 'Permanent',
            end_of_probation_date: new Date().toISOString().split('T')[0]
          },
          autoPopulateWayForward: 'Employee has successfully completed probation and converted to permanent employment.'
        },
        {
          label: 'Extend Probation',
          type: 'EXTEND_PROBATION',
          fields: ['name', 'start_date', 'end_date', 'cycle_type', 'end_of_probation_date', 'description'],
          contractChange: true,
          contractUpdate: { contract_type: 'Probation' },
          autoPopulateWayForward: 'Probation period has been extended for further assessment.'
        }
      );
    }

    if (contract_type === 'Permanent' && !on_pip) {
      baseActions.push(
        {
          label: 'Demote to Probation',
          type: 'DEMOTE_PROBATION',
          fields: ['name', 'start_date', 'end_date', 'cycle_type', 'contract_type', 'end_of_probation_date', 'description'],
          contractChange: true,
          contractUpdate: {
            contract_type: 'Probation',
            end_of_probation_date: null
          },
          autoPopulateWayForward: 'Employee has been reverted to probationary status.'
        }
      );
    }

    return baseActions;
  };





  const handleViewDetails = async (recordId: number, employeeId: string, periodId: number) => {
    if (selectedRecordId === recordId && modalVisible) {
      setModalVisible(false);
      setSelectedRecordId(null);
      setHrRemarks('');
      setWayForward('');
      setDetailedKpis([]);
    } else {
      setSelectedRecordId(recordId);
      setModalVisible(true);
      const selectedRecord = appraisalRecords.find((record) => record.id === recordId);
      setSelectedRecordDetails(selectedRecord || null);

      const contractInfo = await fetchEmployeeContractInfo(employeeId);
      setEmployeeContractInfo(contractInfo);

      setWayForward('');
      setHrRemarks('');
      if (selectedRecord) {
        setHrRemarks(selectedRecord.final_hr_comments || '');
        setWayForward(selectedRecord.way_forward || '');
      }

      await fetchEmployeeDetails(employeeId);


      await fetchDetailedKpis(employeeId, periodId);
    }
  };


  const handleConfirmAction = (action: 'approve' | 'reject') => {
    setPendingAction(action);
    setConfirmModalVisible(true);
  };

  const handleApproveRejectRecord = async (action: 'approve' | 'reject') => {
    if (!selectedRecordDetails) return;

    setLoading(true);
    try {

      if (hrRemarks.trim() === '') {
        alert('❌ Missing Information\n\nPlease provide HR assessment comments before approving or rejecting the appraisal.');
        setLoading(false);
        return;
      }

      if (wayForward.trim() === '') {
        alert('❌ Missing Way Forward\n\nPlease complete an employment action first to auto-generate the way forward, then you can approve the appraisal.');
        setLoading(false);
        return;
      }


      const totalSupScore = detailedKpis.reduce((sum, kpi) => sum + (kpi.supervisor_rating || 0), 0);
      const totalEmpScore = detailedKpis.reduce((sum, kpi) => sum + (kpi.employee_rating || 0), 0);

      const status = action === 'approve' ? "Completed" : "HR Appraised";


      const payload = {
        status,
        hr_acceptance: action === 'approve',
        final_hr_comments: hrRemarks,
        way_forward: wayForward,
        total_emp_self_rating_score: totalEmpScore,
        total_supervisor_rating_score: totalSupScore,
        employeeid: selectedRecordDetails.employeeid,
        supervisor: selectedRecordDetails.supervisor || '',
        period_id: selectedRecordDetails.period_id,
      };

      const res = await fetch(`${BASE_URL}/hrm/employee-appraisals/${selectedRecordId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json', Authorization: `Token ${token}` },
        body: JSON.stringify(payload)
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(`Failed to update appraisal status: ${JSON.stringify(errorData)}`);
      }


      const updatedRecord = {
        ...selectedRecordDetails,
        status,
        hr_acceptance: action === 'approve',
        final_hr_comments: hrRemarks,
        way_forward: wayForward,
        total_emp_self_rating_score: totalEmpScore,
        total_supervisor_rating_score: totalSupScore,
      };

      setSelectedRecordDetails(updatedRecord);

      toast({
        variant: 'default',
        title: 'Success',
        description: `Appraisal has been ${action === 'approve' ? 'approved' : 'rejected'}.`,
      });


      setModalVisible(false);

    } catch (err: any) {
      setError('Error updating appraisal status');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to update appraisal status',
      });
      console.error("API Error:", err);
    } finally {
      setLoading(false);
    }
  };



  const getAppraisalPeriodName = (periodId?: number) => {
    if (!periodId) return 'Unknown Period';
    const period = appraisalPeriods.find(p => p.id === periodId);
    return period ? period.name : `Period ${periodId}`;
  };


  const renderPagination = () => {
    if (totalPages <= 1) return null;

    return (
      <div className="flex justify-center mt-6 space-x-2">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded ${currentPage === 1
            ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
            : 'bg-green-600 text-white hover:bg-green-700'
            }`}
        >
          Prev
        </button>

        <div className="px-3 py-1 bg-gray-100 rounded">
          Page {currentPage} of {totalPages}
        </div>

        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`px-3 py-1 rounded ${currentPage === totalPages
            ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
            : 'bg-green-600 text-white hover:bg-green-700'
            }`}
        >
          Next
        </button>
      </div>
    );
  };



  return (
    <div className="overflow-x-auto bg-white shadow-lg rounded-lg">
      <div className="p-4 flex justify-between items-center bg-gradient-to-r from-green-500 via-green-600 to-green-700 text-white">
        <h3 className="text-xl font-semibold">Appraisal Records</h3>
        {error && (
          <div className="flex items-center text-white bg-red-500 px-3 py-1 rounded">
            <AlertCircle size={16} className="mr-1" />
            {error}
          </div>
        )}
      </div>

      {/* Search Section */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="relative max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by employee name, number, or department..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500"
          />
        </div>
        {searchTerm && (
          <div className="mt-2 text-sm text-gray-600">
            Showing {filteredRecords.length} of {appraisalRecords.length} records
          </div>
        )}
      </div>

      {/* Table Component */}
      <HRAppraisalTable
        currentRecords={currentRecords}
        employees={employees}
        departments={departments}
        handleViewDetails={(recordId, employeeId, periodId) => handleViewDetails(recordId, employeeId, periodId)}
      />

      {renderPagination()}

      {/* Modal for View Details */}
      {modalVisible && selectedRecordDetails && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
          <div className="relative w-full max-w-4xl bg-white rounded-lg shadow-xl overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-green-600 to-green-800 text-white px-6 py-4 flex justify-between items-center">
              <h2 className="text-xl font-semibold">Appraisal Details</h2>
              <button
                onClick={() => setModalVisible(false)}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* Modal Body - Scrollable */}
            <div className="p-6 max-h-[70vh] overflow-y-auto">
              {/* Employee Information Card */}
              <div className="bg-green-50 border border-green-100 p-4 rounded-lg mb-6">
                <h3 className="text-lg font-medium mb-2 text-green-800">Employee Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <span className="font-medium text-gray-700 w-32">Name:</span>
                    <span>{employeeDetails.firstName} {employeeDetails.lastName}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium text-gray-700 w-32">Employee No:</span>
                    <span>{employeeDetails.employeeNo}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium text-gray-700 w-32">Department:</span>
                    <span>{employeeDetails.departmentName}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium text-gray-700 w-32">Contract Type:</span>
                    <span>{employeeContractInfo[0]?.contract_type || 'N/A'}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium text-gray-700 w-32">On PIP:</span>
                    <span>
                      {employeeContractInfo && 
                       employeeContractInfo.length > 0 && 
                       employeeContractInfo[0]?.on_pip === true ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium text-gray-700 w-32">Appraisal Period:</span>
                    <span>{getAppraisalPeriodName(selectedRecordDetails.period_id)}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium text-gray-700 w-32">Appraisal Score:</span>
                    <span className="font-semibold text-green-700">
                      {detailedKpis.reduce((sum, kpi) => sum + (kpi.supervisor_rating || 0), 0)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Appraisal Status Indicators */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <div className={`w-3 h-3 rounded-full mr-2 ${selectedRecordDetails.employee_acceptance ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                  <span className="text-sm">Self Appraised: <span className="font-medium">{selectedRecordDetails.employee_acceptance ? 'Yes' : 'No'}</span></span>
                </div>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <div className={`w-3 h-3 rounded-full mr-2 ${selectedRecordDetails.supervisor_acceptance ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                  <span className="text-sm">Supervisor Appraised: <span className="font-medium">{selectedRecordDetails.supervisor_acceptance ? 'Yes' : 'No'}</span></span>
                </div>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <div className={`w-3 h-3 rounded-full mr-2 ${(selectedRecordDetails.status === "HR Appraised" || selectedRecordDetails.status === "Completed") ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                  <span className="text-sm">HR Appraised: <span className="font-medium">{(selectedRecordDetails.status === "HR Appraised" || selectedRecordDetails.status === "Completed") ? 'Yes' : 'No'}</span></span>
                </div>
              </div>

              {/* KPI Details Section */}
              <KPIDetailsSection
                detailedKpis={detailedKpis}
                loadingKpis={loadingKpis}
              />

              {/* Overall Scores */}
              <div className="mb-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium mb-3 text-gray-800">Overall Score</h3>

                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-white rounded border border-gray-200 p-3 text-center">
                    <span className="block text-gray-500 text-sm">Total Possible</span>
                    <span className="block text-xl font-semibold text-gray-800">
                      {detailedKpis.reduce((sum, kpi) => sum + kpi.total_marks, 0)}
                    </span>
                  </div>
                  <div className="bg-white rounded border border-gray-200 p-3 text-center">
                    <span className="block text-gray-500 text-sm">Employee Score</span>
                    <span className="block text-xl font-semibold text-green-600">
                      {detailedKpis.reduce((sum, kpi) => sum + (kpi.employee_rating || 0), 0)}
                    </span>
                  </div>
                  <div className="bg-white rounded border border-green-200 p-3 text-center">
                    <span className="block text-gray-500 text-sm">Supervisor Score</span>
                    <span className="block text-xl font-semibold text-green-600">
                      {detailedKpis.reduce((sum, kpi) => sum + (kpi.supervisor_rating || 0), 0)}
                    </span>
                  </div>
                </div>

                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div className="bg-white rounded border border-gray-200 p-3">
                    <span className="block text-gray-500 text-sm mb-1">Employee Score Percentage</span>
                    <div className="h-6 w-full bg-gray-200 rounded-full overflow-hidden">
                      {detailedKpis.length > 0 && (
                        <div
                          className="h-full bg-green-500"
                          style={{
                            width: `${Math.min(
                              100,
                              (detailedKpis.reduce((sum, kpi) => sum + (kpi.employee_rating || 0), 0) /
                                detailedKpis.reduce((sum, kpi) => sum + kpi.total_marks, 0)) * 100
                            )}%`
                          }}
                        ></div>
                      )}
                    </div>
                    <span className="block text-right mt-1 text-sm font-medium">
                      {detailedKpis.length > 0 ?
                        `${((detailedKpis.reduce((sum, kpi) => sum + (kpi.employee_rating || 0), 0) /
                          detailedKpis.reduce((sum, kpi) => sum + kpi.total_marks, 0)) * 100).toFixed(2)}%`
                        : '0%'}
                    </span>
                  </div>
                  <div className="bg-white rounded border border-green-200 p-3">
                    <span className="block text-gray-500 text-sm mb-1">Supervisor Score Percentage</span>
                    <div className="h-6 w-full bg-gray-200 rounded-full overflow-hidden">
                      {detailedKpis.length > 0 && (
                        <div
                          className="h-full bg-green-500"
                          style={{
                            width: `${Math.min(
                              100,
                              (detailedKpis.reduce((sum, kpi) => sum + (kpi.supervisor_rating || 0), 0) /
                                detailedKpis.reduce((sum, kpi) => sum + kpi.total_marks, 0)) * 100
                            )}%`
                          }}
                        ></div>
                      )}
                    </div>
                    <span className="block text-right mt-1 text-sm font-medium">
                      {detailedKpis.length > 0 ?
                        `${((detailedKpis.reduce((sum, kpi) => sum + (kpi.supervisor_rating || 0), 0) /
                          detailedKpis.reduce((sum, kpi) => sum + kpi.total_marks, 0)) * 100).toFixed(2)}%`
                        : '0%'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Supervisor Comments Section */}
              {selectedRecordDetails.supervisor_comments && (
                <div className="mb-6 bg-blue-50 border border-blue-100 rounded-lg p-4">
                  <h3 className="text-lg font-medium mb-3 text-blue-800">Supervisor General Comments</h3>
                  <div className="bg-white border border-blue-200 rounded-md p-3">
                    <p className="text-gray-700 whitespace-pre-wrap">{selectedRecordDetails.supervisor_comments}</p>
                  </div>
                </div>
              )}

              {/* HR Remarks Section */}
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-4 flex items-center text-gray-800">
                  <MessageCircle size={18} className="mr-2 text-green-600" />
                  HR Assessment
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1 text-gray-700">
                      Final Assessment *
                      <span className="text-xs text-gray-500 ml-2">(Required for approval)</span>
                    </label>
                    <textarea
                      placeholder="Enter your detailed HR assessment for this appraisal..."
                      value={hrRemarks}
                      onChange={(e) => setHrRemarks(e.target.value)}
                      className={`w-full h-24 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 resize-none ${(selectedRecordDetails.status === "HR Appraised" || selectedRecordDetails.status === "Completed") ? 'bg-gray-50' : ''
                        }`}
                      disabled={selectedRecordDetails.status === "HR Appraised" || selectedRecordDetails.status === "Completed"}
                    />
                    {(selectedRecordDetails.status !== "HR Appraised" && selectedRecordDetails.status !== "Completed") && (
                      <p className="text-xs text-gray-500 mt-1">
                        * This field is required before you can approve the appraisal or take employment actions.
                      </p>
                    )}
                  </div>

                  <div className="mb-4 flex ">
                    <Button
                      onClick={() => {
                        fetchOriginalKpis(selectedRecordDetails.employeeid);
                        setKpiDetailOpen(true);
                      }}
                      variant="outline"
                      className="flex items-center gap-2 bg-green-600 text-white border-green-500 hover:bg-white hover:text-green-600 hover:border-green-500 transition-all"
                    >
                      <Eye className="h-4 w-4" /> View Full KPI Details
                    </Button>
                  </div>
                  {/* Probation Actions Section */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-4 text-gray-800">Employment Actions</h3>

                    {/* Show workflow guidance */}
                    {(selectedRecordDetails.status !== "HR Appraised" && selectedRecordDetails.status !== "Completed") && (
                      <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                          <div>
                            <h4 className="font-medium text-blue-800">Employment Action Required</h4>
                            <p className="text-sm text-blue-700 mt-1">
                              1. First, provide your final assessment comments above<br />
                              2. Then, select an employment action below to auto-generate the way forward<br />
                              3. Finally, you can approve the appraisal record
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {handleProbationAction().map((action, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            // Check if HR has provided assessment comments first
                            if (hrRemarks.trim() === '') {
                              alert('⚠️ Assessment Required\n\nPlease provide your final assessment comments before selecting an employment action.');
                              return;
                            }

                            setSelectedAction(action);
                            setActionModalVisible(true);
                            setNewAppraisalPeriod(prev => ({
                              ...prev,
                              start_date: selectedRecordDetails?.start_date || '',
                              end_date: selectedRecordDetails?.end_date || '',
                              contract_type: employeeContractInfo[0]?.contract_type,
                              on_pip: employeeContractInfo[0]?.on_pip
                            }));
                            // Auto-populate way forward based on the selected action
                            if (action.autoPopulateWayForward) {
                              setWayForward(action.autoPopulateWayForward);
                            }
                          }}
                          disabled={hrRemarks.trim() === ''}
                          className={`p-4 text-left rounded-lg transition-all ${hrRemarks.trim() === ''
                            ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-white shadow border-green-500 hover:border-green-500 hover:shadow-md'
                            }`}
                        >
                          <h4 className="font-medium mb-2">{action.label}</h4>
                          <p className="text-sm">
                            {action.type === 'EXTEND_PIP' && 'Extend the current Performance Improvement Plan period'}
                            {action.type === 'REMOVE_PIP' && 'Remove employee from Performance Improvement Plan'}
                            {action.type === 'EXTEND_PROBATION' && 'Extend probation period with new appraisal cycle'}
                            {action.type === 'CONVERT_PERMANENT' && 'Convert to permanent employment contract'}
                            {action.type === 'INITIATE_PIP' && 'Initiate Performance Improvement Plan'}
                            {action.type === 'DEMOTE_PROBATION' && 'Change employee status back to probation'}
                            {action.type === 'NEW_PERIOD' && 'Create a new appraisal period for this employee'}
                          </p>
                        </button>
                      ))}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 text-gray-700">Way Forward</label>
                    <div className="w-full h-24 px-4 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700">
                      {wayForward || 'Way forward will be auto-generated based on employment action taken.'}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      * Way forward is automatically generated based on the employment action you select above.
                    </p>
                  </div>
                </div>
              </div>
              {/* Modal Action Buttons */}
              {(selectedRecordDetails.status !== "HR Appraised" && selectedRecordDetails.status !== "Completed") && (
                <div className="flex justify-end space-x-4 mt-6">
                  <button
                    onClick={() => handleConfirmAction('reject')}
                    className="flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    disabled={loading || !selectedRecordDetails.supervisor_acceptance}
                  >
                    <X size={16} className="mr-2" />
                    Reject
                  </button>
                  <button
                    onClick={() => handleConfirmAction('approve')}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                    disabled={loading || !selectedRecordDetails.supervisor_acceptance}
                  >
                    <Check size={16} className="mr-2" />
                    Approve
                  </button>
                </div>
              )}

              {!selectedRecordDetails.supervisor_acceptance && (
                <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded text-amber-800 text-sm">
                  <p>This appraisal is still pending supervisor assessment. HR review is only available after supervisor completes their assessment.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <KpiDetailModal
        open={kpiDetailOpen}
        onClose={() => setKpiDetailOpen(false)}
        title="Original KPI Details"
        subtitle={`Viewing original KPIs for ${employeeDetails.firstName} ${employeeDetails.lastName}`}
        kpis={selectedKpis}
        loading={loadingKpis}
        token={token}
        onKpiUpdated={() => {
          // Refresh both original and appraisal KPIs if needed
          if (selectedRecordDetails) {
            fetchOriginalKpis(selectedRecordDetails.employeeid);
            fetchDetailedKpis(
              selectedRecordDetails.employeeid,
              selectedRecordDetails.period_id
            );
          }
        }}
      />
      {/* Action and Confirmation Modal */}
      {confirmModalVisible && (
        <ConfirmationModal
          pendingAction={pendingAction!}
          setConfirmModalVisible={setConfirmModalVisible}
          handleApproveRejectRecord={handleApproveRejectRecord}
        />
      )}
      {actionModalVisible && (
        <ActionModal
          selectedAction={selectedAction.label}
          selectedRecordDetails={selectedRecordDetails}
          employeeContractInfo={employeeContractInfo}
          newAppraisalPeriod={newAppraisalPeriod}
          setActionModalVisible={setActionModalVisible}
          setNewAppraisalPeriod={setNewAppraisalPeriod}
          toast={toast}
          setModalVisible={setModalVisible}
          token={token}
          user={user}
          handleProbationAction={handleProbationAction}
          onWayForwardUpdate={(wayForward) => setWayForward(wayForward)}
        />
      )}
    </div>
  );
};

export default HRAppraisalRecord;
