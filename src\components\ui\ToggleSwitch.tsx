import React from "react";

interface ToggleSwitchProps {
  checked: boolean;
  onChange: (newValue: boolean) => void;
  ariaLabel?: string;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  checked,
  onChange,
  ariaLabel,
}) => {
  return (
    <button
      onClick={() => onChange(!checked)}
      className="
        relative inline-flex items-center h-6 w-12
        rounded-full bg-gray-200 dark:bg-gray-700
        transition-colors focus:outline-none focus:ring-2 focus:ring-green-500
      "
      aria-label={ariaLabel || "Toggle"}
    >
      <span
        className={`
          inline-block w-4 h-4 transform bg-white rounded-full shadow transition-transform
          ${checked ? "translate-x-6" : "translate-x-1"}
        `}
      />
    </button>
  );
};

export default ToggleSwitch;
