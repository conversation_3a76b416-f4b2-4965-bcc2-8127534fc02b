import { useEffect, useState } from "react";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { EmployeeEducationalQualification } from "../../types/EmployeeTypes";
import { TabLayout, FormGrid, FormField } from "../TabLayout";
import { GraduationCap, Calendar, Plus, School, Trash2 } from "lucide-react";
import { BASE_URL } from "@/config";

interface QualificationData {
  id?: number;
  institution: string;
  qualification: string;
  graduation_year: string;
  category: "professional" | "Certification" | "Academic"| "Other"| "";
}

interface EducationData {
  id?: number;
  employee_no: string;
  qualifications: QualificationData[];
}

interface EducationTabProps {
  employeeNo: string;
  onSaveSuccess?: () => void;
}

export default function EducationTab({ employeeNo, onSaveSuccess }: EducationTabProps) {
  const token = useSelector((state: RootState) => state.auth.token);
  const [educationData, setEducationData] = useState<EducationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (!token || !employeeNo) {
      return;
    }
    (async function fetchEducation() {
      setLoading(true);
      try {
        const headers = { Authorization: `Token ${token}` };
        const params = { employee_no: employeeNo };
        const res = await axios.get(
          `${BASE_URL}/users/employee-education-qualifications`,
          { headers, params }
        );

        const educationDataObj: EducationData = {
          employee_no: employeeNo,
          qualifications: [],
        };

        if (Array.isArray(res.data)) {
          const records = res.data.filter(
            (item: EmployeeEducationalQualification) => item.employee_no === employeeNo
          );
          if (records.length > 0) {
            records.forEach((record: EmployeeEducationalQualification) => {
              educationDataObj.qualifications.push({
                id: record.id,
                institution: record.instution || "",
                qualification: record.highear_qualificatins || "",
                graduation_year: record.year_of_graduation || "",
                category: record.category || "",
              });
            });
            if (records[0].id) {
              educationDataObj.id = records[0].id;
            }
          }
        }
        setEducationData(educationDataObj);
      } catch (err) {
        setEducationData({
          employee_no: employeeNo,
          qualifications: [],
        });
      } finally {
        setLoading(false);
      }
    })();
  }, [employeeNo, token]);

  // Utility to update a field for a specific qualification
  const updateField = (index: number, field: keyof QualificationData, value: string) => {
    setEducationData((prev) => {
      if (!prev) return prev;
      const updatedQualifications = [...prev.qualifications];
      updatedQualifications[index] = {
        ...updatedQualifications[index],
        [field]: value,
      };
      return { ...prev, qualifications: updatedQualifications };
    });
  };

  // Add a new qualification entry
  const addQualification = () => {
    const currentLen = educationData?.qualifications.length ?? 0;
    setEducationData((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        qualifications: [
          ...prev.qualifications,
          {
            institution: "",
            qualification: "",
            graduation_year: "",
            category: "",
          },
        ],
      };
    });
    setEditingIndex(currentLen);
  };

  // Delete qualification handler
  const handleDeleteConfirm = async () => {
    if (deleteIndex === null || !educationData || !token) return;
    setIsDeleting(true);
    const qualificationToDelete = educationData.qualifications[deleteIndex];
    if (qualificationToDelete.id) {
      try {
        const headers = { Authorization: `Token ${token}` };
        await axios.delete(
          `${BASE_URL}/users/employee-education-qualifications/${qualificationToDelete.id}`,
          { headers }
        );
        toast.success("Qualification deleted successfully");
      } catch (err) {
        toast.error("Failed to delete qualification");
        setIsDeleting(false);
        setDeleteIndex(null);
        return;
      }
    }
    setEducationData((prev) => {
      if (!prev) return prev;
      const updatedQualifications = prev.qualifications.filter((_, i) => i !== deleteIndex);
      return { ...prev, qualifications: updatedQualifications };
    });
    if (editingIndex === deleteIndex) setEditingIndex(null);
    if (onSaveSuccess) onSaveSuccess();
    setIsDeleting(false);
    setDeleteIndex(null);
  };

  const openDeleteDialog = (index: number) => {
    setDeleteIndex(index);
  };

  // Save handler for all qualifications
  async function handleSave() {
    if (!educationData || !token || !employeeNo) return;
    // Validation
    const invalidQualifications = educationData.qualifications.filter(
      (qual) =>
        !qual.institution.trim() ||
        !qual.qualification.trim() ||
        !qual.graduation_year.trim() ||
        !qual.category
    );
    if (invalidQualifications.length > 0) {
      toast.error("Fill in Institution, Qualification, Graduation Year & Category for all entries");
      return;
    }
    try {
      const headers = { Authorization: `Token ${token}` };
      const validQualifications = educationData.qualifications.filter(
        (qual) =>
          qual.institution.trim() &&
          qual.qualification.trim() &&
          qual.graduation_year.trim() &&
          qual.category
      );
      if (validQualifications.length === 0) {
        toast.success("No qualifications to save");
        return;
      }
      // Save or update each qualification
      const savePromises = validQualifications.map(async (qual) => {
        const payload: EmployeeEducationalQualification = {
          employee_no: employeeNo,
          instution: qual.institution.trim(),
          highear_qualificatins: qual.qualification.trim(),
          year_of_graduation: qual.graduation_year,
          category: qual.category as "professional" | "Certification"| "Academic"| "Other",
        };
        if (qual.id) {
          const res = await axios.patch(
            `${BASE_URL}/users/employee-education-qualifications/${qual.id}`,
            payload,
            { headers }
          );
          return res.data;
        } else {
          const res = await axios.post(
            `${BASE_URL}/users/employee-education-qualifications`,
            payload,
            { headers }
          );
          return res.data;
        }
      });
      const results = await Promise.all(savePromises);
      const updatedQualifications: QualificationData[] = results.map((result) => ({
        id: result.id,
        institution: result.instution || "",
        qualification: result.highear_qualificatins || "",
        graduation_year: result.year_of_graduation || "",
        category: result.category || "",
      }));
      setEducationData({
        ...educationData,
        qualifications: updatedQualifications,
      });
      toast.success("Education data saved successfully");
      if (onSaveSuccess) {
        onSaveSuccess();
      }
      setEditingIndex(null);
    } catch (err) {
      toast.error("Failed to save Education data");
    }
  }

  if (!educationData) return null;

  return (
    <TabLayout
      title="Education & Qualifications"
      description="Manage academic and professional qualifications"
      employeeNo={employeeNo}
      loading={loading}
      actions={
        <Button onClick={handleSave} className="flex items-center gap-2">
          <GraduationCap className="h-4 w-4" />
          Save Education
        </Button>
      }
    >
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium flex items-center">
            <School className="mr-2 h-5 w-5 text-primary" />
            Educational Qualifications
          </h3>
          <Button size="sm" variant="outline" onClick={addQualification} className="flex items-center gap-1">
            <Plus className="h-4 w-4" /> Add Qualification
          </Button>
        </div>

        <div className="space-y-4">
          {educationData.qualifications.length === 0 ? (
            <div className="text-center p-6 border border-dashed rounded-lg bg-muted/20">
              <p className="text-muted-foreground">No educational qualifications added yet</p>
              <Button size="sm" variant="outline" onClick={addQualification} className="mt-2">
                Add Qualification
              </Button>
            </div>
          ) : (
            educationData.qualifications.map((item, idx) => (
              <div key={idx} className="border rounded-lg overflow-hidden transition-all hover:shadow-sm">
                <div className="bg-accent/30 px-4 py-2 flex justify-between items-center">
                  <h4 className="font-medium">
                    {item.qualification
                      ? `${item.qualification} - ${item.institution || "Unknown Institution"}`
                      : item.institution || "New Qualification"}
                  </h4>
                  <div className="flex space-x-2">
                    {editingIndex === idx ? (
                      <Button size="sm" variant="ghost" onClick={() => setEditingIndex(null)}>
                        Done
                      </Button>
                    ) : (
                      <>
                        <Button size="sm" variant="ghost" onClick={() => setEditingIndex(idx)}>
                          Edit
                        </Button>
                        <AlertDialog open={deleteIndex === idx} onOpenChange={(open) => !open && setDeleteIndex(null)}>
                          <AlertDialogTrigger asChild>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                              onClick={() => openDeleteDialog(idx)}
                            >
                              <Trash2 className="h-4 w-4 mr-1" />
                              Delete
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Qualification</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete this qualification? This action cannot be undone.
                                {item.qualification && item.institution && (
                                  <div className="mt-2 p-2 bg-muted rounded text-sm">
                                    <strong>{item.qualification}</strong> from {item.institution}
                                  </div>
                                )}
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={handleDeleteConfirm}
                                disabled={isDeleting}
                                className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                              >
                                {isDeleting ? "Deleting..." : "Delete"}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </>
                    )}
                  </div>
                </div>

                {editingIndex === idx ? (
                  <div className="p-4 bg-card">
                    <FormGrid columns={3}>
                      <FormField>
                        <Label>Institution</Label>
                        <Input
                          value={item.institution}
                          onChange={(e) => updateField(idx, "institution", e.target.value)}
                          placeholder="Enter institution name"
                        />
                      </FormField>
                      <FormField>
                        <Label>Qualification</Label>
                        <Input
                          value={item.qualification}
                          onChange={(e) => updateField(idx, "qualification", e.target.value)}
                          placeholder="E.g. Bachelor's Degree, Diploma"
                        />
                      </FormField>
                      <FormField>
                        <Label>Year of Graduation</Label>
                        <div className="relative">
                          <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="date"
                            value={item.graduation_year}
                            onChange={(e) => updateField(idx, "graduation_year", e.target.value)}
                            className="pl-10"
                          />
                        </div>
                      </FormField>
                      <FormField>
                        <Label>Category</Label>
                        <select
                          className="form-select w-full rounded border px-2 py-1"
                          value={item.category || ""}
                          onChange={(e) => updateField(idx, "category", e.target.value)}
                        >
                          <option value="">Select Category</option>
                          <option value="professional">Professional</option>
                          <option value="Certification">Certification</option>
                          <option value="Academic">Academic</option>
                          <option value="Other">Other</option>
                        </select>
                      </FormField>
                    </FormGrid>
                  </div>
                ) : (
                  <div className="p-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Institution</p>
                      <p className="font-medium">{item.institution || "-"}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Qualification</p>
                      <p className="font-medium">{item.qualification || "-"}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Graduation Year</p>
                      <p className="font-medium flex items-center">
                        <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
                        {item.graduation_year || "-"}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Category</p>
                      <p className="font-medium">{item.category || "-"}</p>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </TabLayout>
  );
}
