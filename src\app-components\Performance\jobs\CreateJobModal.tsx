import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';

// Import shadcn/ui components (adjust import paths as needed)
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';

// Import lucide-react icons
import {
  X,
  ArrowLeft,
  ArrowRight,
  Briefcase,
  FileText,
  GraduationCap,
  DollarSign,
  CheckSquare,
  BookOpen,
  History,
  Users,
  Building,
  Badge,
} from 'lucide-react';

const steps = ['Job Basics', 'Requirements', 'Compensation & Details', 'Review'];

const validationSchemas = [
  Yup.object().shape({
    job_title: Yup.string().required('Job title is required'),
    job_description: Yup.string()
      .required('Description is required')
      .min(10, 'Minimum 10 characters'),
    job_responsibilities: Yup.string().required('Responsibilities are required'),
  }),
  Yup.object().shape({
    job_requirements: Yup.string().required('Requirements are required'),
    job_skills: Yup.string().required('Skills are required'),
    accademic_qualification: Yup.string().required('Academic qualification is required'),
    job_qualifications: Yup.string().required('Qualifications are required'),
    job_experience: Yup.string().required('Experience is required'),
    professional_requirements: Yup.string().required('Professional requirements are required'),
  }),
  Yup.object().shape({
    job_min_salary: Yup.number()
      .required('Minimum salary is required')
      .min(0, 'Minimum salary cannot be negative')
      .max(2147483647, 'Value too large'),
    job_max_salary: Yup.number()
      .required('Maximum salary is required')
      .min(Yup.ref('job_min_salary'), 'Must be greater than min salary')
      .max(2147483647, 'Value too large'),
    posission_status: Yup.string()
      .required('Status is required')
      .oneOf(['open', 'closed', 'pending'], 'Invalid status'),
    no_of_employees: Yup.number()
      .required('Number of employees is required')
      .min(1, 'Minimum 1 employee')
      .max(2147483647, 'Value too large'),
    required_proffesional_body: Yup.string().required('Professional body is required'),
    organisation: Yup.number().required('Organization is required'),
    department: Yup.number().required('Department is required'),
  }),
];

interface CreateJobModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: (job: any) => void;
  existingJob?: any;
}

const CreateJobModal = ({ open, onClose, onSuccess }: CreateJobModalProps) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });
  const [organizations, setOrganizations] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [isLoadingOrgs, setIsLoadingOrgs] = useState(false);
  const [isLoadingDepts, setIsLoadingDepts] = useState(false);
  const { token, user } = useSelector((state: RootState) => state.auth);
  const employee_no = user?.employee_no;
  console.log("User: ", user);

  
  const formik = useFormik({
    initialValues: {
      job_title: '',
      job_description: '',
      job_responsibilities: '',
      job_requirements: '',
      job_qualifications: '',
      job_experience: '',
      job_skills: '',
      job_min_salary: 0,
      job_max_salary: 0,
      posission_status: 'open',
      no_of_employees: 1,
      required_proffesional_body: 'None',
      accademic_qualification: '',
      professional_requirements: '',
      organisation: 0,
      department: 0,
    },
    validationSchema: validationSchemas[activeStep],
    onSubmit: async (values) => {
      if (activeStep === steps.length - 1) {
        try {
          setLoading(true);
          const payload = {
            ...values,
            created_by: employee_no || 'EMP/WN/0001',
          };
          // Debug: console.log(payload);
          const response = await axios.post(
            `${BASE_URL}/hrm/job-positions`,
            payload,
            { headers: { Authorization: `Token ${token}` } }
          );

          setSnackbar({
            open: true,
            message: '🎉 Job created successfully!',
            severity: 'success',
          });
          onSuccess(response.data);
          // Debug: console.log(response.data);
          setTimeout(onClose, 1500);
        } catch (error) {
          let message = '🚨 Error creating job position';
          if (axios.isAxiosError(error)) {
            message = `⚠️ ${error.response?.data?.message || message}`;
          }
          setSnackbar({ open: true, message, severity: 'error' });
        } finally {
          setLoading(false);
        }
      } else {
        setActiveStep((prev) => prev + 1);
      }
    },
  });

   // Fetch organizations on component mount
   useEffect(() => {
    if (open) {
      fetchOrganizations();
    }
  }, [open, token]);
  
  // Fetch departments when organization changes
  useEffect(() => {
    if (formik.values.organisation) {
      fetchDepartments(formik.values.organisation);
    } else {
      setDepartments([]);
    }
  }, [formik.values.organisation, token]);
  
  // Function to fetch organizations
  const fetchOrganizations = async () => {
    try {
      setIsLoadingOrgs(true);
      const response = await axios.get(`${BASE_URL}/users/organization`, {
        headers: { Authorization: `Token ${token}` }
      });
      setOrganizations(response.data);
    } catch (error) {
      console.error('Error fetching organizations:', error);
      setSnackbar({
        open: true,
        message: '⚠️ Failed to load organizations',
        severity: 'error'
      });
    } finally {
      setIsLoadingOrgs(false);
    }
  };
  
  // Function to fetch departments based on selected organization
  const fetchDepartments = async (organizationId: number) => {
    try {
      setIsLoadingDepts(true);
      const response = await axios.get(`${BASE_URL}/users/departments?organization_id=${organizationId}`, {
        headers: { Authorization: `Token ${token}` }
      });
      setDepartments(response.data);
    } catch (error) {
      console.error('Error fetching departments:', error);
      setSnackbar({
        open: true,
        message: '⚠️ Failed to load departments',
        severity: 'error'
      });
    } finally {
      setIsLoadingDepts(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  useEffect(() => {
    if (!open) {
      setActiveStep(0);
      formik.resetForm();
    }
  }, [open]);

  const renderCompensationStep = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Section Header */}
        <div className="col-span-2 flex items-center gap-4 p-4 bg-yellow-50 rounded-md">
          <DollarSign size={24} className="text-yellow-600" />
          <h4 className="text-lg font-semibold">Compensation & Details</h4>
        </div>

        {/* Minimum Salary */}
        <div>
          <Label htmlFor="job_min_salary" className="block text-sm font-medium text-gray-700">
            Minimum Salary (KSH) *
          </Label>
          <div className="flex items-center border border-gray-300 rounded-md">
            <span className="px-3 text-gray-900">
              Ksh
            </span>
            <Input
              id="job_min_salary"
              name="job_min_salary"
              type="number"
              value={formik.values.job_min_salary}
              onChange={formik.handleChange}
              placeholder="Min salary"
              className="w-full border-0 focus:ring-0"
            />
          </div>
          {formik.touched.job_min_salary && formik.errors.job_min_salary && (
            <p className="text-red-500 text-sm mt-1">{formik.errors.job_min_salary}</p>
          )}
        </div>

        {/* Maximum Salary */}
        <div>
          <Label htmlFor="job_max_salary" className="block text-sm font-medium text-gray-700">
            Maximum Salary (KSH) *
          </Label>
          <div className="flex items-center border border-gray-300 rounded-md">
            <span className="px-3 text-gray-900">
              Ksh
            </span>
            <Input
              id="job_max_salary"
              name="job_max_salary"
              type="number"
              value={formik.values.job_max_salary}
              onChange={formik.handleChange}
              placeholder="Max salary"
              className="w-full border-0 focus:ring-0"
            /> 
          </div>
          {formik.touched.job_max_salary && formik.errors.job_max_salary && (
            <p className="text-red-500 text-sm mt-1">{formik.errors.job_max_salary}</p>
          )}
        </div>

        {/* Position Status */}
        <div>
          <Label htmlFor="posission_status" className="block text-sm font-medium text-gray-700">
            Position Status *
          </Label>
          <Select
            value={formik.values.posission_status}
            onValueChange={(value) => formik.setFieldValue('posission_status', value)}
          >
            <SelectTrigger id="posission_status">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="open">Open</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
            </SelectContent>
          </Select>
          {formik.touched.posission_status && formik.errors.posission_status && (
            <p className="text-red-500 text-sm mt-1">{formik.errors.posission_status}</p>
          )}
        </div>

        {/* Number of Employees */}
        <div>
          <Label htmlFor="no_of_employees" className="block text-sm font-medium text-gray-700">
            Number of Employees Needed *
          </Label>
          <div className="flex items-center border border-gray-300 rounded-md">
            <span className="px-3 text-gray-500">
              <Users size={20} />
            </span>
            <Input
              id="no_of_employees"
              name="no_of_employees"
              type="number"
              value={formik.values.no_of_employees}
              onChange={formik.handleChange}
              placeholder="Employees needed"
              className="w-full border-0 focus:ring-0"
            />
          </div>
          {formik.touched.no_of_employees && formik.errors.no_of_employees && (
            <p className="text-red-500 text-sm mt-1">{formik.errors.no_of_employees}</p>
          )}
        </div>

        {/* Required Professional Body */}
        <div>
          <Label
            htmlFor="required_proffesional_body"
            className="block text-sm font-medium text-gray-700"
          >
            Required Professional Body *
          </Label>
          <div className="flex items-center border border-gray-300 rounded-md">
            <span className="px-3 text-gray-500">
              <Badge size={20} />
            </span>
            <Input
              id="required_proffesional_body"
              name="required_proffesional_body"
              value={formik.values.required_proffesional_body}
              onChange={formik.handleChange}
              placeholder="Enter professional body"
              className="w-full border-0 focus:ring-0"
            />
          </div>
          {formik.touched.required_proffesional_body && formik.errors.required_proffesional_body && (
            <p className="text-red-500 text-sm mt-1">
              {formik.errors.required_proffesional_body}
            </p>
          )}
        </div>

        {/* Organization Selection - New Dropdown */}
        <div>
          <Label htmlFor="organisation" className="block text-sm font-medium text-gray-700">
            Organization *
          </Label>
          <Select
            value={formik.values.organisation ? formik.values.organisation.toString() : ""}
            onValueChange={(value) => {
              // Reset department when organization changes
              formik.setFieldValue('department', 0);
              formik.setFieldValue('organisation', Number(value));
            }}
            disabled={isLoadingOrgs}
          >
            <SelectTrigger id="organisation" className="w-full">
              <SelectValue placeholder={isLoadingOrgs ? "Loading organizations..." : "Select organization"} />
            </SelectTrigger>
            <SelectContent>
              {organizations.map(org => (
                <SelectItem key={org.id} value={org.id.toString()}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {formik.touched.organisation && formik.errors.organisation && (
            <p className="text-red-500 text-sm mt-1">{formik.errors.organisation}</p>
          )}
        </div>

        {/* Department Selection - New Dropdown */}
        <div>
          <Label htmlFor="department" className="block text-sm font-medium text-gray-700">
            Department *
          </Label>
          <Select
            value={formik.values.department ? formik.values.department.toString() : ""}
            onValueChange={(value) => formik.setFieldValue('department', Number(value))}
            disabled={isLoadingDepts || !formik.values.organisation}
          >
            <SelectTrigger id="department" className="w-full">
              <SelectValue 
                placeholder={
                  !formik.values.organisation 
                    ? "Select organization first" 
                    : isLoadingDepts 
                      ? "Loading departments..." 
                      : "Select department"
                } 
              />
            </SelectTrigger>
            <SelectContent>
              {departments.map(dept => (
                <SelectItem key={dept.id} value={dept.id.toString()}>
                  {dept.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {formik.touched.department && formik.errors.department && (
            <p className="text-red-500 text-sm mt-1">{formik.errors.department}</p>
          )}
        </div>
      </div>
    );
  };

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <div className="grid grid-cols-1 gap-6">
            {/* Section Header */}
            <div className="flex items-center gap-4 p-4 bg-green-50 rounded-md">
              <Briefcase size={24} className="text-green-600" />
              <h4 className="text-lg font-semibold">Basic Job Information</h4>
            </div>

            {/* Job Title */}
            <div>
              <Label htmlFor="job_title" className="block text-sm font-medium text-gray-700">
                Job Title *
              </Label>
              <div className="flex items-center border border-gray-300 rounded-md">
                <span className="px-3 text-gray-500">
                  <Briefcase size={20} />
                </span>
                <Input
                  id="job_title"
                  name="job_title"
                  value={formik.values.job_title}
                  onChange={formik.handleChange}
                  placeholder="Enter job title"
                  className="w-full border-0 focus:ring-0"
                />
              </div>
              {formik.touched.job_title && formik.errors.job_title && (
                <p className="text-red-500 text-sm mt-1">{formik.errors.job_title}</p>
              )}
            </div>
                                                                                                                                          
            {/* Job Description */}
            <div>
              <Label htmlFor="job_description" className="block text-sm font-medium text-gray-700">
                Job Description *
              </Label>
              <div className="flex items-start border border-gray-300 rounded-md">
                <span className="px-3 pt-2 text-gray-500">
                  <FileText size={20} />
                </span>
                <Textarea
                  id="job_description"
                  name="job_description"
                  value={formik.values.job_description}
                  onChange={formik.handleChange}
                  placeholder="Enter job description"
                  className="w-full border-0 focus:ring-0"
                  rows={4}
                />
              </div>
              {formik.touched.job_description && formik.errors.job_description && (
                <p className="text-red-500 text-sm mt-1">{formik.errors.job_description}</p>
              )}
            </div>

            {/* Key Responsibilities */}
            <div>
              <Label
                htmlFor="job_responsibilities"
                className="block text-sm font-medium text-gray-700"
              >
                Key Responsibilities *
              </Label>
              <div className="flex items-start border border-gray-300 rounded-md">
                <span className="px-3 pt-2 text-gray-500">
                  <CheckSquare size={20} />
                </span>
                <Textarea
                  id="job_responsibilities"
                  name="job_responsibilities"
                  value={formik.values.job_responsibilities}
                  onChange={formik.handleChange}
                  placeholder="Enter key responsibilities"
                  className="w-full border-0 focus:ring-0"
                  rows={3}
                />
              </div>
              {formik.touched.job_responsibilities && formik.errors.job_responsibilities && (
                <p className="text-red-500 text-sm mt-1">
                  {formik.errors.job_responsibilities}
                </p>
              )}
            </div>
          </div>
        );

      case 1:
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Section Header */}
            <div className="col-span-2 flex items-center gap-4 p-4 bg-green-50 rounded-md">
              <GraduationCap size={24} className="text-green-600" />
              <h4 className="text-lg font-semibold">Requirements & Qualifications</h4>
            </div>

            {/* Required Skills */}
            <div>
              <Label htmlFor="job_skills" className="block text-sm font-medium text-gray-700">
                Required Skills *
              </Label>
              <div className="flex items-start border border-gray-300 rounded-md">
                <span className="px-3 pt-2 text-gray-500">
                  <BookOpen size={20} />
                </span>
                <Textarea
                  id="job_skills"
                  name="job_skills"
                  value={formik.values.job_skills}
                  onChange={formik.handleChange}
                  placeholder="Enter required skills"
                  className="w-full border-0 focus:ring-0"
                  rows={3}
                />
              </div>
              {formik.touched.job_skills && formik.errors.job_skills && (
                <p className="text-red-500 text-sm mt-1">{formik.errors.job_skills}</p>
              )}
            </div>

            {/* Academic Qualification */}
            <div>
              <Label
                htmlFor="accademic_qualification"
                className="block text-sm font-medium text-gray-700"
              >
                Academic Qualification *
              </Label>
              <div className="flex items-center border border-gray-300 rounded-md">
                <span className="px-3 text-gray-500">
                  <GraduationCap size={20} />
                </span>
                <Input
                  id="accademic_qualification"
                  name="accademic_qualification"
                  value={formik.values.accademic_qualification}
                  onChange={formik.handleChange}
                  placeholder="Enter academic qualification"
                  className="w-full border-0 focus:ring-0"
                />
              </div>
              {formik.touched.accademic_qualification && formik.errors.accademic_qualification && (
                <p className="text-red-500 text-sm mt-1">
                  {formik.errors.accademic_qualification}
                </p>
              )}
            </div>

            {/* Job Qualifications */}
            <div>
              <Label
                htmlFor="job_qualifications"
                className="block text-sm font-medium text-gray-700"
              >
                Job Qualifications *
              </Label>
              <div className="flex items-start border border-gray-300 rounded-md">
                <span className="px-3 pt-2 text-gray-500">
                  <CheckSquare size={20} />
                </span>
                <Textarea
                  id="job_qualifications"
                  name="job_qualifications"
                  value={formik.values.job_qualifications}
                  onChange={formik.handleChange}
                  placeholder="Enter job qualifications"
                  className="w-full border-0 focus:ring-0"
                  rows={3}
                />
              </div>
              {formik.touched.job_qualifications && formik.errors.job_qualifications && (
                <p className="text-red-500 text-sm mt-1">
                  {formik.errors.job_qualifications}
                </p>
              )}
            </div>

            {/* Job Experience */}
            <div>
              <Label htmlFor="job_experience" className="block text-sm font-medium text-gray-700">
                Job Experience *
              </Label>
              <div className="flex items-start border border-gray-300 rounded-md">
                <span className="px-3 pt-2 text-gray-500">
                  <History size={20} />
                </span>
                <Textarea
                  id="job_experience"
                  name="job_experience"
                  value={formik.values.job_experience}
                  onChange={formik.handleChange}
                  placeholder="Enter job experience"
                  className="w-full border-0 focus:ring-0"
                  rows={3}
                />
              </div>
              {formik.touched.job_experience && formik.errors.job_experience && (
                <p className="text-red-500 text-sm mt-1">{formik.errors.job_experience}</p>
              )}
            </div>

            {/* Detailed Requirements */}
            <div className="col-span-2">
              <Label htmlFor="job_requirements" className="block text-sm font-medium text-gray-700">
                Detailed Requirements *
              </Label>
              <div className="flex items-start border border-gray-300 rounded-md">
                <span className="px-3 pt-2 text-gray-500">
                  <FileText size={20} />
                </span>
                <Textarea
                  id="job_requirements"
                  name="job_requirements"
                  value={formik.values.job_requirements}
                  onChange={formik.handleChange}
                  placeholder="Enter detailed requirements"
                  className="w-full border-0 focus:ring-0"
                  rows={4}
                />
              </div>
              {formik.touched.job_requirements && formik.errors.job_requirements && (
                <p className="text-red-500 text-sm mt-1">{formik.errors.job_requirements}</p>
              )}
            </div>

            {/* Professional Requirements */}
            <div className="col-span-2">
              <Label
                htmlFor="professional_requirements"
                className="block text-sm font-medium text-gray-700"
              >
                Professional Requirements *
              </Label>
              <div className="flex items-start border border-gray-300 rounded-md">
                <span className="px-3 pt-2 text-gray-500">
                  <Users size={20} />
                </span>
                <Textarea
                  id="professional_requirements"
                  name="professional_requirements"
                  value={formik.values.professional_requirements}
                  onChange={formik.handleChange}
                  placeholder="Enter professional requirements"
                  className="w-full border-0 focus:ring-0"
                  rows={3}
                />
              </div>
              {formik.touched.professional_requirements &&
                formik.errors.professional_requirements && (
                  <p className="text-red-500 text-sm mt-1">
                    {formik.errors.professional_requirements}
                  </p>
                )}
            </div>
          </div>
        );

      case 2:
        return renderCompensationStep();
        case 3:
          // Update the review step to remove organogram_level reference
          return (
            <div className="grid grid-cols-1 gap-6">
              {/* Section Header */}
              <div className="flex items-center gap-4 p-4 bg-purple-50 rounded-md">
                <FileText size={24} className="text-purple-600" />
                <h4 className="text-lg font-semibold">Review & Submit</h4>
              </div>
              {/* Review Box */}
              <div className="p-4 bg-gray-100 rounded-md">
                <p className="mb-2">
                  <strong>Job Title:</strong> {formik.values.job_title}
                </p>
                <hr className="my-2" />
                <p className="mb-2">
                  <strong>Description:</strong> {formik.values.job_description}
                </p>
                <p className="mb-2">
                  <strong>Responsibilities:</strong> {formik.values.job_responsibilities}
                </p>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm">
                      <strong>Salary Range:</strong> KSH{formik.values.job_min_salary} - KSH
                      {formik.values.job_max_salary}
                    </p>
                    <p className="text-sm mt-1">
                      <strong>Employees Needed:</strong> {formik.values.no_of_employees}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm flex items-center">
                      <strong>Status:</strong>
                      <span
                        className={`ml-1 px-2 py-1 rounded text-xs ${
                          formik.values.posission_status === 'open'
                            ? 'bg-green-200 text-green-800'
                            : formik.values.posission_status === 'closed'
                            ? 'bg-red-200 text-red-800'
                            : 'bg-yellow-200 text-yellow-800'
                        }`}
                      >
                        {formik.values.posission_status}
                      </span>
                    </p>
                    <p className="text-sm mt-1">
                      <strong>Professional Body:</strong>{' '}
                      {formik.values.required_proffesional_body}
                    </p>
                  </div>
                </div>
                <hr className="my-2" />
                <p className="text-sm mt-1">
                  <strong>Academic Qualification:</strong> {formik.values.accademic_qualification}
                </p>
                <p className="text-sm mt-1">
                  <strong>Organization:</strong> {
                    organizations.find(org => org.id === formik.values.organisation)?.name || 'Not selected'
                  }
                </p>
                <p className="text-sm mt-1">
                  <strong>Department:</strong> {
                    departments.find(dept => dept.id === formik.values.department)?.name || 'Not selected'
                  }
                </p>
              </div>
            </div>
          );
        default:
          return 'Unknown step';
      }
    };

  return (
    <>
      <Dialog open={open} onOpenChange={(openVal) => !openVal && onClose()}>
        <DialogContent className="max-w-3xl w-full p-0">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden flex flex-col h-full">
            {/* Header */}
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-xl font-semibold">Create New Job Position</h3>
              <button onClick={onClose} className="p-2 rounded hover:bg-gray-100">
                <X size={2} />
              </button>
            </div>
            {/* Content */}
            <div className="flex-1 p-4 overflow-y-auto max-h-[80vh]">
              {/* Stepper */}
              <div className="flex items-center justify-between">
                {steps.map((label, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className={`w-10 h-10 flex items-center justify-center rounded-full ${
                        index <= activeStep
                          ? 'bg-green-600 text-white'
                          : 'bg-gray-200 text-gray-500'
                      }`}
                    >
                      {index + 1}
                    </div>
                    <span className="mt-2 text-xs text-center">{label}</span>
                  </div>
                ))}
              </div>
              {/* Animated Step Content */}
              <div className="mt-6">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeStep}
                    initial={{ opacity: 0, x: activeStep === 3 ? 50 : -50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: activeStep === 3 ? -50 : 50 }}
                    transition={{ duration: 0.3 }}
                  >
                    {getStepContent(activeStep)}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
            {/* Actions */}
            <div className="flex justify-between items-center p-4 border-t">
              <Button
                variant="outline"
                onClick={activeStep === 0 ? onClose : handleBack}
                disabled={loading}
              >
                {activeStep === 0 ? (
                  'Cancel'
                ) : (
                  <>
                    Back <ArrowLeft size={16} className="ml-2" />
                  </>
                )}
              </Button>
              <Button
                onClick={() => formik.handleSubmit()}
                disabled={loading || !formik.isValid}
                className="flex items-center"
              >
                {loading ? (
                  <svg
                    className="animate-spin mr-2 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                    ></path>
                  </svg>
                ) : activeStep === steps.length - 1 ? (
                  'Submit Job'
                ) : (
                  <>
                    Continue <ArrowRight size={16} className="ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Toast Snackbar */}
      {snackbar.open && (
        <div className="fixed top-4 right-4 z-50">
          <div
            className={`flex items-center p-4 rounded shadow ${
              snackbar.severity === 'success' ? 'bg-green-500' : 'bg-red-500'
            } text-white`}
          >
            <span className="mr-2">
              {snackbar.severity === 'success' ? '🎉' : '⚠️'}
            </span>
            <span>{snackbar.message}</span>
            <button onClick={handleCloseSnackbar} className="ml-4">
              <X size={16} />
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default CreateJobModal;
