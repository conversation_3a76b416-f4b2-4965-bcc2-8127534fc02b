import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { BASE_URL } from "@/config";

interface UserOrgData {
  department_id: number;
  department_name: string;
  group_id: number;
  group_name: string;
  organisation_id: number;
  organisation_name: string;
}

interface UserBio {
  first_name: string | null;
  middle_name: string | null;
  last_name: string | null;
  id_number: string | null;
}

interface UserContacts {
  phone: string | null;
  company_email: string | null;
  personal_email: string | null;
}

interface UserJobInfo {
  job_title_code: string | null;
  job_title: string | null;
}

interface UserResponse {
  // Basic fields
  employee_no?: string;
  AccessToken?: string;
  RefreshToken?: string;
  // Extended / nested fields
  userOrgData?: UserOrgData;
  UserBio?: UserBio;
  UserContacts?: UserContacts;
  UserJobInfo?: UserJobInfo;
}

interface AuthState {
  user: UserResponse | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  resetStatus: {
    loading: boolean;
    success: boolean;
    error: string | null;
  },
  passwordChangeRequired: boolean;
}

const userString = localStorage.getItem("user");
const tokenString = localStorage.getItem("authToken");

const validUser =
  userString && userString !== "undefined" ? JSON.parse(userString) : null;
const validToken =
  tokenString && tokenString !== "undefined" ? tokenString : null;

const initialState: AuthState = {
  user: validUser,
  token: validToken,
  loading: false,
  error: null,
  resetStatus: {
    loading: false,
    success: false,
    error: null,
  },
  passwordChangeRequired: false,
};

function getErrorMessage(error: unknown): string {
  if (axios.isAxiosError(error)) {
    return error.response?.data?.message || "An error occurred";
  } else if (error instanceof Error) {
    return error.message;
  }
  return "An unexpected error occurred";
}

export const loginUser = createAsyncThunk(
  "auth/loginUser",
  async (
    credentials: { username: string; password: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post(`${BASE_URL}/users/login`, {
        username: credentials.username,
        password: credentials.password,
      });

      console.log("Login response data:", response.data); // Log entire response

      const {
        Employee_no,
        AccessToken,
        RefreshToken,
        userOrgData,
        UserBio,
        UserContacts,
        UserJobInfo,
      } = response.data;
      const isDefaultPassword = credentials.password === "12345678"
      return {
        user: {
          employee_no: Employee_no,
          userOrgData,
          UserBio,
          UserContacts,
          UserJobInfo,
          RefreshToken,
        },
        token: AccessToken,
        passwordChangeRequired: isDefaultPassword,
      };
    } catch (error) {
      if (axios.isAxiosError(error)){
        return rejectWithValue(error.response?.data?.message || "Failed to log in");
      }
      return rejectWithValue("An unexpected error occurred");
    }
  }
);

export const registerUser = createAsyncThunk(
  "auth/registerUser",
  async (
    formData: {
      email: string;
      password: string;
      organisation_id: number;
      department_id: number;
      group_id: number;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post(`${BASE_URL}/users/registration`, {
        user: {
          email: formData.email,
          password: formData.password,
          organisation_id: formData.organisation_id,
          department_id: formData.department_id,
          group_id: formData.group_id,
        },
      });

      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error));
    }
  }
);

// New thunk for sending password reset link
export const sendPasswordResetLink = createAsyncThunk(
  "auth/sendPasswordResetLink",
  async (email: string, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${BASE_URL}/users/reset-link`, {
        email,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error));
    }
  }
);

// New thunk for resetting password
export const resetPassword = createAsyncThunk(
  "auth/resetPassword",
  async (
    data: { token: string; newPassword: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post(`${BASE_URL}/users/reset-password`, {
        token: data.token,
        newPassword: data.newPassword,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(getErrorMessage(error));
    }
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.error = null;
      localStorage.clear();
    },
    loadUserFromLocalStorage: (state) => {
      const newUserString = localStorage.getItem("user");
      const newTokenString = localStorage.getItem("authToken");
      state.user =
        newUserString && newUserString !== "undefined"
          ? JSON.parse(newUserString)
          : null;
      state.token =
        newTokenString && newTokenString !== "undefined"
          ? newTokenString
          : null;
    },
    resetPasswordStatus: (state) => {
      state.resetStatus = {
        loading: false,
        success: false,
        error: null,
      };
    },
    clearPasswordChangeRequired: (state) => {
      state.passwordChangeRequired = false;
    }
  },
  extraReducers: (builder) => {
    // ---------- LOGIN ----------
    builder.addCase(loginUser.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(loginUser.fulfilled, (state, action) => {
      state.loading = false;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.passwordChangeRequired = action.payload.passwordChangeRequired;
      
      // Only store in localStorage if password change is not required
      if (!action.payload.passwordChangeRequired) {
        localStorage.setItem("authToken", action.payload.token || "");
        localStorage.setItem("user", JSON.stringify(action.payload.user));
      }
    });
    builder.addCase(loginUser.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // ---------- REGISTER ----------
    builder.addCase(registerUser.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(registerUser.fulfilled, (state, action) => {
      state.loading = false;
      state.user = action.payload;
      localStorage.setItem("user", JSON.stringify(action.payload));
    });
    builder.addCase(registerUser.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    
    // ---------- SEND PASSWORD RESET LINK ----------
    builder.addCase(sendPasswordResetLink.pending, (state) => {
      state.resetStatus.loading = true;
      state.resetStatus.error = null;
      state.resetStatus.success = false;
    });
    builder.addCase(sendPasswordResetLink.fulfilled, (state) => {
      state.resetStatus.loading = false;
      state.resetStatus.success = true;
    });
    builder.addCase(sendPasswordResetLink.rejected, (state, action) => {
      state.resetStatus.loading = false;
      state.resetStatus.error = action.payload as string;
    });
    
    // ---------- RESET PASSWORD ----------
    builder.addCase(resetPassword.pending, (state) => {
      state.resetStatus.loading = true;
      state.resetStatus.error = null;
      state.resetStatus.success = false;
    });
    builder.addCase(resetPassword.fulfilled, (state) => {
      state.resetStatus.loading = false;
      state.resetStatus.success = true;
      state.passwordChangeRequired = false;
    });
    builder.addCase(resetPassword.rejected, (state, action) => {
      state.resetStatus.loading = false;
      state.resetStatus.error = action.payload as string;
    });
  },
});

export const { logout, loadUserFromLocalStorage, resetPasswordStatus, clearPasswordChangeRequired } = authSlice.actions;
export default authSlice.reducer;