import React, { useState, useEffect } from "react";
import dayjs, { Dayjs } from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { BASE_URL } from "../../../config";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

import { Plus, Trash } from "lucide-react";
import ToggleSwitch from "@/components/ui/ToggleSwitch";
import { Checkbox } from "@/components/ui/checkbox";

export interface SpecialEvent {
  date: string;
  title: string;
  reason?: string;
  non_working_day: boolean;
  is_public_holiday: boolean;
  leave_restricted_day: boolean;
}

export interface OrganisationCalendar {
  id?: number;
  calendar_name: string;
  calendar_description: string;
  calendar_start_date: string;
  calendar_end_date: string;
  organisation: number;
  special_days: SpecialEvent[];
}

interface CalendarEventFormProps {
  open: boolean;
  onClose: () => void;
  initialData?: OrganisationCalendar;
  onSave: (data: OrganisationCalendar) => Promise<void>;
}

const CalendarEventForm: React.FC<CalendarEventFormProps> = ({
  open,
  onClose,
  initialData,
  onSave,
}) => {
  const [calendarName, setCalendarName] = useState("");
  const [calendarDescription, setCalendarDescription] = useState("");
  const [startDate, setStartDate] = useState<Dayjs | null>(null);
  const [endDate, setEndDate] = useState<Dayjs | null>(null);
  const [specialEvents, setSpecialEvents] = useState<SpecialEvent[]>([]);
  const [organisation, setOrganisation] = useState<number | null>(null);

  const [everySundayNonWorking, setEverySundayNonWorking] = useState(false);
  const [everySaturdayNonWorking, setEverySaturdayNonWorking] = useState(false);

  const [organisations, setOrganisations] = useState<{ id: number; name: string }[]>([]);
  const [error, setError] = useState("");

  const { token } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    const fetchOrganisations = async () => {
      try {
        const response = await fetch(`${BASE_URL}/users/organization`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!response.ok) throw new Error("Failed to fetch organizations");
        const data = await response.json();
        setOrganisations(data);
      } catch {
        setError("Failed to fetch organizations.");
      }
    };
    fetchOrganisations();
  }, [token]);

  useEffect(() => {
    if (initialData) {
      setCalendarName(initialData.calendar_name);
      setCalendarDescription(initialData.calendar_description);
      setStartDate(dayjs(initialData.calendar_start_date));
      setEndDate(dayjs(initialData.calendar_end_date));
      setSpecialEvents(initialData.special_days || []);
      setOrganisation(initialData.organisation);
      setEverySundayNonWorking(false);
      setEverySaturdayNonWorking(false);
    } else {
      setCalendarName("");
      setCalendarDescription("");
      setStartDate(null);
      setEndDate(null);
      setSpecialEvents([]);
      setOrganisation(null);
      setEverySundayNonWorking(false);
      setEverySaturdayNonWorking(false);
    }
    setError("");
  }, [initialData]);

  const handleAddSpecialEvent = () => {
    setSpecialEvents((prev) => [
      ...prev,
      {
        date: "",
        title: "",
        reason: "",
        non_working_day: false,
        is_public_holiday: false,
        leave_restricted_day: false,
      },
    ]);
  };

  const handleSpecialEventChange = (
    index: number,
    key: keyof SpecialEvent,
    value: string | boolean
  ) => {
    const updatedEvents = [...specialEvents];
    updatedEvents[index] = { ...updatedEvents[index], [key]: value };
    setSpecialEvents(updatedEvents);
  };

  const handleRemoveSpecialEvent = (index: number) => {
    setSpecialEvents(specialEvents.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    setError("");

    if (!calendarName.trim()) {
      setError("Please enter a calendar name.");
      return;
    }
    if (!calendarDescription.trim()) {
      setError("Please enter a calendar description.");
      return;
    }
    if (!startDate || !endDate) {
      setError("Please select start and end dates.");
      return;
    }
    if (startDate.isAfter(endDate)) {
      setError("Start date cannot be after end date.");
      return;
    }
    if (!organisation) {
      setError("Please select an organization.");
      return;
    }

    let validSpecialDays = specialEvents.filter((e) => e.date && e.title);

    if (everySundayNonWorking) {
      let d = startDate.clone();
      while (d.isSameOrBefore(endDate, "day")) {
        if (d.day() === 0) {
          const dateStr = d.format("YYYY-MM-DD");
          if (!validSpecialDays.some((ev) => ev.date === dateStr)) {
            validSpecialDays.push({
              date: dateStr,
              title: "Non-working Sunday",
              reason: "",
              non_working_day: true,
              is_public_holiday: false,
              leave_restricted_day: false,
            });
          }
        }
        d = d.add(1, "day");
      }
    }

    if (everySaturdayNonWorking) {
      let d = startDate.clone();
      while (d.isSameOrBefore(endDate, "day")) {
        if (d.day() === 6) {
          const dateStr = d.format("YYYY-MM-DD");
          if (!validSpecialDays.some((ev) => ev.date === dateStr)) {
            validSpecialDays.push({
              date: dateStr,
              title: "Non-working Saturday",
              reason: "",
              non_working_day: true,
              is_public_holiday: false,
              leave_restricted_day: false,
            });
          }
        }
        d = d.add(1, "day");
      }
    }

    const formObj: OrganisationCalendar = {
      calendar_name: calendarName.trim(),
      calendar_description: calendarDescription.trim(),
      calendar_start_date: startDate.format("YYYY-MM-DD"),
      calendar_end_date: endDate.format("YYYY-MM-DD"),
      organisation,
      special_days: validSpecialDays,
    };

    try {
      await onSave(formObj);
      onClose();
    } catch {
      setError("Failed to save calendar. Please try again.");
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent
        className="max-w-3xl w-full max-h-[80vh] overflow-y-auto"
        aria-describedby="calendar-event-form-description"
      >
        <DialogHeader>
          <DialogTitle>
            {initialData ? "Edit Calendar" : "Create Calendar"}
          </DialogTitle>
        </DialogHeader>

        <p id="calendar-event-form-description" className="sr-only">
          Fill out the form to create or edit a calendar event.
        </p>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="flex flex-col">
            <label className="block text-sm font-medium text-gray-700">
              Calendar Name
            </label>
            <Input
              value={calendarName}
              onChange={(e) => setCalendarName(e.target.value)}
              className="mt-1"
              placeholder="Enter calendar name"
            />
          </div>
          <div className="flex flex-col">
            <label className="block text-sm font-medium text-gray-700">
              Calendar Description
            </label>
            <textarea
              value={calendarDescription}
              onChange={(e) => setCalendarDescription(e.target.value)}
              className="mt-1 block w-full h-10 resize-none rounded-md border border-gray-300 p-2"
              placeholder="Enter calendar description"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div className="flex flex-col">
            <label className="block text-sm font-medium text-gray-700">
              Organization
            </label>
            <select
              value={organisation || ""}
              onChange={(e) => setOrganisation(Number(e.target.value))}
              className="mt-1 block w-full rounded-md border border-gray-300 p-2"
            >
              <option value="" disabled>
                Select organization
              </option>
              {organisations.map((org) => (
                <option key={org.id} value={org.id}>
                  {org.name}
                </option>
              ))}
            </select>
          </div>
          <div className="flex flex-col">
            <label className="block text-sm font-medium text-gray-700">
              Start Date
            </label>
            <Input
              type="date"
              value={startDate ? startDate.format("YYYY-MM-DD") : ""}
              onChange={(e) => setStartDate(dayjs(e.target.value))}
              className="mt-1"
            />
          </div>
          <div className="flex flex-col">
            <label className="block text-sm font-medium text-gray-700">
              End Date
            </label>
            <Input
              type="date"
              value={endDate ? endDate.format("YYYY-MM-DD") : ""}
              onChange={(e) => setEndDate(dayjs(e.target.value))}
              className="mt-1"
            />
          </div>
        </div>

        <div className="mt-4 flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="sunday-non-working"
              checked={everySundayNonWorking}
              onCheckedChange={(checked: boolean) => setEverySundayNonWorking(checked)}
            />
            <label
              htmlFor="sunday-non-working"
              className="text-sm font-medium"
            >
              Every Sunday is Non-working
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="saturday-non-working"
              checked={everySaturdayNonWorking}
              onCheckedChange={(checked: boolean) => setEverySaturdayNonWorking(checked)}
            />
            <label
              htmlFor="saturday-non-working"
              className="text-sm font-medium"
            >
              Every Saturday is Non-working
            </label>
          </div>
        </div>

        <div className="mt-6">
          <p className="text-lg font-semibold">Special Days / Events</p>
          <p className="text-sm text-gray-500">
            Configure any special or non-working days, public holidays, or restricted days.
          </p>

          {everySundayNonWorking && (
            <div className="mt-4 border rounded-md p-4 bg-gray-50">
              <p className="text-sm font-medium text-gray-700">
                All Sundays are non-working days.
              </p>
            </div>
          )}
          {everySaturdayNonWorking && (
            <div className="mt-4 border rounded-md p-4 bg-gray-50">
              <p className="text-sm font-medium text-gray-700">
                All Saturdays are non-working days.
              </p>
            </div>
          )}

          {specialEvents.map((event, index) => (
            <div key={index} className="mt-4 border rounded-md p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex flex-col">
                  <label className="block text-sm font-medium text-gray-700">
                    Date
                  </label>
                  <Input
                    type="date"
                    value={event.date || ""}
                    onChange={(e) =>
                      handleSpecialEventChange(index, "date", e.target.value)
                    }
                    className="mt-1"
                  />
                </div>
                <div className="flex flex-col">
                  <label className="block text-sm font-medium text-gray-700">
                    Title
                  </label>
                  <Input
                    value={event.title}
                    onChange={(e) =>
                      handleSpecialEventChange(index, "title", e.target.value)
                    }
                    className="mt-1"
                    placeholder="e.g. Company Event"
                  />
                </div>
                <div className="flex flex-col">
                  <label className="block text-sm font-medium text-gray-700">
                    Reason (Optional)
                  </label>
                  <Input
                    value={event.reason || ""}
                    onChange={(e) =>
                      handleSpecialEventChange(index, "reason", e.target.value)
                    }
                    className="mt-1"
                    placeholder="Reason if needed"
                  />
                </div>
              </div>
              <div className="flex items-center justify-between gap-4 mt-4">
                <div className="flex items-center gap-6">
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-700">
                      Non-working
                    </label>
                    <ToggleSwitch
                      checked={event.non_working_day}
                      onChange={(checked) =>
                        handleSpecialEventChange(index, "non_working_day", checked)
                      }
                      ariaLabel="Toggle non-working day"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-700">
                      Public Holiday
                    </label>
                    <ToggleSwitch
                      checked={event.is_public_holiday}
                      onChange={(checked) =>
                        handleSpecialEventChange(index, "is_public_holiday", checked)
                      }
                      ariaLabel="Toggle public holiday"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-700">
                      Restricted
                    </label>
                    <ToggleSwitch
                      checked={event.leave_restricted_day}
                      onChange={(checked) =>
                        handleSpecialEventChange(index, "leave_restricted_day", checked)
                      }
                      ariaLabel="Toggle restricted day"
                    />
                  </div>
                </div>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleRemoveSpecialEvent(index)}
                  className="flex items-center gap-1"
                >
                  <Trash className="w-4 h-4" />
                  Remove
                </Button>
              </div>
            </div>
          ))}

          <Button
            variant="outline"
            onClick={handleAddSpecialEvent}
            className="mt-3 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" /> Add Special Day
          </Button>
        </div>

        <DialogFooter className="mt-6">
          <div className="flex justify-end space-x-2">
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button variant="default" onClick={handleSubmit}>
              Save
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CalendarEventForm;
