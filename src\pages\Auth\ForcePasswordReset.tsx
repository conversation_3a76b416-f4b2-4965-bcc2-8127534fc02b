import React from "react";
import { useLocation, useNavigate, <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Mail } from "lucide-react";

const ForcePasswordReset: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const email = location.state?.email || "your email";

  return (
    <div className="flex w-full min-h-screen">
      {/* Left side */}
      <div 
        className="hidden lg:flex lg:w-1/2 justify-center items-center relative"
        style={{
          backgroundImage: "url(https://lh3.googleusercontent.com/p/AF1QipN0diuh8haESrAhd2TclRU5rQ3Qp2OlzstOU2BR=s680-w680-h510)",
          backgroundSize: "cover",
          backgroundPosition: "center"
        }}
      >
      </div>

      {/* Content */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-4 bg-gray-50 dark:bg-gray-900">
        <div className="w-full max-w-md px-8 py-10 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
          <div className="flex justify-center mb-6">
            <img 
              src="https://workspace.optiven.co.ke/static/media/optiven-logo-full.f498da6255572ff1ab8a.png" 
              alt="Optiven Limited Logo" 
              className="h-16" 
            />
          </div>
          
          <div className="text-center space-y-4">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 text-green-600 mb-4">
              <Mail className="h-8 w-8" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Password Reset Required
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              For security reasons, you need to reset your default password before accessing the system.
            </p>
            <div className="border border-green-200 bg-green-50 dark:bg-green-900/30 dark:border-green-900 p-4 rounded-lg mt-4">
              <p className="text-gray-700 dark:text-gray-300">
                We've sent a password reset link to <span className="font-medium">{email}</span>
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Please check your inbox and follow the instructions to set a new password.
              </p>
            </div>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Note: The link will expire in 30 minutes.
            </p>
          </div>

          <div className="mt-8 space-y-4">
            <Button
              onClick={() => navigate("/login")}
              className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 text-white font-medium rounded-md transition-colors"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Return to Login
            </Button>
          </div>
          
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-500">
              © {new Date().getFullYear()} Optiven Limited. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForcePasswordReset;