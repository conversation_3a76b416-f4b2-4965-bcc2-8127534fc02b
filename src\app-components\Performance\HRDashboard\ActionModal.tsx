
import React from 'react';
import { X } from 'lucide-react';
import { BASE_URL } from '@/config';
import { AppraisalPeriodForm } from './types';



interface ActionModalProps {
  selectedAction: any;
  selectedRecordDetails: any;
  employeeContractInfo: any;
  newAppraisalPeriod: AppraisalPeriodForm;
  setActionModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  setNewAppraisalPeriod: React.Dispatch<React.SetStateAction<AppraisalPeriodForm>>;
  toast: (args: any) => void;
  setModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  token: any;
  user: any;
  handleProbationAction: () => any[];
  onWayForwardUpdate?: (wayForward: string) => void;
}

const ActionModal: React.FC<ActionModalProps> = ({
  selectedAction,
  selectedRecordDetails,
  employeeContractInfo,
  newAppraisalPeriod,
  setActionModalVisible,
  setNewAppraisalPeriod,
  toast,
  setModalVisible,
  token,
  handleProbationAction,
  user,
  onWayForwardUpdate,
}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  console.log("Employee Contract Info", employeeContractInfo);

  const handleContractUpdate = async () => {
    const currentAction = handleProbationAction().find((a) => a.label === selectedAction);
    if (!currentAction) return;

    setIsLoading(true);
    try {

      if (currentAction.contractChange) {
        const contractUpdatePayload = {
          employee_no: selectedRecordDetails?.employeeid,
          ...currentAction.contractUpdate
        };

        console.log("Updating contract with payload:", contractUpdatePayload);

        const contractRes = await fetch(`${BASE_URL}/users/employee-contract-info-details/${employeeContractInfo[0].id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Token ${token}`,
          },
          body: JSON.stringify(contractUpdatePayload),
        });

        if (!contractRes.ok) {
          const errorData = await contractRes.json();
          console.error("Contract update failed:", errorData);
          throw new Error(`Contract update failed: ${JSON.stringify(errorData)}`);
        }

        console.log("Contract update successful");
      }

      const checkActiveAppraisalsRes = await fetch(
        `${BASE_URL}/hrm/employee-appraisals?employeeid=${selectedRecordDetails?.employeeid}&is_active=true`,
        {
          headers: {
            Authorization: `Token ${token}`,
          },
        }
      );

      if (!checkActiveAppraisalsRes.ok) {
        const errorData = await checkActiveAppraisalsRes.json();
        console.error("Failed to check active appraisals:", errorData);
        throw new Error(`Failed to check active appraisals: ${JSON.stringify(errorData)}`);
      }

      const activeAppraisals = await checkActiveAppraisalsRes.json();
      console.log("Active appraisals found:", activeAppraisals);

      for (const appraisal of activeAppraisals) {
        const updatePayload = {
          is_active: false,
          status: "Completed",
          way_forward: currentAction.autoPopulateWayForward || "",
          closed_date: new Date().toISOString().split('T')[0],
          closed_by: user?.employee_no || 'SYSTEM',
        };

        console.log(`Marking appraisal ${appraisal.id} as inactive with payload:`, updatePayload);

        const updateRes = await fetch(`${BASE_URL}/hrm/employee-appraisals/${appraisal.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Token ${token}`,
          },
          body: JSON.stringify(updatePayload),
        });

        if (!updateRes.ok) {
          const errorData = await updateRes.json();
          console.error(`Failed to update appraisal ${appraisal.id}:`, errorData);
          throw new Error(`Failed to update appraisal: ${JSON.stringify(errorData)}`);
        }

        console.log(`Successfully marked appraisal ${appraisal.id} as inactive`);
      }


      if (!newAppraisalPeriod.name || newAppraisalPeriod.name.trim() === "") {
        const actionType = currentAction.type.replace('_', ' ').toLowerCase();
        const today = new Date().toISOString().split('T')[0];

        newAppraisalPeriod.name = `${actionType} - ${selectedRecordDetails?.employeeid} (${today})`;
      }

      const newAppraisalPayload = {
        name: newAppraisalPeriod.name,
        start_date: newAppraisalPeriod.start_date || new Date().toISOString().split('T')[0],
        end_date: newAppraisalPeriod.end_date || null,
        cycle_type: newAppraisalPeriod.cycle_type || "ANNUAL",
        description: newAppraisalPeriod.description || `${currentAction.label} action created on ${new Date().toDateString()}`,
        date_created: new Date().toISOString().split('T')[0],
        closed_date: null,
        status: "Open",
        employee_acceptance: false,
        supervisor_acceptance: false,
        total_emp_self_rating_score: null,
        total_supervisor_rating_score: null,
        final_hr_comments: null,
        way_forward: null,
        is_active: true,
        created_by: user?.employee_no || 'SYSTEM',
        closed_by: null,
        employeeid: selectedRecordDetails?.employeeid,
        supervisor: selectedRecordDetails?.supervisor,
        period_id: selectedRecordDetails?.period_id || null,
      };

      console.log("Selected Record Details:", selectedRecordDetails);
      console.log("Creating new appraisal with payload:", newAppraisalPayload);

      const appraisalRes = await fetch(`${BASE_URL}/hrm/employee-appraisals`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Token ${token}`,
        },
        body: JSON.stringify(newAppraisalPayload),
      });

      if (!appraisalRes.ok) {
        const errorData = await appraisalRes.json();
        console.error("Appraisal creation failed:", errorData);

        // Extract meaningful error message
        let errorMessage = 'Failed to create new appraisal period';
        if (errorData.created_by && Array.isArray(errorData.created_by)) {
          errorMessage = errorData.created_by[0] || errorMessage;
        } else if (errorData.error) {
          errorMessage = errorData.error;
        } else if (errorData.detail) {
          errorMessage = errorData.detail;
        }

        alert(`❌ Appraisal Creation Failed: ${errorMessage}`);

        throw new Error(`Appraisal creation failed: ${JSON.stringify(errorData)}`);
      }

      const newAppraisalData = await appraisalRes.json();
      console.log("Successfully created new appraisal period:", newAppraisalData);

      alert(`✅ Action Completed Successfully!\n\n${currentAction.label} action has been processed successfully.\n\nNew appraisal period "${newAppraisalPeriod.name}" has been created and is now active.\n\nThe way forward has been auto-generated. You can now approve the appraisal record.`);

      // Update the way forward in the parent component
      if (onWayForwardUpdate && currentAction.autoPopulateWayForward) {
        onWayForwardUpdate(currentAction.autoPopulateWayForward);
      }

      // Only close the action modal, keep the main modal open for HR to approve the old record
      setActionModalVisible(false);
      // setModalVisible(false); // Keep this commented to allow HR to approve the old record
    } catch (error: any) {
      console.error("Error in handleContractUpdate:", error);

      // Provide specific error messages based on the error type
      if (error.message && error.message.includes("active appraisal cycle already exists")) {
        alert('❌ Appraisal Cycle Error\n\nAn active appraisal cycle already exists for this employee. Please close the existing cycle first.');
      } else if (error.message && error.message.includes("Appraisal creation failed")) {
        alert('❌ Creation Failed\n\nFailed to create new appraisal period. Please check the form data and try again.');
      } else if (error.message && error.message.includes("Contract update failed")) {
        alert('❌ Contract Update Failed\n\nFailed to update employee contract. Please try again.');
      } else {
        alert(`❌ Unexpected Error\n\n${error.message || 'An unknown error occurred during the appraisal update process.'}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const currentAction = handleProbationAction().find((a) => a.label === selectedAction);
  if (!currentAction) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      <div className="w-full max-w-2xl bg-white rounded-lg shadow-xl flex flex-col max-h-[90vh] overflow-y-auto">
        <div className="bg-gradient-to-r from-green-600 to-green-800 text-white px-6 py-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold">{selectedAction}</h2>
          <button onClick={() => setActionModalVisible(false)}>
            <X size={20} className="text-white hover:text-gray-200" />
          </button>
        </div>

        <div className="p-6 space-y-4 overflow-y-auto flex-1">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded border">
              <label className="block text-sm font-medium mb-1">Current Contract Type</label>
              <p className="font-semibold">{employeeContractInfo[0]?.contract_type || 'N/A'}</p>
            </div>
            <div className="bg-gray-50 p-3 rounded border">
              <label className="block text-sm font-medium mb-1">On PIP</label>
              <p className="font-semibold">{employeeContractInfo[0]?.on_pip ? 'Yes' : 'No'}</p>
            </div>
          </div>
          {/* Always show name field for new appraisal periods */}
          <div>
            <label className="block text-sm font-medium mb-1">Appraisal Period Name *</label>
            <input
              type="text"
              className="w-full border rounded p-2"
              value={newAppraisalPeriod.name}
              onChange={(e) =>
                setNewAppraisalPeriod((prev) => ({
                  ...prev,
                  name: e.target.value,
                }))
              }
              placeholder="Enter appraisal period name"
              required
            />
          </div>

          {/* Always show start date field */}
          <div>
            <label className="block text-sm font-medium mb-1">Start Date *</label>
            <input
              type="date"
              className="w-full border rounded p-2"
              value={newAppraisalPeriod.start_date}
              onChange={(e) =>
                setNewAppraisalPeriod((prev) => ({
                  ...prev,
                  start_date: e.target.value,
                }))
              }
              required
            />
          </div>

          {currentAction.fields.includes('end_date') && (
            <div>
              <label className="block text-sm font-medium mb-1">End Date</label>
              <input
                type="date"
                className="w-full border rounded p-2"
                value={newAppraisalPeriod.end_date}
                onChange={(e) =>
                  setNewAppraisalPeriod((prev) => ({
                    ...prev,
                    end_date: e.target.value,
                  }))
                }
                required
              />
            </div>
          )}

          {/* Always show cycle type field */}
          <div>
            <label className="block text-sm font-medium mb-1">Cycle Type *</label>
            <select
              className="w-full border rounded p-2"
              value={newAppraisalPeriod.cycle_type}
              onChange={(e) =>
                setNewAppraisalPeriod((prev) => ({
                  ...prev,
                  cycle_type: e.target.value,
                }))
              }
              required
            >
              <option value="">Select Cycle Type</option>
              <option value="ANNUAL">Annual</option>
              <option value="SEMI_ANNUAL">Semi-Annual</option>
              <option value="QUARTERLY">Quarterly</option>
              <option value="PROBATION">Probation</option>
              <option value="SPECIAL">Special</option>
            </select>
          </div>

          {currentAction.fields.includes('contract_type') && (
            <div>
              <label className="block text-sm font-medium mb-1">New Contract Type</label>
              <select
                className="w-full border rounded p-2"
                value={newAppraisalPeriod.contract_type || ''}
                onChange={(e) =>
                  setNewAppraisalPeriod((prev) => ({
                    ...prev,
                    contract_type: e.target.value,
                  }))
                }
                required
              >
                <option value="">Select Contract Type</option>
                <option value="Permanent">Permanent</option>
                <option value="Probation">Probation</option>
                <option value="Contract">Contract</option>
                <option value="Temporary">Temporary</option>
              </select>
            </div>
          )}

          {currentAction.fields.includes('end_of_probation_date') && (
            <div>
              <label className="block text-sm font-medium mb-1">End of Probation Date</label>
              <input
                type="date"
                className="w-full border rounded p-2"
                value={newAppraisalPeriod.end_of_probation_date || ''}
                onChange={(e) =>
                  setNewAppraisalPeriod((prev) => ({
                    ...prev,
                    end_of_probation_date: e.target.value,
                  }))
                }
                required={currentAction.fields.includes('end_of_probation_date')}
              />
            </div>
          )}

          {currentAction.fields.includes('on_pip') && (
            <div>
              <label className="block text-sm font-medium mb-1">PIP Status</label>
              <select
                className="w-full border rounded p-2"
                value={newAppraisalPeriod.on_pip ? 'true' : 'false'}
                onChange={(e) =>
                  setNewAppraisalPeriod((prev) => ({
                    ...prev,
                    on_pip: e.target.value === 'true',
                  }))
                }
              >
                <option value="true">On PIP</option>
                <option value="false">Off PIP</option>
              </select>
            </div>
          )}

          {/* Always show description field */}
          <div>
            <label className="block text-sm font-medium mb-1">Description *</label>
            <textarea
              className="w-full border rounded p-2 h-24"
              value={newAppraisalPeriod.description}
              onChange={(e) =>
                setNewAppraisalPeriod((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Enter detailed description for this action..."
              required
            />
          </div>

          {/* Always show way forward preview */}
          <div className="bg-blue-50 p-3 rounded border border-blue-200">
            <label className="block text-sm font-medium mb-1 text-blue-800">Way Forward (Auto-Generated)</label>
            <p className="text-sm text-blue-700">
              {currentAction.autoPopulateWayForward || 'Way forward will be automatically generated based on this action.'}
            </p>
            <p className="text-xs text-blue-600 mt-1">
              * This will be automatically applied to the previous appraisal record.
            </p>
          </div>

          <div className="flex justify-end space-x-4 p-2 py-3 border-t">
            <button
              className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
              onClick={() => setActionModalVisible(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleContractUpdate}
              disabled={isLoading}
            >
              {isLoading ? 'Processing...' : 'Confirm Action'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActionModal;