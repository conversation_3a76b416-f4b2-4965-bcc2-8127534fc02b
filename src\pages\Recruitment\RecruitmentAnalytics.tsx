"use client";

import React, { useState,  useMemo } from "react";
import { useNavigate } from "react-router-dom";

// shadcn/ui components
import { Screen } from "@/app-components/layout/screen";
import { Button } from "@/components/ui/button";
import {
  Re<PERSON>onsive<PERSON><PERSON>r,
  BarChart,
  Bar,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Cell,
} from "recharts";

interface ApplicationStats {
  total: number;
  by_status: {
    shortlisted: number;
    "under review": number;
  };
}

interface RecruitmentAnalyticsResponse {
  application_stats: ApplicationStats;
  interview_performance: Record<string, any>;
  hiring_funnel: Record<string, any>;
  time_to_hire: number | null;
}

interface RecruitmentAnalyticsDashboardProps {
  departmentId?: number;
  startDate?: string;
  endDate?: string;
}

const RecruitmentAnalyticsDashboard: React.FC<RecruitmentAnalyticsDashboardProps> = () => {
  const navigate = useNavigate();

  // Sample data for testing
  const sampleData: RecruitmentAnalyticsResponse = {
    application_stats: {
      total: 3,
      by_status: {
        shortlisted: 1,
        "under review": 2,
      },
    },
    interview_performance: {},
    hiring_funnel: {},
    time_to_hire: 14, // Example: Time to hire in days
  };

  const [analyticsData, setAnalyticsData] = useState<RecruitmentAnalyticsResponse>(sampleData);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("90d");

  // Application stats chart data
  const applicationStatsData = useMemo(() => {
    const stats = analyticsData.application_stats;
    return [
      { status: "Shortlisted", count: stats.by_status.shortlisted },
      { status: "Under Review", count: stats.by_status["under review"] },
    ];
  }, [analyticsData]);

  // Filter recruitment data based on the selected time range (optional)
  const timeRangeData = useMemo(() => {
    // In this case, we're not applying any filter but just showing sample data
    return analyticsData.application_stats;
  }, [analyticsData]);

  // Render states
  if (loading) {
    return (
      <Screen>
        <div className="p-4">
          <p className="text-gray-900 dark:text-gray-100">Loading recruitment analytics...</p>
        </div>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <div className="p-4 text-red-500">
          <p>{error}</p>
        </div>
      </Screen>
    );
  }

  // Extract application stats for convenience
  const { total, by_status } = analyticsData.application_stats;

  return (
    <Screen>
      {/* Breadcrumb */}
      <div className="p-4 space-y-4">
        {/* Summary cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          {/* Total Applications */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4 bg-white dark:bg-gray-800">
            <h2 className="font-semibold text-sm text-gray-900 dark:text-gray-100">Total Applications</h2>
            <p className="text-xl text-gray-900 dark:text-gray-100">{total}</p>
          </div>
          {/* Shortlisted Applications */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4 bg-white dark:bg-gray-800">
            <h2 className="font-semibold text-sm text-gray-900 dark:text-gray-100">Shortlisted</h2>
            <p className="text-xl text-gray-900 dark:text-gray-100">{by_status.shortlisted}</p>
          </div>
          {/* Under Review Applications */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4 bg-white dark:bg-gray-800">
            <h2 className="font-semibold text-sm text-gray-900 dark:text-gray-100">Under Review</h2>
            <p className="text-xl text-gray-900 dark:text-gray-100">{by_status["under review"]}</p>
          </div>
        </div>

        {/* Application Status Distribution (Bar Chart) */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded shadow">
          <h3 className="text-md font-semibold mb-2 text-gray-900 dark:text-gray-100">Application Status Distribution</h3>
          {applicationStatsData.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">No application status data available.</p>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={applicationStatsData}>
                <CartesianGrid strokeDasharray="3 3" stroke="gray" />
                <XAxis dataKey="status" tick={{ fill: "gray" }} />
                <YAxis tick={{ fill: "gray" }} />
                <Tooltip />
                <Bar dataKey="count" fill="#2196f3">
                  {applicationStatsData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`hsl(${(index * 60) % 360}, 70%, 50%)`}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>

        {/* Time to Hire (Average) */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded shadow">
          <h3 className="text-md font-semibold mb-2 text-gray-900 dark:text-gray-100">Time to Hire (Average)</h3>
          <p className="text-xl text-gray-900 dark:text-gray-100">
            {analyticsData.time_to_hire ? `${analyticsData.time_to_hire} days` : "N/A"}
          </p>
        </div>

        {/* Button to navigate to recruitment management */}
        <div className="text-right">
          <Button
            onClick={() => navigate("/recruitment-management")}
            className="bg-green-600 dark:bg-green-500 hover:bg-green-700 dark:hover:bg-green-600 text-white"
          >
            Manage Recruitment
          </Button>
        </div>
      </div>
    </Screen>
  );
};

export default RecruitmentAnalyticsDashboard;
