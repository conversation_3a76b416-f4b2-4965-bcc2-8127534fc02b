// src/redux/features/setup/setupSlice.ts
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { BASE_URL } from "@/config";

// ---------------------------------------
// Types/Interfaces
// ---------------------------------------
export interface Organization {
  id: number;
  name: string;
  description: string;
  logo: string;
  primary_color: string;
  secondary_color: string;
  tertiary_color: string;
  fourth_color: string;
  fifth_color: string;
  contact: string;
  address: string;
  domain: string;
}

interface Department {
  id: number;
  name: string;
  description: string;
  dep_head: string;
  dep_head_assistant: string;
  dep_hr: string;
  department_status_active: boolean;
  organisation: number;
  parent_department?: number | null;
}

interface Group {
  id: number;
  name: string;
  description?: string;
}

interface EmploymentType {
  id: number;
  name: string;
  description?: string;
}

interface SetupState {
  // Organization
  organization: Organization | null;
  orgLoading: boolean;
  orgError: string | null;

  // Departments
  departments: Department[];
  depLoading: boolean;
  depError: string | null;

  // Groups
  groups: Group[];
  groupLoading: boolean;
  groupError: string | null;

  // Employment Types
  employmentTypes: EmploymentType[];
  empTypesLoading: boolean;
  empTypesError: string | null;

  // User
  userLoading: boolean;
  userError: string | null;
}

// ---------------------------------------
// Initial State
// ---------------------------------------
const initialState: SetupState = {
  organization: null,
  orgLoading: false,
  orgError: null,

  departments: [],
  depLoading: false,
  depError: null,

  groups: [],
  groupLoading: false,
  groupError: null,

  employmentTypes: [],
  empTypesLoading: false,
  empTypesError: null,

  userLoading: false,
  userError: null,
};

// ---------------------------------------
// Thunks
// ---------------------------------------
export const createOrganization = createAsyncThunk<
  Organization,
  {
    name: string;
    description: string;
    logo: File;
    primary_color: string;
    secondary_color: string;
    tertiary_color: string;
    fourth_color: string;
    fifth_color: string;
    contact: string;
    address: string;
    domain: string;
  }
>("setup/createOrganization", async (payload, { rejectWithValue }) => {
  try {
    const formData = new FormData();
    formData.append("name", payload.name);
    formData.append("description", payload.description);
    formData.append("logo", payload.logo);
    formData.append("primary_color", payload.primary_color);
    formData.append("secondary_color", payload.secondary_color);
    formData.append("tertiary_color", payload.tertiary_color);
    formData.append("fourth_color", payload.fourth_color);
    formData.append("fifth_color", payload.fifth_color);
    formData.append("contact", payload.contact);
    formData.append("address", payload.address);
    formData.append("domain", payload.domain);

    const response = await fetch(`${BASE_URL}/users/organization`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to create organization: ${errorText}`);
    }
    return (await response.json()) as Organization;
  } catch (error: any) {
    return rejectWithValue(error.message);
  }
});

export const createDepartment = createAsyncThunk<
  Department,
  {
    name: string;
    description: string;
    dep_head: string;
    dep_head_assistant: string;
    dep_hr: string;
    department_status_active: boolean;
    organisation: number;
    parent_department?: number | null;
  }
>("setup/createDepartment", async (payload, { rejectWithValue }) => {
  try {
    const response = await fetch(`${BASE_URL}/users/departments`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });
    if (!response.ok) throw new Error("Failed to create department");
    return (await response.json()) as Department;
  } catch (error: any) {
    return rejectWithValue(error.message);
  }
});


export const createGroup = createAsyncThunk<
  Group,
  { name: string; description?: string; organisation: number }
>("setup/createGroup", async (payload, { rejectWithValue }) => {
  try {
    const response = await fetch(`${BASE_URL}/users/organization_groups`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });
    if (!response.ok) throw new Error("Failed to create group");
    return (await response.json()) as Group;
  } catch (error: any) {
    return rejectWithValue(error.message);
  }
});

export const createEmploymentType = createAsyncThunk<
  EmploymentType,
  {
    name: string;
    description?: string;
    emp_no_prefix?: string;
    emp_no_suffix?: string;
    organisation: number;
  }
>("setup/createEmploymentType", async (payload, { rejectWithValue }) => {
  try {
    const response = await fetch(
      `${BASE_URL}/users/organization-employment_type`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      }
    );
    if (!response.ok) throw new Error("Failed to create employment type");
    return (await response.json()) as EmploymentType;
  } catch (error: any) {
    return rejectWithValue(error.message);
  }
});

export const createUser = createAsyncThunk<
  void,
  {
    email: string;
    password: string;
    organisation_id: number;
    department_id?: number;
    group_id?: number;
  }
>("setup/createUser", async (payload, { rejectWithValue }) => {
  try {
    const response = await fetch(`${BASE_URL}/users/registration`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ user: payload }),
    });
    if (!response.ok) throw new Error("Failed to create user");
  } catch (error: any) {
    return rejectWithValue(error.message);
  }
});

// ---------------------------------------
// Slice
// ---------------------------------------
const setupSlice = createSlice({
  name: "setup",
  initialState,
  reducers: {
    resetSetup: () => initialState,
  },
  extraReducers: (builder) => {
    // ------ Create Organization ------
    builder.addCase(createOrganization.pending, (state) => {
      state.orgLoading = true;
      state.orgError = null;
    });
    builder.addCase(createOrganization.fulfilled, (state, action) => {
      state.orgLoading = false;
      state.organization = action.payload;
    });
    builder.addCase(createOrganization.rejected, (state, action) => {
      state.orgLoading = false;
      state.orgError = action.payload as string;
    });

    // ------ Create Department ------
    builder.addCase(createDepartment.pending, (state) => {
      state.depLoading = true;
      state.depError = null;
    });
    builder.addCase(createDepartment.fulfilled, (state, action) => {
      state.depLoading = false;
      state.departments.push(action.payload);
    });
    builder.addCase(createDepartment.rejected, (state, action) => {
      state.depLoading = false;
      state.depError = action.payload as string;
    });

    // ------ Create Group ------
    builder.addCase(createGroup.pending, (state) => {
      state.groupLoading = true;
      state.groupError = null;
    });
    builder.addCase(createGroup.fulfilled, (state, action) => {
      state.groupLoading = false;
      state.groups.push(action.payload);
    });
    builder.addCase(createGroup.rejected, (state, action) => {
      state.groupLoading = false;
      state.groupError = action.payload as string;
    });

    // ------ Create Employment Type ------
    builder.addCase(createEmploymentType.pending, (state) => {
      state.empTypesLoading = true;
      state.empTypesError = null;
    });
    builder.addCase(createEmploymentType.fulfilled, (state, action) => {
      state.empTypesLoading = false;
      state.employmentTypes.push(action.payload);
    });
    builder.addCase(createEmploymentType.rejected, (state, action) => {
      state.empTypesLoading = false;
      state.empTypesError = action.payload as string;
    });

    // ------ Create User ------
    builder.addCase(createUser.pending, (state) => {
      state.userLoading = true;
      state.userError = null;
    });
    builder.addCase(createUser.fulfilled, (state) => {
      state.userLoading = false;
    });
    builder.addCase(createUser.rejected, (state, action) => {
      state.userLoading = false;
      state.userError = action.payload as string;
    });
  },
});

export const { resetSetup } = setupSlice.actions;
export default setupSlice.reducer;
