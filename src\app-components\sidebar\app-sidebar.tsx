import * as React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

import {
  GalleryVerticalEnd,
  Briefcase,
  LocateFixed,
  Home,
  Calendar,
  Umbrella,
  UserSearch,
  Activity,
  Settings,
  Target,
  ChartArea,
} from "lucide-react";

import { NavMain } from "@/app-components/sidebar/nav-main";
import { NavUser } from "@/app-components/sidebar/nav-user";
import { TeamSwitcher } from "@/app-components/sidebar/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { CreateAppraisalModal } from "@/app-components/Performance/appraisal/CreateAppraisalPeriodModal";

const navData = {
  teams: [
    {
      name: "Optiven Limited",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
  ],
  navMain: [
    {
      title: "Home",
      url: "/", // real route
      icon: Home,
      isActive: true,
    },
    {
      title: "Employee Management",
      // Instead of "#" we can leave this blank or treat it as a toggle.
      url: "", 
      icon: Briefcase,
      isActive: true,
      items: [
        { title: "Create Employee", url: "/employee-creation" },
        { title: "Employee Analytics", url: "/employee-analytics" },
        { title: "Employee's List", url: "/employees-list" },
        { title: "Employee Reports", url: "/employee-reports" },
        { title: "Department Reports", url: "/department-reports" },
        { title: "Contract Reports", url: "/contract-reports" },
        { title: "Probation Reports", url: "/probation-reports" },
        { title: "Performance Appraisal Reports", url: "/performance-appraisal-reports" },
      ],
    },
    {
      title: "Organizational Chart",
      // Instead of "#" we can leave this blank or treat it as a toggle.
      url: "", 
      icon: ChartArea,
      isActive: true,
      items: [
        { title: "Organizational Chart", url: "/organizational-chart" },
      ],
    },
    {
      title: "Training ",
      url: "",
      icon: LocateFixed,
      items: [
        { title: "Create Training Need", url: "/create-training-need" },
        { title: "Approve Training", url: "/approve-training" },
        { title: "Employee Evaluation", url: "/training-evaluation" },
        { title: "Assessment Results", url: "/results" },
        { title: "Resource Sourcing", url: "/resource-sourcing" },
      ],
    },
    {
      title: "Calendar",
      url: "",
      icon: Calendar,
      items: [
        { title: "Calendar Management", url: "/calender-management" },
        { title: "Calendar Analytics", url: "/calendar-analytics" },
      ],
    },
    {
      title: "Leave",
      url: "",
      icon: Umbrella,
      items: [
        { title: "Leave Management", url: "/leave-management" },
        { title: "HR Approval", url: "/hr-approval" },
        { title: "Leave Reports", url: "/leave-reports" },
        { title: "Leave Analytics", url: "/leave-analytics" },
        { title: "Leave ClawBack", url: "/leave-clawback" },
      ],
    },
    {
      title: "Recruitment",
      url: "",
      icon: UserSearch,
      items: [
        { title: "Job Position Management", url: "/job-management" },
        { title: "Job Requisition", url: "/create-vacancy" },
        { title: "Requisition Pipeline", url: "/requisition-pipeline" },
        { title: "Recruitment Dashboard", url: "/recruitment-dash" },
        { title: "Recruitment Analytics", url: "/recruitment-analytics" },
      ],
    },
    {
      title: "Performance",
      url: "",
      icon: Activity,
      items: [
        // We keep an action-based item for modal
        // { title: "Create Appraisal Period", url: "", action: "openModal" },
        { title: "Performance Management", url: "/performance-dashboard" },
        { title: "Performance Analytics", url: "/performance-analytics" },
        { title: "Appraisal Reports", url: "/performance-appraisal-reports" },
      ],
    },
    {
      title: "KPI Management",
      url: "",
      icon: Target,
      items: [
        { title: "KPI Creation and List", url: "/kpi-management" },
        { title: "Custom Kpi Approval", url: "/custom-kpi-approval" },
      ],
    },
    {
      title: "Settings",
      url: "",
      icon: Settings,
      items: [
        { title: "Employee Permissions", url: "/emp-permissions" },
        { title: "Permissions Management", url: "/permissions" },
        { title: "Department Management", url: "/departments" },
        { title: "Group Management", url: "/groups" },
      ],
    },
  ],
};

export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  const { user } = useSelector((state: RootState) => state.auth);
  const [modalOpen, setModalOpen] = React.useState(false);

  const realUser = {
    name:
      user?.UserBio?.first_name && user?.UserBio?.last_name
        ? `${user.UserBio.first_name} ${user.UserBio.last_name}`
        : user?.employee_no || "Unknown User",
    email:
      user?.UserContacts?.company_email ||
      user?.UserContacts?.personal_email ||
      "No Email",
    avatar: "/avatars/shadcn.jpg",
  };

  // Handle menu click to open modal
  const handleMenuClick = (action: string) => {
    if (action === "openModal") {
      setModalOpen(true);
    }
  };

  return (
    <>
      <Sidebar
        collapsible="icon"
        className="sidebar"
        {...props}
      >
        <SidebarHeader className="border-b sidebar-header">
          <TeamSwitcher teams={navData.teams} />
        </SidebarHeader>

        <SidebarContent className="flex-1 overflow-y-auto">
          {/* 
            We pass onMenuClick so that if the nav item has an 'action',
            we trigger handleMenuClick (like opening the modal).
          */}
          <NavMain items={navData.navMain} onMenuClick={handleMenuClick} />
        </SidebarContent>

        <SidebarFooter className="sidebar-footer border-t">
          <NavUser user={realUser} />
        </SidebarFooter>
      </Sidebar>

      <CreateAppraisalModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSuccess={() => setModalOpen(false)}
      />
    </>
  );
}
