import axios from "axios";
import toast from "react-hot-toast";
import { BASE_URL } from "@/config";

// Specialized function for JobInfo records
export async function updateOrCreateJobInfoRecord<T extends { id?: number; employee_no: string; teams_code?: any; department?: any }>(
  url: string,
  token: string,
  data: T
): Promise<T | null> {
  try {
    const headers = { Authorization: `Token ${token}` };

    // Prepare data with correct types for API
    const dataToSend = {
      ...data,
      // Ensure department is a number if it exists, or null
      department: data.department ? Number(data.department) : null,
      // Ensure teams_code is a number if it exists, or null
      teams_code: data.teams_code ? Number(data.teams_code) : null,
    };

    console.log("Prepared data for API:", dataToSend);

    // 1) Check if record(s) for this employee_no exist
    const getRes = await axios.get(url, {
      headers,
      params: { employee_no: data.employee_no },
    });
    const allRecords = getRes.data;

    // Filter records to only include those for this specific employee
    const records = Array.isArray(allRecords)
      ? allRecords.filter(record => record.employee_no === data.employee_no)
      : [];

    console.log(`Found ${records.length} job info records for employee ${data.employee_no}:`, records);

    if (records.length > 0) {
      // Find the record with the matching ID if we have one
      let recordToUpdate = records[0];

      if (data.id) {
        const matchingRecord = records.find(r => r.id === data.id);
        if (matchingRecord) {
          recordToUpdate = matchingRecord;
          console.log(`Found record with matching ID ${data.id} to update`);
        } else {
          console.log(`Could not find record with ID ${data.id}, using first record with ID ${recordToUpdate.id}`);
        }
      }

      const recordId = recordToUpdate.id;

      // When updating, don't send employee_no and id in the body
      const { employee_no, id, ...dataToUpdate } = dataToSend;

      console.log(`Updating record ${recordId} with data:`, dataToUpdate);

      try {
        // Try a simple PATCH
        const patchRes = await axios.patch(`${url}/${recordId}`, dataToUpdate, { headers });

        console.log("API response after PATCH update:", patchRes.data);
        toast.success("Job information updated successfully!");

        // Return the updated data with the original ID and employee_no
        return {
          ...patchRes.data,
          id: recordId,
          employee_no: data.employee_no,
          department: patchRes.data.department ? Number(patchRes.data.department) : null,
          teams_code: patchRes.data.teams_code ? Number(patchRes.data.teams_code) : null,
        };
      } catch (patchErr: any) {
        console.error("Error with PATCH job info update:", patchErr);

        // Log detailed error information
        if (patchErr.response) {
          console.error("PATCH Error Response:", patchErr.response.data);
          console.error("PATCH Error Status:", patchErr.response.status);
        }

        // Show specific error message
        const errorMessage = patchErr.response?.data
          ? (typeof patchErr.response.data === 'object'
              ? JSON.stringify(patchErr.response.data)
              : patchErr.response.data)
          : "Unknown error occurred";

        toast.error(`Failed to update job information: ${errorMessage}`);
        return null;
      }
    } else {
      // No record => POST
      console.log("Creating new record with data:", dataToSend);
      const postRes = await axios.post(url, dataToSend, { headers });

      if (postRes.status === 201) {
        console.log("API response after create:", postRes.data);
        toast.success("Job information created successfully!");

        // Verify the response contains the expected data
        if (postRes.data && typeof postRes.data === 'object') {
          return {
            ...postRes.data,
            // Ensure these fields are properly typed
            department: postRes.data.department ? Number(postRes.data.department) : null,
            teams_code: postRes.data.teams_code ? Number(postRes.data.teams_code) : null,
          };
        }
      }

      return postRes.data;
    }
  } catch (err: any) {
    console.error("Error updating/creating job record:", err);
    // Show more detailed error message if available
    if (err.response && err.response.data) {
      console.error("API error details:", err.response.data);
      const errorMessage = typeof err.response.data === 'object'
        ? JSON.stringify(err.response.data)
        : err.response.data;
      toast.error(`Failed to update/create job information: ${errorMessage}`);
    } else {
      toast.error("Failed to update/create job information.");
    }
    return null;
  }
}

// Specialized function for ContractInfo records
export async function updateOrCreateContractInfoRecord<T extends {
  id?: number;
  employee_no: string;
  contract_type?: string;
  contract_start_date?: string;
  current_contract_end?: string | null;
  end_of_probation_date?: string | null;
  on_pip?: boolean;
}>(
  url: string,
  token: string,
  data: T
): Promise<T | null> {
  try {
    const headers = { Authorization: `Token ${token}` };

    // Validate required fields first
    if (!data.contract_type || !data.contract_start_date) {
      toast.error("Contract type and start date are required");
      return null;
    }

    // Prepare data with correct types for API
    const dataToSend = {
      ...data,
      // Ensure on_pip is a boolean
      on_pip: data.on_pip === undefined ? false : Boolean(data.on_pip),
      // Handle empty date fields - set to null if empty
      current_contract_end: data.current_contract_end && data.current_contract_end.trim() !== ''
        ? data.current_contract_end
        : null,
      end_of_probation_date: data.end_of_probation_date && data.end_of_probation_date.trim() !== ''
        ? data.end_of_probation_date
        : null
    };

    console.log("Sending contract data to API:", dataToSend);

    // 1) Check if record(s) for this employee_no exist
    const getRes = await axios.get(url, {
      headers,
      params: { employee_no: data.employee_no },
    });
    const allRecords = getRes.data;

    // Filter records to only include those for this specific employee
    const records = Array.isArray(allRecords)
      ? allRecords.filter(record => record.employee_no === data.employee_no)
      : [];

    console.log(`Found ${records.length} contract records for employee ${data.employee_no}:`, records);

    if (records.length > 0) {
      // Find the record with the matching ID if we have one
      let recordToUpdate = records[0];

      if (data.id) {
        const matchingRecord = records.find(r => r.id === data.id);
        if (matchingRecord) {
          recordToUpdate = matchingRecord;
          console.log(`Found contract record with matching ID ${data.id} to update`);
        } else {
          console.log(`Could not find contract record with ID ${data.id}, using first record with ID ${recordToUpdate.id}`);
        }
      }

      const recordId = recordToUpdate.id;

      // When updating, don't send employee_no and id in the body
      const { employee_no, id, ...dataToUpdate } = dataToSend;

      console.log(`Updating contract record ${recordId} with data:`, dataToUpdate);

      try {
        // Try a simple PATCH
        const patchRes = await axios.patch(`${url}/${recordId}`, dataToUpdate, { headers });

        console.log("API response after PATCH contract update:", patchRes.data);
        toast.success("Contract information updated successfully!");

        // Return the updated data with the original ID and employee_no
        return {
          ...patchRes.data,
          id: recordId,
          employee_no: data.employee_no,
          on_pip: patchRes.data.on_pip === undefined ? false : Boolean(patchRes.data.on_pip),
          current_contract_end: patchRes.data.current_contract_end || null,
          end_of_probation_date: patchRes.data.end_of_probation_date || null
        };
      } catch (patchErr: any) {
        console.error("Error with PATCH contract update:", patchErr);

        // Log detailed error information
        if (patchErr.response) {
          console.error("PATCH Error Response:", patchErr.response.data);
          console.error("PATCH Error Status:", patchErr.response.status);
        }

        // Show specific error message
        const errorMessage = patchErr.response?.data
          ? (typeof patchErr.response.data === 'object'
              ? JSON.stringify(patchErr.response.data)
              : patchErr.response.data)
          : "Unknown error occurred";

        toast.error(`Failed to update contract information: ${errorMessage}`);
        return null;
      }
    } else {
      // No record => POST
      console.log("Creating new contract record with data:", dataToSend);
      const postRes = await axios.post(url, dataToSend, { headers });

      if (postRes.status === 201) {
        console.log("API response after contract create:", postRes.data);
        toast.success("Contract information created successfully!");

        // Verify the response contains the expected data
        if (postRes.data && typeof postRes.data === 'object') {
          return {
            ...postRes.data,
            // Ensure these fields are properly typed
            on_pip: postRes.data.on_pip === undefined ? false : Boolean(postRes.data.on_pip),
            current_contract_end: postRes.data.current_contract_end || null,
            end_of_probation_date: postRes.data.end_of_probation_date || null
          };
        }
      }

      return postRes.data;
    }
  } catch (err: any) {
    console.error("Error updating/creating contract record:", err);
    // Show more detailed error message if available
    if (err.response && err.response.data) {
      console.error("API error details:", err.response.data);
      const errorMessage = typeof err.response.data === 'object'
        ? JSON.stringify(err.response.data)
        : err.response.data;
      toast.error(`Failed to update/create contract information: ${errorMessage}`);
    } else {
      toast.error("Failed to update/create contract information.");
    }
    return null;
  }
}

// Specialized function for ImportantDates records
export async function updateOrCreateImportantDatesRecord<T extends {
  id?: number;
  employee_no: string;
  date_of_current_appointment?: string;
  date_of_leaveing?: string | null;
}>(
  url: string,
  token: string,
  data: T
): Promise<T | null> {
  try {
    const headers = { Authorization: `Token ${token}` };

    // Validate required fields first
    if (!data.date_of_current_appointment) {
      toast.error("Date of current appointment is required");
      return null;
    }

    // Prepare data with correct types for API
    const dataToSend = {
      ...data,
      // Handle empty date fields - set to null if empty
      date_of_leaveing: data.date_of_leaveing && data.date_of_leaveing.trim() !== ''
        ? data.date_of_leaveing
        : null
    };

    console.log("Sending important dates data to API:", dataToSend);

    // 1) Check if record(s) for this employee_no exist
    const getRes = await axios.get(url, {
      headers,
      params: { employee_no: data.employee_no },
    });
    const allRecords = getRes.data;

    // Filter records to only include those for this specific employee
    const records = Array.isArray(allRecords)
      ? allRecords.filter(record => record.employee_no === data.employee_no)
      : [];

    console.log(`Found ${records.length} important dates records for employee ${data.employee_no}:`, records);

    if (records.length > 0) {
      // Find the record with the matching ID if we have one
      let recordToUpdate = records[0];

      if (data.id) {
        const matchingRecord = records.find(r => r.id === data.id);
        if (matchingRecord) {
          recordToUpdate = matchingRecord;
          console.log(`Found important dates record with matching ID ${data.id} to update`);
        } else {
          console.log(`Could not find important dates record with ID ${data.id}, using first record with ID ${recordToUpdate.id}`);
        }
      }

      const recordId = recordToUpdate.id;

      // When updating, don't send employee_no and id in the body
      const { employee_no, id, ...dataToUpdate } = dataToSend;

      console.log(`Updating important dates record ${recordId} with data:`, dataToUpdate);

      try {
        // Try a simple PATCH
        const patchRes = await axios.patch(`${url}/${recordId}`, dataToUpdate, { headers });

        console.log("API response after PATCH important dates update:", patchRes.data);
        toast.success("Important dates updated successfully!");

        // Return the updated data with the original ID and employee_no
        return {
          ...patchRes.data,
          id: recordId,
          employee_no: data.employee_no,
          date_of_leaveing: patchRes.data.date_of_leaveing || null
        };
      } catch (patchErr: any) {
        console.error("Error with PATCH important dates update:", patchErr);

        // Log detailed error information
        if (patchErr.response) {
          console.error("PATCH Error Response:", patchErr.response.data);
          console.error("PATCH Error Status:", patchErr.response.status);
        }

        // Show specific error message
        const errorMessage = patchErr.response?.data
          ? (typeof patchErr.response.data === 'object'
              ? JSON.stringify(patchErr.response.data)
              : patchErr.response.data)
          : "Unknown error occurred";

        toast.error(`Failed to update important dates: ${errorMessage}`);
        return null;
      }
    } else {
      // No record => POST
      console.log("Creating new important dates record with data:", dataToSend);
      const postRes = await axios.post(url, dataToSend, { headers });

      if (postRes.status === 201) {
        console.log("API response after important dates create:", postRes.data);
        toast.success("Important dates created successfully!");

        // Verify the response contains the expected data
        if (postRes.data && typeof postRes.data === 'object') {
          return {
            ...postRes.data,
            // Ensure these fields are properly typed
            date_of_leaveing: postRes.data.date_of_leaveing || null
          };
        }
      }

      return postRes.data;
    }
  } catch (error: any) {
    console.error("Error updating/creating important dates:", error);

    // Log detailed error information
    if (error.response) {
      console.error("Error Response:", error.response.data);
      console.error("Error Status:", error.response.status);
    }

    // Show specific error message
    const errorMessage = error.response?.data
      ? (typeof error.response.data === 'object'
          ? JSON.stringify(error.response.data)
          : error.response.data)
      : "Unknown error occurred";

    toast.error(`Failed to update/create important dates: ${errorMessage}`);
    return null;
  }
}

// Specialized function for PaymentInfo records
export async function updateOrCreatePaymentInfoRecord<T extends {
  id?: number;
  employee_no: string;
  pension_scheme_join?: string;
  salary?: number;
  bonus?: number;
  tax_status?: boolean;
  bank_name?: string;
  account_number?: string;
  payment_frequency?: string;
  KRA_pin?: string;
  NHIF_SHIF_number?: string;
  NSSF_number?: string;
  HELB_number?: string;
}>(
  url: string,
  token: string,
  data: T
): Promise<T | null> {
  try {
    const headers = { Authorization: `Token ${token}` };

    // Prepare data with correct types for API
    const dataToSend = {
      ...data,
      // Ensure numeric fields are properly handled
      salary: data.salary != null ? Number(data.salary) : null,
      bonus: data.bonus != null ? Number(data.bonus) : null,
      // Ensure boolean field is properly handled
      tax_status: data.tax_status != null ? Boolean(data.tax_status) : null,
      // Handle empty string fields - convert to null if empty
      pension_scheme_join: data.pension_scheme_join && data.pension_scheme_join.trim() !== ''
        ? data.pension_scheme_join
        : null,
      bank_name: data.bank_name && data.bank_name.trim() !== ''
        ? data.bank_name
        : null,
      account_number: data.account_number && data.account_number.trim() !== ''
        ? data.account_number
        : null,
      payment_frequency: data.payment_frequency && data.payment_frequency.trim() !== ''
        ? data.payment_frequency
        : null,
      KRA_pin: data.KRA_pin && data.KRA_pin.trim() !== ''
        ? data.KRA_pin
        : null,
      NHIF_SHIF_number: data.NHIF_SHIF_number && data.NHIF_SHIF_number.trim() !== ''
        ? data.NHIF_SHIF_number
        : null,
      NSSF_number: data.NSSF_number && data.NSSF_number.trim() !== ''
        ? data.NSSF_number
        : null,
      HELB_number: data.HELB_number && data.HELB_number.trim() !== ''
        ? data.HELB_number
        : null,
    };

    console.log("Sending payment data to API:", dataToSend);

    // 1) Check if record(s) for this employee_no exist
    const getRes = await axios.get(url, {
      headers,
      params: { employee_no: data.employee_no },
    });
    const allRecords = getRes.data;

    // Filter records to only include those for this specific employee
    const records = Array.isArray(allRecords)
      ? allRecords.filter(record => record.employee_no === data.employee_no)
      : [];

    console.log(`Found ${records.length} payment records for employee ${data.employee_no}:`, records);

    if (records.length > 0) {
      // Find the record with the matching ID if we have one
      let recordToUpdate = records[0];

      if (data.id) {
        const matchingRecord = records.find(r => r.id === data.id);
        if (matchingRecord) {
          recordToUpdate = matchingRecord;
          console.log(`Found payment record with matching ID ${data.id} to update`);
        } else {
          console.log(`Could not find payment record with ID ${data.id}, using first record with ID ${recordToUpdate.id}`);
        }
      }

      const recordId = recordToUpdate.id;

      // When updating, don't send employee_no and id in the body
      const { employee_no, id, ...dataToUpdate } = dataToSend;

      console.log(`Updating payment record ${recordId} with data:`, dataToUpdate);

      try {
        // Try a simple PATCH
        const patchRes = await axios.patch(`${url}/${recordId}`, dataToUpdate, { headers });

        console.log("API response after PATCH payment update:", patchRes.data);
        toast.success("Payment information updated successfully!");

        // Return the updated data with the original ID and employee_no
        return {
          ...patchRes.data,
          id: recordId,
          employee_no: data.employee_no,
          // Ensure proper type conversion for response data
          salary: patchRes.data.salary != null ? Number(patchRes.data.salary) : null,
          bonus: patchRes.data.bonus != null ? Number(patchRes.data.bonus) : null,
          tax_status: patchRes.data.tax_status != null ? Boolean(patchRes.data.tax_status) : null,
        };
      } catch (patchErr: any) {
        console.error("Error with PATCH payment update:", patchErr);

        // Log detailed error information
        if (patchErr.response) {
          console.error("PATCH Error Response:", patchErr.response.data);
          console.error("PATCH Error Status:", patchErr.response.status);
        }

        // Show specific error message
        const errorMessage = patchErr.response?.data
          ? (typeof patchErr.response.data === 'object'
              ? JSON.stringify(patchErr.response.data)
              : patchErr.response.data)
          : "Unknown error occurred";

        toast.error(`Failed to update payment information: ${errorMessage}`);
        return null;
      }
    } else {
      // No record => POST
      console.log("Creating new payment record with data:", dataToSend);
      const postRes = await axios.post(url, dataToSend, { headers });

      if (postRes.status === 201) {
        console.log("API response after payment create:", postRes.data);
        toast.success("Payment information created successfully!");

        // Verify the response contains the expected data
        if (postRes.data && typeof postRes.data === 'object') {
          return {
            ...postRes.data,
            // Ensure proper type conversion for response data
            salary: postRes.data.salary != null ? Number(postRes.data.salary) : null,
            bonus: postRes.data.bonus != null ? Number(postRes.data.bonus) : null,
            tax_status: postRes.data.tax_status != null ? Boolean(postRes.data.tax_status) : null,
          };
        }
      }

      return postRes.data;
    }
  } catch (error: any) {
    console.error("Error updating/creating payment information:", error);

    // Log detailed error information
    if (error.response) {
      console.error("Error Response:", error.response.data);
      console.error("Error Status:", error.response.status);
    }

    // Show specific error message
    const errorMessage = error.response?.data
      ? (typeof error.response.data === 'object'
          ? JSON.stringify(error.response.data)
          : error.response.data)
      : "Unknown error occurred";

    toast.error(`Failed to update/create payment information: ${errorMessage}`);
    return null;
  }
}

// Function to fetch work locations
export async function fetchWorkLocations(token: string) {
  try {
    const headers = { Authorization: `Token ${token}` };
    const res = await axios.get(`${BASE_URL}/users/organization_work_locations`, { headers });
    return res.data;
  } catch (err) {
    console.error("Error fetching work locations:", err);
    toast.error("Failed to fetch work locations");
    return [];
  }
}

// Function to fetch organization groups
export async function fetchOrganizationGroups(token: string) {
  try {
    const headers = { Authorization: `Token ${token}` };
    const res = await axios.get(`${BASE_URL}/users/organization_groups`, { headers });
    return res.data;
  } catch (err) {
    console.error("Error fetching organization groups:", err);
    toast.error("Failed to fetch organization groups");
    return [];
  }
}
