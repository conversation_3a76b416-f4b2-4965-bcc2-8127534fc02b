import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { BASE_URL } from '@/config';
import { RootState } from '@/redux/store';
import { useToast } from '@/hooks/use-toast';
import {
  Loader2,
  AlertCircle,
  RefreshCw,
  TrendingUp,
  Users,
  Award,
  BarChart3,
  Calendar,
  Filter,
  Download,
  Eye,
  Search,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  Clock,
  CheckCircle2,
  Building2,
  UserCheck,
  Star,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import KPITrendAnalysis from './KPITrendAnalysis';
import DepartmentPerformance from './DepartmentPerformance';
import PerformanceTimeline from './PerformanceTimeline';
import CreativePerformanceTrends from './CreativePerformanceTrends';

// Enhanced types for comprehensive analytics
export type CompletedAppraisal = {
  id: number;
  employeeid: string;
  employee_name: string;
  department_id: number;
  department_name: string;
  supervisor: string;
  period_id: number;
  period_name: string;
  cycle_type: string;
  start_date: string;
  end_date: string;
  status: string;
  total_emp_self_rating_score: number;
  total_supervisor_rating_score: number;
  final_hr_comments: string;
  way_forward: string;
  date_created: string;
  closed_date: string;
  closed_by: string;
  created_by: string;
  is_active: boolean;
  employee_acceptance: boolean;
  supervisor_acceptance: boolean;
  hr_acceptance: boolean;
};

export type DepartmentStats = {
  department_id: number;
  department_name: string;
  total_appraisals: number;
  completed_appraisals: number;
  average_score: number;
  completion_rate: number;
  employee_count: number;
};

export type PerformanceTrend = {
  period: string;
  average_score: number;
  total_appraisals: number;
  completion_rate: number;
};

export type EmployeePerformance = {
  employee_id: string;
  employee_name: string;
  department_name: string;
  total_appraisals: number;
  average_score: number;
  latest_score: number;
  trend: 'up' | 'down' | 'stable';
  performance_category: 'excellent' | 'good' | 'average' | 'needs_improvement';
};

export type PerformanceData = {
  department_id: number;
  employee_id: string;
  kpi_score: number;
  appraisal_period: string;
};

export type ContractTypeStats = {
  contract_type: string;
  employee_count: number;
  percentage: number;
  avg_performance_score: number;
  pip_count: number;
};

// Contract types from the system
const CONTRACT_TYPES = [
  "Permanent",
  "Temporary",
  "Internship",
  "Consultant",
  "Contractor",
  "Volunteer",
  "Probation",
  "Management Trainee",
];

const PerformanceAnalytics: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State management
  const [completedAppraisals, setCompletedAppraisals] = useState<CompletedAppraisal[]>([]);
  const [departmentStats, setDepartmentStats] = useState<DepartmentStats[]>([]);
  const [performanceTrends, setPerformanceTrends] = useState<PerformanceTrend[]>([]);
  const [topPerformers, setTopPerformers] = useState<EmployeePerformance[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // New state for employee-first approach
  const [uniqueEmployees, setUniqueEmployees] = useState<any[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null);
  const [employeeAppraisalHistory, setEmployeeAppraisalHistory] = useState<CompletedAppraisal[]>([]);
  const [showEmployeeHistory, setShowEmployeeHistory] = useState<boolean>(false);

  // Filter states
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedPeriod, setSelectedPeriod] = useState<string>('');
  const [selectedDateRange, setSelectedDateRange] = useState<string>('last_6_months');
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Analytics summary states
  const [totalCompletedAppraisals, setTotalCompletedAppraisals] = useState<number>(0);
  const [averageOrganizationScore, setAverageOrganizationScore] = useState<number>(0);
  const [completionRate, setCompletionRate] = useState<number>(0);
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);

  // New states for optimized display
  const [employeesOnPip, setEmployeesOnPip] = useState<any[]>([]);
  const [contractTypeStats, setContractTypeStats] = useState<any[]>([]);
  const [currentView, setCurrentView] = useState<'overview' | 'employees' | 'pip' | 'analytics'>('overview');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage] = useState<number>(20);
  const [totalPages, setTotalPages] = useState<number>(1);

  // Fetch employees data
  const fetchEmployees = async () => {
    try {
      const res = await fetch(`${BASE_URL}/employee_details/?team=ALL&department=ALL`, {
        headers: { Authorization: `Token ${token}` },
      });
      if (!res.ok) throw new Error('Failed to fetch employees');
      const data = await res.json();

      // Transform the data to maintain backward compatibility
      const transformedEmployees = data.results.map((emp: any) => ({
        ...emp,
        employee_no: emp.employee_no_id, // Map for backward compatibility
        department_id: emp.department_id, // Ensure it's a number
      }));

      setEmployees(transformedEmployees);
    } catch (err: any) {
      console.error('Error fetching employees:', err);
    }
  };

  // Fetch contract information for all employees (including PIP status)
  const fetchContractInfo = async () => {
    try {
      const res = await fetch(`${BASE_URL}/users/employee-contract-info-details`, {
        headers: { Authorization: `Token ${token}` },
      });
      if (!res.ok) throw new Error('Failed to fetch contract info');
      const data = await res.json();
      return data;
    } catch (err: any) {
      console.error('Error fetching contract info:', err);
      return [];
    }
  };

  // Fetch employee bio details for names and departments
  const fetchEmployeeBioDetails = async () => {
    try {
      const res = await fetch(`${BASE_URL}/users/employee-bio-details`, {
        headers: { Authorization: `Token ${token}` },
      });
      if (!res.ok) throw new Error('Failed to fetch employee bio details');
      const data = await res.json();
      return data;
    } catch (err: any) {
      console.error('Error fetching employee bio details:', err);
      return [];
    }
  };

  // Fetch departments data
  const fetchDepartments = async () => {
    try {
      const res = await fetch(`${BASE_URL}/users/departments`, {
        headers: { Authorization: `Token ${token}` },
      });
      if (!res.ok) throw new Error('Failed to fetch departments');
      const data = await res.json();
      setDepartments(data);
    } catch (err: any) {
      console.error('Error fetching departments:', err);
    }
  };

  // Fetch completed appraisals (inactive records)
  const fetchCompletedAppraisals = async () => {
    try {
      setRefreshing(true);

      // Build query parameters for filtering
      const departmentFilter = selectedDepartment ? `&department_id=${selectedDepartment}` : '';
      const periodFilter = selectedPeriod ? `&appraisal_period=${selectedPeriod}` : '';

      const res = await fetch(`${BASE_URL}/hrm/employee-appraisals?is_active=false${departmentFilter}${periodFilter}`, {
        headers: { Authorization: `Token ${token}` },
      });

      if (!res.ok) throw new Error('Failed to fetch completed appraisals');
      const data = await res.json();

      // Fetch additional employee data for enrichment
      const [contractInfo, employeeBioDetails] = await Promise.all([
        fetchContractInfo(),
        fetchEmployeeBioDetails()
      ]);

      // Process and enrich the data
      const processedAppraisals = data.map((record: any) => {
        const employee = employees.find(emp => emp.employee_no === record.employeeid);
        const bioDetails = employeeBioDetails.find((bio: any) => bio.employee_no === record.employeeid);
        const department = departments.find(dept => dept.id === employee?.department_id);

        // Use bio details for proper name formatting
        const employeeName = bioDetails
          ? `${bioDetails.first_name} ${bioDetails.last_name}`.trim()
          : employee
            ? `${employee.first_name} ${employee.last_name}`.trim()
            : record.employeeid;

        const departmentName = department?.name || 'Unknown Department';

        return {
          ...record,
          employee_name: employeeName,
          department_name: departmentName,
          department_id: employee?.department_id || 0,
        };
      });

      setCompletedAppraisals(processedAppraisals);

      // Calculate analytics
      await calculateAnalytics(processedAppraisals);

    } catch (err: any) {
      console.error('Error fetching completed appraisals:', err);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load completed appraisals',
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Calculate comprehensive analytics
  const calculateAnalytics = async (appraisals: CompletedAppraisal[]) => {
    if (!appraisals.length) return;

    // Overall statistics
    setTotalCompletedAppraisals(appraisals.length);

    const validScores = appraisals.filter(a => a.total_supervisor_rating_score > 0);
    const avgScore = validScores.length > 0
      ? validScores.reduce((sum, a) => sum + a.total_supervisor_rating_score, 0) / validScores.length
      : 0;
    setAverageOrganizationScore(Math.round(avgScore * 100) / 100);

    // Fetch contract information and employee bio details for enhanced analytics
    const [contractInfo, employeeBioDetails] = await Promise.all([
      fetchContractInfo(),
      fetchEmployeeBioDetails()
    ]);

    // Department statistics
    const deptStats: { [key: number]: any } = {};
    appraisals.forEach(appraisal => {
      const deptId = appraisal.department_id;
      if (!deptStats[deptId]) {
        deptStats[deptId] = {
          department_id: deptId,
          department_name: appraisal.department_name,
          total_appraisals: 0,
          completed_appraisals: 0,
          total_score: 0,
          valid_scores: 0,
          employees: new Set(),
        };
      }

      deptStats[deptId].total_appraisals++;
      if (appraisal.status === 'Completed') {
        deptStats[deptId].completed_appraisals++;
      }
      if (appraisal.total_supervisor_rating_score > 0) {
        deptStats[deptId].total_score += appraisal.total_supervisor_rating_score;
        deptStats[deptId].valid_scores++;
      }
      deptStats[deptId].employees.add(appraisal.employeeid);
    });

    const departmentStatistics = Object.values(deptStats).map((dept: any) => ({
      department_id: dept.department_id,
      department_name: dept.department_name,
      total_appraisals: dept.total_appraisals,
      completed_appraisals: dept.completed_appraisals,
      average_score: dept.valid_scores > 0 ? Math.round((dept.total_score / dept.valid_scores) * 100) / 100 : 0,
      completion_rate: Math.round((dept.completed_appraisals / dept.total_appraisals) * 100),
      employee_count: dept.employees.size,
    }));

    setDepartmentStats(departmentStatistics);

    // Top performers
    const employeePerformance: { [key: string]: any } = {};
    appraisals.forEach(appraisal => {
      const empId = appraisal.employeeid;
      if (!employeePerformance[empId]) {
        employeePerformance[empId] = {
          employee_id: empId,
          employee_name: appraisal.employee_name,
          department_name: appraisal.department_name,
          scores: [],
          total_appraisals: 0,
        };
      }

      employeePerformance[empId].total_appraisals++;
      if (appraisal.total_supervisor_rating_score > 0) {
        employeePerformance[empId].scores.push(appraisal.total_supervisor_rating_score);
      }
    });

    const topPerformersList = Object.values(employeePerformance)
      .map((emp: any) => {
        const avgScore = emp.scores.length > 0
          ? emp.scores.reduce((sum: number, score: number) => sum + score, 0) / emp.scores.length
          : 0;

        return {
          employee_id: emp.employee_id,
          employee_name: emp.employee_name,
          department_name: emp.department_name,
          total_appraisals: emp.total_appraisals,
          average_score: Math.round(avgScore * 100) / 100,
          latest_score: emp.scores.length > 0 ? emp.scores[emp.scores.length - 1] : 0,
          trend: 'stable' as const,
          performance_category: avgScore >= 90 ? 'excellent' as const :
            avgScore >= 75 ? 'good' as const :
              avgScore >= 60 ? 'average' as const : 'needs_improvement' as const,
        };
      })
      .filter(emp => emp.average_score > 0)
      .sort((a, b) => b.average_score - a.average_score)
      .slice(0, 10);

    setTopPerformers(topPerformersList);

    // Performance trends (by period)
    const periodTrends: { [key: string]: any } = {};
    appraisals.forEach(appraisal => {
      const period = appraisal.period_name || 'Unknown Period';
      if (!periodTrends[period]) {
        periodTrends[period] = {
          period,
          total_appraisals: 0,
          completed_appraisals: 0,
          total_score: 0,
          valid_scores: 0,
        };
      }

      periodTrends[period].total_appraisals++;
      if (appraisal.status === 'Completed') {
        periodTrends[period].completed_appraisals++;
      }
      if (appraisal.total_supervisor_rating_score > 0) {
        periodTrends[period].total_score += appraisal.total_supervisor_rating_score;
        periodTrends[period].valid_scores++;
      }
    });

    const trends = Object.values(periodTrends).map((trend: any) => ({
      period: trend.period,
      average_score: trend.valid_scores > 0 ? Math.round((trend.total_score / trend.valid_scores) * 100) / 100 : 0,
      total_appraisals: trend.total_appraisals,
      completion_rate: Math.round((trend.completed_appraisals / trend.total_appraisals) * 100),
    }));

    setPerformanceTrends(trends);

    // Calculate unique employees with their latest appraisal info
    calculateUniqueEmployees(appraisals, contractInfo, employeeBioDetails);

    // Calculate contract type statistics
    calculateContractTypeStatistics(contractInfo, appraisals, employeeBioDetails);
  };

  // Calculate contract type statistics
  const calculateContractTypeStatistics = (contractInfo: any[], appraisals: CompletedAppraisal[], employeeBioDetails: any[] = []) => {
    const contractStats: { [key: string]: any } = {};

    // Initialize contract type stats
    CONTRACT_TYPES.forEach(type => {
      contractStats[type] = {
        contract_type: type,
        employee_count: 0,
        percentage: 0,
        avg_performance_score: 0,
        pip_count: 0,
        total_score: 0,
        score_count: 0,
      };
    });

    // Count employees by contract type
    contractInfo.forEach(contract => {
      const type = contract.contract_type;
      if (contractStats[type]) {
        contractStats[type].employee_count++;
        if (contract.on_pip) {
          contractStats[type].pip_count++;
        }
      }
    });

    // Add performance scores
    appraisals.forEach(appraisal => {
      const contract = contractInfo.find(c => c.employee_no === appraisal.employeeid);
      if (contract && contractStats[contract.contract_type] && appraisal.total_supervisor_rating_score > 0) {
        contractStats[contract.contract_type].total_score += appraisal.total_supervisor_rating_score;
        contractStats[contract.contract_type].score_count++;
      }
    });

    // Calculate percentages and averages
    const totalEmployees = contractInfo.length;
    const contractStatsArray = Object.values(contractStats).map((stat: any) => ({
      ...stat,
      percentage: totalEmployees > 0 ? Math.round((stat.employee_count / totalEmployees) * 100) : 0,
      avg_performance_score: stat.score_count > 0 ? Math.round((stat.total_score / stat.score_count) * 100) / 100 : 0,
    })).filter(stat => stat.employee_count > 0);

    setContractTypeStats(contractStatsArray);
  };

  // Calculate unique employees for employee-first view
  const calculateUniqueEmployees = (appraisals: CompletedAppraisal[], contractInfo: any[] = [], employeeBioDetails: any[] = []) => {
    const employeeMap: { [key: string]: any } = {};

    appraisals.forEach(appraisal => {
      const empId = appraisal.employeeid;
      const contract = contractInfo.find(c => c.employee_no === empId);
      const bioDetails = employeeBioDetails.find(bio => bio.employee_no === empId);
      const employee = employees.find(emp => emp.employee_no === empId);
      const department = departments.find(dept => dept.id === employee?.department_id);

      if (!employeeMap[empId]) {
        // Use bio details for proper name formatting
        const employeeName = bioDetails
          ? `${bioDetails.first_name} ${bioDetails.last_name}`.trim()
          : employee
            ? `${employee.first_name} ${employee.last_name}`.trim()
            : appraisal.employee_name || empId;

        const departmentName = department?.name || appraisal.department_name || 'Unknown Department';

        employeeMap[empId] = {
          employee_id: empId,
          employee_name: employeeName,
          department_name: departmentName,
          department_id: employee?.department_id || appraisal.department_id || 0,
          contract_type: contract?.contract_type || 'Unknown',
          total_appraisals: 0,
          latest_appraisal: null,
          latest_score: 0,
          average_score: 0,
          scores: [],
          periods: [],
          pip_status: contract?.on_pip || false,
          probation_status: false,
          performance_trend: 'stable' as const,
        };
      }

      employeeMap[empId].total_appraisals++;
      employeeMap[empId].periods.push({
        period_name: appraisal.period_name,
        cycle_type: appraisal.cycle_type,
        score: appraisal.total_supervisor_rating_score,
        date: appraisal.closed_date,
        way_forward: appraisal.way_forward,
      });

      if (appraisal.total_supervisor_rating_score > 0) {
        employeeMap[empId].scores.push(appraisal.total_supervisor_rating_score);
      }

      // Check for PIP/Probation status based on way_forward or cycle_type
      if (appraisal.way_forward?.toLowerCase().includes('pip') ||
        appraisal.cycle_type?.toLowerCase().includes('pip')) {
        employeeMap[empId].pip_status = true;
      }

      if (appraisal.way_forward?.toLowerCase().includes('probation') ||
        appraisal.cycle_type?.toLowerCase().includes('probation')) {
        employeeMap[empId].probation_status = true;
      }

      // Update latest appraisal if this one is more recent
      if (!employeeMap[empId].latest_appraisal ||
        new Date(appraisal.closed_date) > new Date(employeeMap[empId].latest_appraisal.closed_date)) {
        employeeMap[empId].latest_appraisal = appraisal;
        employeeMap[empId].latest_score = appraisal.total_supervisor_rating_score;
      }
    });

    // Calculate averages and trends
    const uniqueEmployeesList = Object.values(employeeMap).map((emp: any) => {
      const avgScore = emp.scores.length > 0
        ? emp.scores.reduce((sum: number, score: number) => sum + score, 0) / emp.scores.length
        : 0;

      emp.average_score = Math.round(avgScore * 100) / 100;

      // Calculate performance trend
      if (emp.scores.length >= 2) {
        const recent = emp.scores.slice(-2);
        if (recent[1] > recent[0]) {
          emp.performance_trend = 'up';
        } else if (recent[1] < recent[0]) {
          emp.performance_trend = 'down';
        }
      }

      return emp;
    });

    // Separate PIP employees and sort them by latest score (lowest first)
    const pipEmployees = uniqueEmployeesList
      .filter(emp => emp.pip_status)
      .sort((a, b) => a.latest_score - b.latest_score);

    setEmployeesOnPip(pipEmployees);
    setUniqueEmployees(uniqueEmployeesList);

    // Calculate pagination
    const totalItems = uniqueEmployeesList.length;
    setTotalPages(Math.ceil(totalItems / itemsPerPage));
  };

  // Fetch specific employee's appraisal history
  const fetchEmployeeAppraisalHistory = async (employeeId: string) => {
    try {
      setRefreshing(true);

      const [appraisalRes, contractInfo, employeeBioDetails] = await Promise.all([
        fetch(`${BASE_URL}/hrm/employee-appraisals?employeeid=${employeeId}&is_active=false`, {
          headers: { Authorization: `Token ${token}` },
        }),
        fetchContractInfo(),
        fetchEmployeeBioDetails()
      ]);

      if (!appraisalRes.ok) throw new Error('Failed to fetch employee appraisal history');
      const data = await appraisalRes.json();

      // Process and enrich the data
      const processedHistory = data.map((record: any) => {
        const employee = employees.find(emp => emp.employee_no === record.employeeid);
        const bioDetails = employeeBioDetails.find((bio: any) => bio.employee_no === record.employeeid);
        const department = departments.find(dept => dept.id === employee?.department_id);

        // Use bio details for proper name formatting
        const employeeName = bioDetails
          ? `${bioDetails.first_name} ${bioDetails.last_name}`.trim()
          : employee
            ? `${employee.first_name} ${employee.last_name}`.trim()
            : record.employeeid;

        const departmentName = department?.name || 'Unknown Department';

        return {
          ...record,
          employee_name: employeeName,
          department_name: departmentName,
          department_id: employee?.department_id || 0,
        };
      });

      setEmployeeAppraisalHistory(processedHistory);

    } catch (err: any) {
      console.error('Error fetching employee appraisal history:', err);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load employee appraisal history',
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Handle employee selection
  const handleEmployeeSelect = async (employee: any) => {
    setSelectedEmployee(employee);
    setShowEmployeeHistory(true);
    await fetchEmployeeAppraisalHistory(employee.employee_id);
  };

  // Main data fetching function
  const fetchAllData = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch base data first
      await Promise.all([
        fetchEmployees(),
        fetchDepartments()
      ]);

    } catch (err: any) {
      setError(err.message || 'Failed to load initial data');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Unable to fetch initial data',
      });
    } finally {
      setLoading(false);
    }
  };

  // Refresh all analytics data
  const refreshAnalytics = async () => {
    if (employees.length > 0 && departments.length > 0) {
      await fetchCompletedAppraisals();
    }
  };

  // UseEffect hooks
  useEffect(() => {
    if (token) {
      fetchAllData();
    }
  }, [token]);

  // Fetch completed appraisals when employees and departments are loaded
  useEffect(() => {
    if (employees.length > 0 && departments.length > 0) {
      fetchCompletedAppraisals();
    }
  }, [employees, departments, selectedDepartment, selectedPeriod]);

  // Filter completed appraisals based on search term
  const filteredAppraisals = completedAppraisals.filter(appraisal => {
    const searchLower = searchTerm.toLowerCase();
    return (
      appraisal.employee_name.toLowerCase().includes(searchLower) ||
      appraisal.department_name.toLowerCase().includes(searchLower) ||
      appraisal.employeeid.toLowerCase().includes(searchLower) ||
      appraisal.period_name?.toLowerCase().includes(searchLower)
    );
  });

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="/" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href="/performance-dashboard" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Performance
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Performance Analytics</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  return (
    <div className="flex-1 space-y-6 p-2 md:p-2">
      {/* Header */}
      <div className="flex items-center justify-between">
        {breadcrumb}
        <div className="flex items-center gap-2">
          <Button
            onClick={refreshAnalytics}
            variant="outline"
            size="sm"
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh Analytics
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center p-12">
          <Loader2 className="animate-spin h-10 w-10 text-green-600" />
        </div>
      ) : error ? (
        <Card className="border-red-200">
          <CardContent className="flex items-center justify-center p-6 text-red-500">
            <AlertCircle className="mr-2" size={24} />
            <p>{error}</p>
            <Button
              onClick={() => {
                setLoading(true);
                setError('');
                fetchAllData();
              }}
              variant="outline"
              size="sm"
              className="ml-4"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Navigation Tabs */}
          <div className="flex flex-wrap gap-2 mb-6">
            <Button
              variant={currentView === 'overview' ? 'default' : 'outline'}
              onClick={() => setCurrentView('overview')}
              className="flex items-center gap-2"
            >
              <BarChart3 className="h-4 w-4" />
              Overview
            </Button>
            <Button
              variant={currentView === 'pip' ? 'default' : 'outline'}
              onClick={() => setCurrentView('pip')}
              className="flex items-center gap-2"
            >
              <AlertCircle className="h-4 w-4" />
              PIP Employees ({employeesOnPip.length})
            </Button>
            <Button
              variant={currentView === 'employees' ? 'default' : 'outline'}
              onClick={() => setCurrentView('employees')}
              className="flex items-center gap-2"
            >
              <Users className="h-4 w-4" />
              All Employees ({uniqueEmployees.length})
            </Button>
            <Button
              variant={currentView === 'analytics' ? 'default' : 'outline'}
              onClick={() => setCurrentView('analytics')}
              className="flex items-center gap-2"
            >
              <TrendingUp className="h-4 w-4" />
              Analytics
            </Button>
          </div>

          {/* Key Performance Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Total Completed
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalCompletedAppraisals}</div>
                <p className="text-xs opacity-80">Appraisals</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center">
                  <Target className="mr-2 h-4 w-4" />
                  Avg Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{averageOrganizationScore}%</div>
                <p className="text-xs opacity-80">Organization</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-red-500 to-red-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center">
                  <AlertCircle className="mr-2 h-4 w-4" />
                  PIP Employees
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{employeesOnPip.length}</div>
                <p className="text-xs opacity-80">Need attention</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center">
                  <Users className="mr-2 h-4 w-4" />
                  Total Employees
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{uniqueEmployees.length}</div>
                <p className="text-xs opacity-80">With appraisals</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center">
                  <Star className="mr-2 h-4 w-4" />
                  Top Performers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{topPerformers.length}</div>
                <p className="text-xs opacity-80">High achievers</p>
              </CardContent>
            </Card>
          </div>

          {/* Conditional Content Based on Current View */}
          {currentView === 'pip' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-red-600">
                  <AlertCircle className="mr-2 h-5 w-5" />
                  Employees on Performance Improvement Plan (PIP)
                </CardTitle>
                <CardDescription>
                  {employeesOnPip.length} employees currently require performance improvement attention
                </CardDescription>
              </CardHeader>
              <CardContent>
                {employeesOnPip.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle2 className="mx-auto h-12 w-12 text-green-500 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Employees on PIP</h3>
                    <p className="text-gray-600">Great news! No employees are currently on Performance Improvement Plans.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {employeesOnPip.map((employee) => (
                      <Card key={employee.employee_id} className="border-red-200 bg-red-50">
                        <CardContent className="p-4">
                          <div className="flex items-center space-x-3 mb-3">
                            <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white font-bold">
                              {employee.employee_name.split(' ').map((n: string) => n[0]).join('').substring(0, 2)}
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900">{employee.employee_name}</h3>
                              <p className="text-sm text-gray-600">{employee.employee_id}</p>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Department:</span>
                              <span className="font-medium">{employee.department_name}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Contract Type:</span>
                              <span className="font-medium">{employee.contract_type}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Latest Score:</span>
                              <Badge variant="destructive">{employee.latest_score}%</Badge>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Average Score:</span>
                              <Badge variant="outline">{employee.average_score}%</Badge>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Total Appraisals:</span>
                              <span className="font-medium">{employee.total_appraisals}</span>
                            </div>
                          </div>

                          <div className="mt-4 flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1"
                              onClick={() => handleEmployeeSelect(employee)}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              View History
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {currentView === 'overview' && (
            <>
              {/* Performance Insights Dashboard */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                {/* Performance Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-sm">
                      <TrendingUp className="mr-2 h-4 w-4" />
                      Performance Distribution
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Excellent (90%+)</span>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div className="bg-green-500 h-2 rounded-full" style={{width: `${(uniqueEmployees.filter(e => e.average_score >= 90).length / uniqueEmployees.length) * 100}%`}}></div>
                          </div>
                          <span className="text-sm font-medium">{uniqueEmployees.filter(e => e.average_score >= 90).length}</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Good (75-89%)</span>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-500 h-2 rounded-full" style={{width: `${(uniqueEmployees.filter(e => e.average_score >= 75 && e.average_score < 90).length / uniqueEmployees.length) * 100}%`}}></div>
                          </div>
                          <span className="text-sm font-medium">{uniqueEmployees.filter(e => e.average_score >= 75 && e.average_score < 90).length}</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Average (60-74%)</span>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div className="bg-yellow-500 h-2 rounded-full" style={{width: `${(uniqueEmployees.filter(e => e.average_score >= 60 && e.average_score < 75).length / uniqueEmployees.length) * 100}%`}}></div>
                          </div>
                          <span className="text-sm font-medium">{uniqueEmployees.filter(e => e.average_score >= 60 && e.average_score < 75).length}</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Needs Improvement</span>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div className="bg-red-500 h-2 rounded-full" style={{width: `${(uniqueEmployees.filter(e => e.average_score < 60).length / uniqueEmployees.length) * 100}%`}}></div>
                          </div>
                          <span className="text-sm font-medium">{uniqueEmployees.filter(e => e.average_score < 60).length}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Department Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-sm">
                      <Building2 className="mr-2 h-4 w-4" />
                      Department Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Total Departments</span>
                        <Badge variant="outline">{departments.length}</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Avg Dept Performance</span>
                        <Badge variant="secondary">
                          {departmentStats.length > 0 ? Math.round(departmentStats.reduce((sum, dept) => sum + dept.average_score, 0) / departmentStats.length) : 0}%
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Best Performing</span>
                        <span className="text-xs font-medium text-green-600">
                          {departmentStats.length > 0 ? departmentStats.sort((a, b) => b.average_score - a.average_score)[0]?.department_name?.substring(0, 15) + '...' : 'N/A'}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Needs Attention</span>
                        <span className="text-xs font-medium text-red-600">
                          {departmentStats.length > 0 ? departmentStats.sort((a, b) => a.average_score - b.average_score)[0]?.department_name?.substring(0, 15) + '...' : 'N/A'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Trends */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-sm">
                      <Calendar className="mr-2 h-4 w-4" />
                      Recent Trends
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Improving Performance</span>
                        <div className="flex items-center gap-1">
                          <ArrowUpRight className="h-3 w-3 text-green-500" />
                          <span className="text-sm font-medium text-green-600">{uniqueEmployees.filter(e => e.performance_trend === 'up').length}</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Declining Performance</span>
                        <div className="flex items-center gap-1">
                          <ArrowDownRight className="h-3 w-3 text-red-500" />
                          <span className="text-sm font-medium text-red-600">{uniqueEmployees.filter(e => e.performance_trend === 'down').length}</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Stable Performance</span>
                        <span className="text-sm font-medium text-gray-600">{uniqueEmployees.filter(e => e.performance_trend === 'stable').length}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Completion Rate</span>
                        <Badge variant="default">
                          {departmentStats.length > 0 ? Math.round(departmentStats.reduce((sum, dept) => sum + dept.completion_rate, 0) / departmentStats.length) : 0}%
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Contract Type Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <UserCheck className="mr-2 h-5 w-5" />
                    Employee Contract Type Distribution
                  </CardTitle>
                  <CardDescription>Performance statistics by contract type</CardDescription>
                </CardHeader>
                <CardContent>
                  {contractTypeStats.length === 0 ? (
                    <div className="text-center py-8">
                      <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Contract Data</h3>
                      <p className="text-gray-600">Contract type statistics will appear here once data is loaded.</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {contractTypeStats.map((stat) => (
                        <Card key={stat.contract_type} className="border border-gray-200">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="font-semibold text-gray-900">{stat.contract_type}</h3>
                              <Badge variant="secondary">{stat.percentage}%</Badge>
                            </div>

                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Employees:</span>
                                <span className="font-medium">{stat.employee_count}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Avg Performance:</span>
                                <Badge variant={stat.avg_performance_score >= 75 ? "default" : stat.avg_performance_score >= 60 ? "secondary" : "destructive"}>
                                  {stat.avg_performance_score}%
                                </Badge>
                              </div>
                              {stat.pip_count > 0 && (
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">On PIP:</span>
                                  <Badge variant="destructive">{stat.pip_count}</Badge>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Filters Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Filter className="mr-2 h-5 w-5" />
                    Analytics Filters
                  </CardTitle>
                  <CardDescription>Filter performance data by department, period, and date range</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1 text-gray-700">Department</label>
                      <select
                        value={selectedDepartment}
                        onChange={(e) => setSelectedDepartment(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      >
                        <option value="">All Departments</option>
                        {departments.map((dept) => (
                          <option key={dept.id} value={dept.id}>
                            {dept.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1 text-gray-700">Date Range</label>
                      <select
                        value={selectedDateRange}
                        onChange={(e) => setSelectedDateRange(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      >
                        <option value="last_3_months">Last 3 Months</option>
                        <option value="last_6_months">Last 6 Months</option>
                        <option value="last_year">Last Year</option>
                        <option value="all_time">All Time</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1 text-gray-700">Search</label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <input
                          type="text"
                          placeholder="Search employees..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      </div>
                    </div>

                    <div className="flex items-end">
                      <Button
                        onClick={() => {
                          setSelectedDepartment('');
                          setSelectedPeriod('');
                          setSelectedDateRange('last_6_months');
                          setSearchTerm('');
                        }}
                        variant="outline"
                        className="w-full"
                      >
                        Reset Filters
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}

          {currentView === 'employees' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Users className="mr-2 h-5 w-5" />
                    All Employees Performance
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, uniqueEmployees.length)} of {uniqueEmployees.length}
                    </span>
                  </div>
                </CardTitle>
                <CardDescription>
                  Paginated view of all employees with performance data
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search Bar */}
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="Search employees by name, ID, or department..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                </div>

                {/* Employee Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                  {uniqueEmployees
                    .filter(employee => {
                      const searchLower = searchTerm.toLowerCase();
                      return (
                        employee.employee_name.toLowerCase().includes(searchLower) ||
                        employee.department_name.toLowerCase().includes(searchLower) ||
                        employee.employee_id.toLowerCase().includes(searchLower)
                      );
                    })
                    .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                    .map((employee) => (
                      <Card
                        key={employee.employee_id}
                        className={`border hover:shadow-md transition-all cursor-pointer ${
                          employee.pip_status ? 'border-red-200 bg-red-50' : 'border-gray-200'
                        }`}
                        onClick={() => handleEmployeeSelect(employee)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center space-x-3 mb-3">
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
                              employee.pip_status ? 'bg-red-500' : 'bg-gradient-to-br from-blue-500 to-purple-600'
                            }`}>
                              {employee.employee_name.split(' ').map((n: string) => n[0]).join('').substring(0, 2)}
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900">{employee.employee_name}</h3>
                              <p className="text-sm text-gray-600">{employee.employee_id}</p>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Department:</span>
                              <span className="font-medium text-xs">{employee.department_name}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Contract:</span>
                              <span className="font-medium text-xs">{employee.contract_type}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Avg Score:</span>
                              <Badge variant={employee.average_score >= 75 ? "default" : employee.average_score >= 60 ? "secondary" : "destructive"}>
                                {employee.average_score}%
                              </Badge>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Latest:</span>
                              <div className="flex items-center gap-1">
                                <Badge variant="outline">{employee.latest_score}%</Badge>
                                {employee.performance_trend === 'up' && <ArrowUpRight className="h-3 w-3 text-green-500" />}
                                {employee.performance_trend === 'down' && <ArrowDownRight className="h-3 w-3 text-red-500" />}
                              </div>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600 text-sm">Status:</span>
                              <div className="flex gap-1">
                                {employee.pip_status && <Badge variant="destructive" className="text-xs">PIP</Badge>}
                                {employee.probation_status && <Badge variant="outline" className="text-xs">Probation</Badge>}
                                <Badge variant="secondary" className="text-xs">{employee.total_appraisals} appraisals</Badge>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </Button>

                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          const page = i + 1;
                          return (
                            <Button
                              key={page}
                              variant={currentPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => setCurrentPage(page)}
                            >
                              {page}
                            </Button>
                          );
                        })}
                        {totalPages > 5 && (
                          <>
                            <span className="px-2">...</span>
                            <Button
                              variant={currentPage === totalPages ? "default" : "outline"}
                              size="sm"
                              onClick={() => setCurrentPage(totalPages)}
                            >
                              {totalPages}
                            </Button>
                          </>
                        )}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="text-sm text-gray-600">
                      Page {currentPage} of {totalPages}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {currentView === 'analytics' && (
            <>
              {/* Top Performers */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Award className="mr-2 h-5 w-5" />
                    Top Performers
                  </CardTitle>
                  <CardDescription>Highest performing employees across the organization</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {topPerformers.slice(0, 10).map((performer, index) => (
                      <div key={performer.employee_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${index === 0 ? 'bg-yellow-500' :
                            index === 1 ? 'bg-gray-400' :
                              index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                            }`}>
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{performer.employee_name}</p>
                            <p className="text-sm text-gray-600">{performer.department_name}</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">{performer.average_score}%</p>
                            <p className="text-xs text-gray-500">{performer.total_appraisals} appraisals</p>
                          </div>

                          <Badge variant={
                            performer.performance_category === 'excellent' ? 'default' :
                              performer.performance_category === 'good' ? 'secondary' :
                                performer.performance_category === 'average' ? 'outline' : 'destructive'
                          }>
                            {performer.performance_category.replace('_', ' ')}
                          </Badge>

                          {performer.trend === 'up' && <ArrowUpRight className="h-4 w-4 text-green-500" />}
                          {performer.trend === 'down' && <ArrowDownRight className="h-4 w-4 text-red-500" />}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Creative Performance Trends */}
              <CreativePerformanceTrends
                employees={uniqueEmployees}
                appraisalData={completedAppraisals}
              />
            </>
          )}

          {/* Performance Timeline for Selected Employee */}
          {showEmployeeHistory && selectedEmployee && (
            <>
              {/* Employee History View */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <CheckCircle2 className="mr-2 h-5 w-5" />
                      Employee Performance History
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowEmployeeHistory(false)}
                    >
                      Back to Overview
                    </Button>
                  </CardTitle>
                  <CardDescription>
                    Performance history for {selectedEmployee.employee_name} ({employeeAppraisalHistory.length} records)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedEmployee && (
                      <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                              {selectedEmployee.employee_name.split(' ').map((n: string) => n[0]).join('').substring(0, 2)}
                            </div>
                            <div>
                              <h2 className="text-xl font-bold text-gray-900">{selectedEmployee.employee_name}</h2>
                              <p className="text-gray-600">{selectedEmployee.employee_id} • {selectedEmployee.department_name}</p>
                              <div className="flex items-center gap-2 mt-2">
                                {selectedEmployee.pip_status && (
                                  <Badge variant="destructive">Currently on PIP</Badge>
                                )}
                                {selectedEmployee.probation_status && (
                                  <Badge variant="outline">On Probation</Badge>
                                )}
                                <Badge variant="secondary">
                                  {selectedEmployee.total_appraisals} Total Appraisals
                                </Badge>
                              </div>
                            </div>
                          </div>

                          <div className="text-right">
                            <p className="text-sm text-gray-600">Overall Average</p>
                            <p className="text-3xl font-bold text-gray-900">{selectedEmployee.average_score}%</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Appraisal History Table */}
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="border-b border-gray-200">
                            <th className="text-left p-3 font-medium text-gray-900">Period</th>
                            <th className="text-left p-3 font-medium text-gray-900">Cycle Type</th>
                            <th className="text-left p-3 font-medium text-gray-900">Score</th>
                            <th className="text-left p-3 font-medium text-gray-900">Status</th>
                            <th className="text-left p-3 font-medium text-gray-900">Completed</th>
                            <th className="text-left p-3 font-medium text-gray-900">Way Forward</th>
                            <th className="text-left p-3 font-medium text-gray-900">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {employeeAppraisalHistory.map((appraisal) => (
                            <tr key={appraisal.id} className="border-b border-gray-100 hover:bg-gray-50">
                              <td className="p-3">
                                <div>
                                  <p className="font-medium text-gray-900">{appraisal.period_name}</p>
                                  <p className="text-xs text-gray-500">
                                    {new Date(appraisal.start_date).toLocaleDateString()} - {new Date(appraisal.end_date).toLocaleDateString()}
                                  </p>
                                </div>
                              </td>
                              <td className="p-3">
                                <Badge variant={
                                  appraisal.cycle_type?.toLowerCase().includes('pip') ? 'destructive' :
                                    appraisal.cycle_type?.toLowerCase().includes('probation') ? 'outline' : 'secondary'
                                }>
                                  {appraisal.cycle_type}
                                </Badge>
                              </td>
                              <td className="p-3">
                                <Badge variant={
                                  appraisal.total_supervisor_rating_score >= 90 ? 'default' :
                                    appraisal.total_supervisor_rating_score >= 75 ? 'secondary' :
                                      appraisal.total_supervisor_rating_score >= 60 ? 'outline' : 'destructive'
                                }>
                                  {appraisal.total_supervisor_rating_score}%
                                </Badge>
                              </td>
                              <td className="p-3">
                                <Badge variant="default">{appraisal.status}</Badge>
                              </td>
                              <td className="p-3 text-sm text-gray-600">
                                {new Date(appraisal.closed_date).toLocaleDateString()}
                              </td>
                              <td className="p-3">
                                <p className="text-xs text-gray-600 max-w-xs truncate" title={appraisal.way_forward}>
                                  {appraisal.way_forward || 'N/A'}
                                </p>
                              </td>
                              <td className="p-3">
                                <Button variant="outline" size="sm" className="flex items-center gap-1">
                                  <Eye className="h-3 w-3" />
                                  Details
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <PerformanceTimeline
                employee={selectedEmployee}
                appraisalHistory={employeeAppraisalHistory}
              />
            </>
          )}
        </>
      )}
    </div>
  );
};

export default PerformanceAnalytics;
