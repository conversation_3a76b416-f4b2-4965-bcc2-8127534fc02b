import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { BASE_URL } from "../../config";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  RefreshCw,
  Pencil,
  Trash2,
  Plus,
  CheckCircle2,
  Search,
  ArrowUpDown,
  AlertCircle,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

// Define the Permission interface (as per your API docs)
interface Permission {
  id: number;
  permission_name: string;
  permission_description?: string | null;
}

// Form schema for permission creation/updating
const permissionSchema = z.object({
  permission_name: z
    .string()
    .min(1, "Permission name is required")
    .max(50, "Permission name must be less than 50 characters"),
  permission_description: z.string().min(1, "Description is required").optional(),
});

export default function PermissionsManagement() {
  const navigate = useNavigate();
  const { token } = useSelector((state: RootState) => state.auth);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentPermission, setCurrentPermission] = useState<Permission | null>(null);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState<{ key: keyof Permission; direction: "ascending" | "descending" } | null>(null);
  const [filteredPermissions, setFilteredPermissions] = useState<Permission[]>([]);

  // Initialize react-hook-form
  const form = useForm<z.infer<typeof permissionSchema>>({
    resolver: zodResolver(permissionSchema),
    defaultValues: {
      permission_name: "",
      permission_description: "",
    },
  });

  // Reset form when dialog closes
  useEffect(() => {
    if (!isAddDialogOpen && !isEditDialogOpen) {
      form.reset();
      setFormError("");
    }
  }, [isAddDialogOpen, isEditDialogOpen, form]);

  // Fetch permissions list from the API
  const fetchPermissions = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await axios.get(`${BASE_URL}/users/permissions`, {
        headers: { Authorization: `Token ${token}` },
      });
      setPermissions(response.data);
      setFilteredPermissions(response.data);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching permissions:", err);
      setError("Failed to load permissions. Please try again.");
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchPermissions();
    }
  }, [token]);

  // Filtering and sorting
  useEffect(() => {
    let result = [...permissions];
    if (searchTerm) {
      result = result.filter(
        (perm) =>
          perm.permission_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (perm.permission_description &&
            perm.permission_description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    if (sortConfig !== null) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.key] ?? "";
        const bValue = b[sortConfig.key] ?? "";
        if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
        return 0;
      });
    }
    setFilteredPermissions(result);
  }, [permissions, searchTerm, sortConfig]);

  const requestSort = (key: keyof Permission) => {
    let direction: "ascending" | "descending" = "ascending";
    if (sortConfig && sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  // Handle form submission for adding a permission
  const onSubmitAdd = async (data: z.infer<typeof permissionSchema>) => {
    try {
      setIsSubmitting(true);
      setFormError("");
      await axios.post(`${BASE_URL}/users/permissions`, data, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchPermissions();
      setIsAddDialogOpen(false);
      setSuccessMessage("Permission created successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err: any) {
      console.error("Error creating permission:", err);
      setFormError(err.response?.data?.message || "Failed to create permission. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission for editing a permission
  const onSubmitEdit = async (data: z.infer<typeof permissionSchema>) => {
    if (!currentPermission) return;
    try {
      setIsSubmitting(true);
      setFormError("");
      await axios.patch(`${BASE_URL}/users/permissions/${currentPermission.id}`, data, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchPermissions();
      setIsEditDialogOpen(false);
      setSuccessMessage("Permission updated successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err: any) {
      console.error("Error updating permission:", err);
      setFormError(err.response?.data?.message || "Failed to update permission. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deletion of a permission
  const handleDelete = async () => {
    if (!deleteId) return;
    try {
      setIsSubmitting(true);
      await axios.delete(`${BASE_URL}/users/permissions/${deleteId}`, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchPermissions();
      setDeleteId(null);
      setSuccessMessage("Permission deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err: any) {
      console.error("Error deleting permission:", err);
      setError(err.response?.data?.message || "Failed to delete permission. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handler to populate the form for editing a permission
  const handleEdit = (perm: Permission) => {
    setCurrentPermission(perm);
    form.reset({
      permission_name: perm.permission_name,
      permission_description: perm.permission_description || "",
    });
    setIsEditDialogOpen(true);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSortConfig(null);
  };

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink href="/" className="text-gray-500 hover:text-gray-700">
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block" />
        <BreadcrumbItem>
          <BreadcrumbLink href="/dashboard" className="text-gray-500 hover:text-gray-700">
            Dashboard
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Permissions</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  if (loading && permissions.length === 0) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-gray-600">Loading permissions...</p>
          </div>
        </div>
      </Screen>
    );
  }

  if (error && permissions.length === 0) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="bg-red-50 border border-red-200 p-6 rounded-lg max-w-md text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-red-700 mb-2">Error Loading Permissions</h2>
            <p className="text-gray-700">{error}</p>
            <button
              onClick={() => fetchPermissions()}
              className="mt-4 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
            >
              <RefreshCw className="w-4 h-4 inline-block mr-2" />
              Retry
            </button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen headerContent={breadcrumb}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Plus className="w-7 h-7 mr-2 text-green-600" />
              Permissions Management
            </h1>
            <p className="text-gray-600 mt-1">Manage and configure system permissions.</p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Permission
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Add New Permission</DialogTitle>
                <DialogDescription>
                  Provide details for the new permission.
                </DialogDescription>
              </DialogHeader>
              {formError && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                  <p className="text-red-600 text-sm">{formError}</p>
                </div>
              )}
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmitAdd)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="permission_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Permission Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter permission name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="permission_description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Permission Description</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter permission description" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        "Create Permission"
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
        {successMessage && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-center">
            <CheckCircle2 className="w-5 h-5 text-green-600 mr-3" />
            <p className="text-green-600">{successMessage}</p>
          </div>
        )}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col lg:flex-row gap-4 lg:items-center justify-between">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search permissions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Permissions ({filteredPermissions.length})</span>
              {loading && (
                <div className="flex items-center text-sm text-gray-500">
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Refreshing data...
                </div>
              )}
            </CardTitle>
            <CardDescription>
              Overview of system permissions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredPermissions.length === 0 ? (
              <div className="text-center py-8">
                <Plus className="w-12 h-12 mx-auto text-gray-400 mb-3" />
                <h3 className="text-lg font-medium text-gray-900">No permissions found</h3>
                <p className="text-gray-500 mt-1">
                  {searchTerm ? "Try adjusting your search filters" : "Create a permission to get started"}
                </p>
                {searchTerm && (
                  <Button variant="outline" onClick={clearFilters} className="mt-4">
                    Clear Filters
                  </Button>
                )}
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => requestSort("permission_name")}
                      >
                        <div className="flex items-center">
                          Name
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPermissions.map((perm) => (
                      <TableRow key={perm.id}>
                        <TableCell className="font-medium">{perm.permission_name}</TableCell>
                        <TableCell className="max-w-xs truncate">{perm.permission_description}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Dialog
                              open={isEditDialogOpen && currentPermission?.id === perm.id}
                              onOpenChange={setIsEditDialogOpen}
                            >
                              <DialogTrigger asChild>
                                <Button variant="outline" size="icon" onClick={() => handleEdit(perm)}>
                                  <Pencil className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-md">
                                <DialogHeader>
                                  <DialogTitle>Edit Permission</DialogTitle>
                                  <DialogDescription>
                                    Update details for {currentPermission?.permission_name}
                                  </DialogDescription>
                                </DialogHeader>
                                {formError && (
                                  <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                                    <p className="text-red-600 text-sm">{formError}</p>
                                  </div>
                                )}
                                <Form {...form}>
                                  <form onSubmit={form.handleSubmit(onSubmitEdit)} className="space-y-4">
                                    <FormField
                                      control={form.control}
                                      name="permission_name"
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Permission Name</FormLabel>
                                          <FormControl>
                                            <Input placeholder="Enter permission name" {...field} />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />
                                    <FormField
                                      control={form.control}
                                      name="permission_description"
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Permission Description</FormLabel>
                                          <FormControl>
                                            <Input placeholder="Enter permission description" {...field} />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />
                                    <DialogFooter>
                                      <Button type="submit" disabled={isSubmitting}>
                                        {isSubmitting ? (
                                          <>
                                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                            Updating...
                                          </>
                                        ) : (
                                          "Update Permission"
                                        )}
                                      </Button>
                                    </DialogFooter>
                                  </form>
                                </Form>
                              </DialogContent>
                            </Dialog>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="outline" size="icon" onClick={() => setDeleteId(perm.id)}>
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action will permanently delete the permission.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel onClick={() => setDeleteId(null)}>
                                    Cancel
                                  </AlertDialogCancel>
                                  <AlertDialogAction onClick={handleDelete} disabled={isSubmitting}>
                                    {isSubmitting ? (
                                      <>
                                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Screen>
  );
}
