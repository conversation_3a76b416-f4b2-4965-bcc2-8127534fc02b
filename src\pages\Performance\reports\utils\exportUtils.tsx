import * as XLSX from 'xlsx';
import { toast } from '@/hooks/use-toast';

// Individual Performance Export
export const exportIndividualPerformanceToExcel = async (data: any[], reportType: string = 'Individual Performance') => {
  const workbook = XLSX.utils.book_new();

  // Create a separate sheet for each employee following the template format
  data.forEach((employee) => {
    const sheetData: any[][] = [];

    // Company Header Section
    sheetData.push(['OPTIVEN LIMITED']);
    sheetData.push(['Absa Tower 2nd floor, Loita St']);
    sheetData.push(['P.O BOX: 6525-00600 Nairobi']);
    sheetData.push(['E-Mail: <EMAIL>']);
    sheetData.push(['Mobile: 0790600600 / 0711601019']);
    sheetData.push([]); // Empty row

    // Staff Information Section
    sheetData.push(['Staff No.', employee.employeeid_id]);
    sheetData.push(['Staff Name', `${employee.first_name} ${employee.last_name}`]);
    sheetData.push(['Designation', 'Performance Analyst']); // This would come from job info
    sheetData.push(['Email Address', '<EMAIL>']); // This would come from employee data
    sheetData.push(['Staff Rating', employee.total_emp_self_rating_score || 'N/A']);
    sheetData.push(['Staff Comments', '']); // Placeholder
    sheetData.push(['H1. Rating', employee.total_supervisor_rating_score || 'N/A']);
    sheetData.push(['H1. Comments', employee.final_supervisor_comments || 'N/A']);
    sheetData.push([]); // Empty row

    // Performance Categories Header
    sheetData.push([
      'NO.',
      'Department',
      'Staff No.',
      'Staff Name',
      'Designation',
      'Email Address',
      'Staff Rating',
      'Staff Comments',
      'H1. Rating',
      'H1. Comments'
    ]);

    // Add KPI data
    employee.grouped_kpis.forEach((kpi: any, index: number) => {
      sheetData.push([
        index + 1,
        'ICT SYSTEMS', // This would come from department data
        employee.employeeid_id,
        `${employee.first_name} ${employee.last_name}`,
        'Performance Analyst', // This would come from job info
        '<EMAIL>', // This would come from employee data
        kpi.employee_rating || '',
        kpi.employee_comments || '',
        kpi.supervisor_rating || '',
        kpi.supervisor_comments || ''
      ]);

      // Add detailed performance areas
      kpi.how_items.forEach((howItem: any) => {
        const performanceDetails = [];
        if (howItem.MIB_target) {
          performanceDetails.push(`MIB Target: ${howItem.MIB_target}, Achieved: ${howItem.MIB_achieved || 0}`);
        }
        if (howItem.Sales_target) {
          performanceDetails.push(`Sales Target: ${howItem.Sales_target}, Achieved: ${howItem.Sales_achieved || 0}`);
        }
        
        if (performanceDetails.length > 0) {
          sheetData.push([
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            performanceDetails.join('; '),
            '',
            ''
          ]);
        }
      });
    });

    // Summary Section
    sheetData.push([]); // Empty row
    sheetData.push(['SUMMARY']);
    sheetData.push(['Total Self Rating Score', employee.total_emp_self_rating_score || 'N/A']);
    sheetData.push(['Total Supervisor Rating Score', employee.total_supervisor_rating_score || 'N/A']);
    sheetData.push(['Status', employee.status]);
    sheetData.push([]); // Empty row

    // Final Comments Section
    sheetData.push(['Final Supervisor Comments']);
    sheetData.push([employee.final_supervisor_comments || 'N/A']);
    sheetData.push([]); // Empty row
    sheetData.push(['Final HR Comments']);
    sheetData.push([employee.final_hr_comments || 'N/A']);

    // Create worksheet
    const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

    // Set column widths to match template
    worksheet['!cols'] = [
      { wch: 5 },   // NO.
      { wch: 15 },  // Department
      { wch: 12 },  // Staff No.
      { wch: 20 },  // Staff Name
      { wch: 20 },  // Designation
      { wch: 25 },  // Email Address
      { wch: 12 },  // Staff Rating
      { wch: 40 },  // Staff Comments
      { wch: 12 },  // H1. Rating
      { wch: 40 }   // H1. Comments
    ];

    // Add worksheet to workbook
    const sheetName = `${employee.first_name}_${employee.last_name}`.substring(0, 31);
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
  });

  const fileName = `${reportType.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};

// Department Performance Export
export const exportDepartmentPerformanceToExcel = async (data: any[], reportType: string = 'Department Performance') => {
  const workbook = XLSX.utils.book_new();

  const sheetData: any[][] = [];

  // Header
  sheetData.push(['OPTIVEN LIMITED - DEPARTMENT PERFORMANCE REPORT']);
  sheetData.push([`Generated on: ${new Date().toLocaleDateString()}`]);
  sheetData.push([]); // Empty row

  // Column headers
  sheetData.push([
    'Department Name',
    'Total Employees',
    'Completed Appraisals',
    'Pending Appraisals',
    'Completion Rate (%)',
    'Average Self Rating',
    'Average Supervisor Rating',
    'Top Performers',
    'Needs Improvement',
    'Department Head',
    'Department Assistant'
  ]);

  // Data rows
  data.forEach(dept => {
    sheetData.push([
      dept.department_name,
      dept.total_employees,
      dept.completed_appraisals,
      dept.pending_appraisals,
      dept.completion_rate,
      dept.average_self_rating,
      dept.average_supervisor_rating,
      dept.top_performers,
      dept.needs_improvement,
      dept.department_head || 'N/A',
      dept.department_assistant || 'N/A'
    ]);
  });

  // Summary
  sheetData.push([]); // Empty row
  sheetData.push(['SUMMARY']);
  sheetData.push(['Total Departments', data.length]);
  sheetData.push(['Total Employees', data.reduce((sum, dept) => sum + dept.total_employees, 0)]);
  sheetData.push(['Overall Completion Rate', Math.round(data.reduce((sum, dept) => sum + dept.completion_rate, 0) / data.length) + '%']);

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Set column widths
  worksheet['!cols'] = [
    { wch: 20 }, { wch: 15 }, { wch: 18 }, { wch: 16 }, { wch: 15 },
    { wch: 18 }, { wch: 20 }, { wch: 15 }, { wch: 18 }, { wch: 18 }, { wch: 18 }
  ];

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Department Performance');

  const fileName = `${reportType.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};

// Team Performance Export
export const exportTeamPerformanceToExcel = async (data: any[], reportType: string = 'Team Performance') => {
  const workbook = XLSX.utils.book_new();

  const sheetData: any[][] = [];

  // Header
  sheetData.push(['OPTIVEN LIMITED - TEAM PERFORMANCE REPORT']);
  sheetData.push([`Generated on: ${new Date().toLocaleDateString()}`]);
  sheetData.push([]); // Empty row

  // Column headers
  sheetData.push([
    'Team Name',
    'Department',
    'Total Employees',
    'Completed Appraisals',
    'Pending Appraisals',
    'Completion Rate (%)',
    'Average Self Rating',
    'Average Supervisor Rating',
    'Top Performers',
    'Needs Improvement',
    'Team Lead',
    'Team Assistant'
  ]);

  // Data rows
  data.forEach(team => {
    sheetData.push([
      team.team_name,
      team.department_name,
      team.total_employees,
      team.completed_appraisals,
      team.pending_appraisals,
      team.completion_rate,
      team.average_self_rating,
      team.average_supervisor_rating,
      team.top_performers,
      team.needs_improvement,
      team.team_lead || 'N/A',
      team.team_assistant || 'N/A'
    ]);
  });

  // Summary
  sheetData.push([]); // Empty row
  sheetData.push(['SUMMARY']);
  sheetData.push(['Total Teams', data.length]);
  sheetData.push(['Total Employees', data.reduce((sum, team) => sum + team.total_employees, 0)]);
  sheetData.push(['Overall Completion Rate', Math.round(data.reduce((sum, team) => sum + team.completion_rate, 0) / data.length) + '%']);

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Set column widths
  worksheet['!cols'] = [
    { wch: 20 }, { wch: 20 }, { wch: 15 }, { wch: 18 }, { wch: 16 }, { wch: 15 },
    { wch: 18 }, { wch: 20 }, { wch: 15 }, { wch: 18 }, { wch: 18 }, { wch: 18 }
  ];

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Team Performance');

  const fileName = `${reportType.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};

// Summary Report Export
export const exportSummaryReportToExcel = async (data: any, reportType: string = 'Summary Report') => {
  const workbook = XLSX.utils.book_new();

  // Overview Sheet
  const overviewData: any[][] = [];
  overviewData.push(['OPTIVEN LIMITED - PERFORMANCE SUMMARY REPORT']);
  overviewData.push([`Generated on: ${new Date().toLocaleDateString()}`]);
  overviewData.push([]); // Empty row

  // Key Metrics
  overviewData.push(['KEY METRICS']);
  overviewData.push(['Total Employees', data.total_employees]);
  overviewData.push(['Total Appraisals', data.total_appraisals]);
  overviewData.push(['Completion Rate (%)', data.completion_rate]);
  overviewData.push(['Average Organization Rating', data.average_organization_rating]);
  overviewData.push(['Departments Count', data.departments_count]);
  overviewData.push(['Teams Count', data.teams_count]);
  overviewData.push([]); // Empty row

  // Performance Distribution
  overviewData.push(['PERFORMANCE DISTRIBUTION']);
  overviewData.push(['Excellent (90%+)', data.performance_distribution.excellent]);
  overviewData.push(['Good (70-89%)', data.performance_distribution.good]);
  overviewData.push(['Average (50-69%)', data.performance_distribution.average]);
  overviewData.push(['Needs Improvement (<50%)', data.performance_distribution.needs_improvement]);

  const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData);
  XLSX.utils.book_append_sheet(workbook, overviewSheet, 'Overview');

  // Top Departments Sheet
  if (data.top_performing_departments && data.top_performing_departments.length > 0) {
    const deptData: any[][] = [];
    deptData.push(['TOP PERFORMING DEPARTMENTS']);
    deptData.push(['Department', 'Employee Count', 'Completion Rate (%)', 'Average Rating']);
    
    data.top_performing_departments.forEach((dept: any) => {
      deptData.push([
        dept.department_name,
        dept.employee_count,
        dept.completion_rate,
        dept.average_rating
      ]);
    });

    const deptSheet = XLSX.utils.aoa_to_sheet(deptData);
    XLSX.utils.book_append_sheet(workbook, deptSheet, 'Top Departments');
  }

  const fileName = `${reportType.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};

// Generic export handler
export const handleExport = async (
  data: any,
  reportType: 'individual' | 'department' | 'team' | 'summary',
  format: 'excel' | 'pdf' = 'excel'
) => {
  if (!data || (Array.isArray(data) && data.length === 0)) {
    toast({
      title: "No Data",
      description: "No data available to export.",
      variant: "destructive",
    });
    return;
  }

  try {
    if (format === 'excel') {
      switch (reportType) {
        case 'individual':
          await exportIndividualPerformanceToExcel(data, 'Individual Performance Report');
          break;
        case 'department':
          await exportDepartmentPerformanceToExcel(data, 'Department Performance Report');
          break;
        case 'team':
          await exportTeamPerformanceToExcel(data, 'Team Performance Report');
          break;
        case 'summary':
          await exportSummaryReportToExcel(data, 'Performance Summary Report');
          break;
      }

      toast({
        title: "Export Successful",
        description: `${reportType} report exported to Excel successfully.`,
      });
    }
  } catch (error) {
    console.error('Export error:', error);
    toast({
      title: "Export Failed",
      description: "Failed to export data. Please try again.",
      variant: "destructive",
    });
  }
};
