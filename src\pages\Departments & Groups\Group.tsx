import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { BASE_URL } from "../../config";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import toast from "react-hot-toast";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Building,
  Plus,
  Pencil,
  Trash2,
  RefreshCw,
  AlertCircle,
  Search,
  ArrowUpDown,
  Filter,
} from "lucide-react";

// Group interface based on API
interface Group {
  id: number;
  name: string;
  description: string;
  group_head: string;
  group_assistant: string;
  group_hr: string;
  group_status_active: boolean;
  organisation: number;
  parent_group: number | null;
}

// Employee interface (for group head, assistant, and HR selection)
interface Employee {
  employee_no: string;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  job_title?: string;
}

// Group form schema validation
const groupSchema = z.object({
  name: z.string().min(1, "Name is required").max(50, "Name must be less than 50 characters"),
  description: z.string().min(1, "Description is required"),
  group_head: z.string().min(1, "Group head is required").max(60, "Group head must be less than 60 characters"),
  group_assistant: z.string().max(60, "Group assistant must be less than 60 characters").optional().transform(val => val === "" ? undefined : val),
  group_hr: z.string().max(60, "Group HR must be less than 60 characters").optional().transform(val => val === "" ? undefined : val),
  group_status_active: z.boolean().default(true),
  organisation: z.number(),
  parent_group: z.number().nullable(),
});

export default function Groups() {
  const navigate = useNavigate();
  const { token } = useSelector((state: RootState) => state.auth);
  const [groups, setGroups] = useState<Group[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentGroup, setCurrentGroup] = useState<Group | null>(null);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState("");
  const [sortConfig, setSortConfig] = useState<{ key: keyof Group; direction: "ascending" | "descending" } | null>(null);

  // Default organisation ID (in a real app, this would come from user context)
  const defaultOrganisationId = 1;

  // Initialize react-hook-form
  const form = useForm<z.infer<typeof groupSchema>>({
    resolver: zodResolver(groupSchema),
    defaultValues: {
      name: "",
      description: "",
      group_head: "",
      group_assistant: "",
      group_hr: "",
      group_status_active: true,
      organisation: defaultOrganisationId,
      parent_group: null,
    },
  });

  // Reset form when dialog closes
  useEffect(() => {
    if (!isAddDialogOpen && !isEditDialogOpen) {
      form.reset();
      setFormError("");
    }
  }, [isAddDialogOpen, isEditDialogOpen, form]);

  // Fetch groups with useCallback for optimization
  const fetchGroups = useCallback(async () => {
    if (!token) return;

    try {
      setLoading(true);
      setError("");
      const response = await axios.get(`${BASE_URL}/users/organization_groups`, {
        headers: { Authorization: `Token ${token}` },
      });
      console.log("Fetched groups data:", response.data);
      setGroups(response.data);
    } catch (err: any) {
      console.error("Error fetching groups:", err);
      const errorMessage = err.response?.data?.message || "Failed to load groups. Please try again.";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token]);

  // Fetch employees (for group head dropdown) with useCallback
  const fetchEmployees = useCallback(async () => {
    if (!token) return;

    try {
      const response = await axios.get(`${BASE_URL}/users/all-employees`, {
        headers: { Authorization: `Token ${token}` },
      });
      setEmployees(response.data);
    } catch (err: any) {
      console.error("Error fetching employees:", err);
      toast.error("Failed to load employees for dropdowns");
    }
  }, [token]);

  useEffect(() => {
    if (token) {
      fetchGroups();
      fetchEmployees();
    }
  }, [token]);

  // Optimized filtering and sorting with useMemo
  const filteredGroups = useMemo(() => {
    let result = [...groups];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      result = result.filter(
        (grp) =>
          grp.name.toLowerCase().includes(searchLower) ||
          grp.description.toLowerCase().includes(searchLower) ||
          grp.group_head.toLowerCase().includes(searchLower) ||
          (grp.group_assistant && grp.group_assistant.toLowerCase().includes(searchLower)) ||
          (grp.group_hr && grp.group_hr.toLowerCase().includes(searchLower))
      );
    }

    // Apply status filter
    if (statusFilter) {
      result = statusFilter === "active"
        ? result.filter((grp) => grp.group_status_active)
        : result.filter((grp) => !grp.group_status_active);
    }

    // Apply sorting
    if (sortConfig !== null) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.key] ?? "";
        const bValue = b[sortConfig.key] ?? "";
        if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
        return 0;
      });
    }

    return result;
  }, [groups, searchTerm, statusFilter, sortConfig]);

  const requestSort = (key: keyof Group) => {
    let direction: "ascending" | "descending" = "ascending";
    if (sortConfig && sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  // Helper: display employee full name from employee_no
  const getEmployeeName = (employeeNo: string) => {
    const emp = employees.find((e) => e.employee_no === employeeNo);
    if (!emp) return employeeNo;

    const fullName = `${emp.first_name || ''} ${emp.last_name || ''}`.trim();
    return fullName || emp.username || emp.email || employeeNo;
  };

  // EmployeeSelect component for group head, assistant, and HR fields
  const EmployeeSelect: React.FC<{
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    allowEmpty?: boolean;
  }> = ({ value, onChange, placeholder = "Select an employee", allowEmpty = false }) => {
    const [search, setSearch] = useState("");

    // Helper function to get full name for display
    const getFullName = (emp: Employee) => {
      const fullName = `${emp.first_name || ''} ${emp.last_name || ''}`.trim();
      return fullName || emp.username || emp.email;
    };

    const filtered = employees.filter((emp) => {
      const fullName = getFullName(emp);
      const searchLower = search.toLowerCase();
      return fullName.toLowerCase().includes(searchLower) ||
             emp.username.toLowerCase().includes(searchLower) ||
             (emp.email && emp.email.toLowerCase().includes(searchLower)) ||
             (emp.job_title && emp.job_title.toLowerCase().includes(searchLower));
    });

    // Handle value change to convert "none" back to empty string
    const handleValueChange = (newValue: string) => {
      onChange(newValue === "none" ? "" : newValue);
    };

    // Convert empty string to "none" for the Select component
    const selectValue = value === "" ? "none" : value;

    return (
      <Select onValueChange={handleValueChange} value={selectValue}>
        <SelectTrigger>
          <SelectValue placeholder={placeholder}>
            {value && value !== "" ? getEmployeeName(value) : placeholder}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <div className="p-2">
            <Input placeholder="Search employees..." value={search} onChange={(e) => setSearch(e.target.value)} />
          </div>
          {allowEmpty && (
            <SelectItem value="none">None</SelectItem>
          )}
          {filtered.map((emp) => (
            <SelectItem key={emp.employee_no} value={emp.employee_no}>
              <div className="flex flex-col">
                <span className="font-medium">{getFullName(emp)}</span>
                {emp.job_title && (
                  <span className="text-xs text-gray-500">{emp.job_title}</span>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  };

  // GroupSelect component for parent group field
  const GroupSelect: React.FC<{
    value: number | null;
    onChange: (value: number | null) => void;
    placeholder?: string;
  }> = ({ value, onChange, placeholder = "Select a group" }) => {
    const [search, setSearch] = useState("");
    const options = groups.map((grp) => ({
      value: grp.id,
      label: grp.name,
    }));
    const filteredOptions = options.filter((opt) =>
      opt.label.toLowerCase().includes(search.toLowerCase())
    );
    return (
      <Select
        onValueChange={(val) => onChange(val === "none" ? null : parseInt(val))}
        value={value?.toString() || "none"}
      >
        <SelectTrigger>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <div className="p-2">
            <Input placeholder="Search..." value={search} onChange={(e) => setSearch(e.target.value)} />
          </div>
          <SelectItem value="none">None</SelectItem>
          {filteredOptions.map((opt) => (
            <SelectItem key={opt.value} value={opt.value.toString()}>
              {opt.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  };

  // Handle form submission for adding a group
  const onSubmitAdd = useCallback(async (data: z.infer<typeof groupSchema>) => {
    const toastId = toast.loading("Creating group...");

    try {
      setIsSubmitting(true);
      setFormError("");

      // Clean the data - convert empty strings to null for optional fields
      const cleanedData = {
        ...data,
        group_assistant: data.group_assistant === "" ? null : data.group_assistant,
        group_hr: data.group_hr === "" ? null : data.group_hr,
      };

      console.log("Sending create data:", cleanedData);

      const response = await axios.post(`${BASE_URL}/users/organization_groups`, cleanedData, {
        headers: {
          Authorization: `Token ${token}`,
          'Content-Type': 'application/json'
        },
      });

      console.log("Create response:", response.data);

      await fetchGroups();
      setIsAddDialogOpen(false);
      form.reset();

      toast.success("Group created successfully!", { id: toastId });
    } catch (err: any) {
      console.error("Error creating group:", err);
      console.error("Error response:", err.response?.data);
      const errorMessage = err.response?.data?.message || err.response?.data?.detail || "Failed to create group. Please try again.";
      setFormError(errorMessage);
      toast.error(errorMessage, { id: toastId });
    } finally {
      setIsSubmitting(false);
    }
  }, [token, fetchGroups, form]);

  // Handle form submission for editing a group
  const onSubmitEdit = useCallback(async (data: z.infer<typeof groupSchema>) => {
    if (!currentGroup) return;

    const toastId = toast.loading("Updating group...");

    try {
      setIsSubmitting(true);
      setFormError("");

      // Clean the data - convert empty strings to null for optional fields
      const cleanedData = {
        ...data,
        group_assistant: data.group_assistant === "" ? null : data.group_assistant,
        group_hr: data.group_hr === "" ? null : data.group_hr,
      };

      console.log("Sending update data:", cleanedData);

      const response = await axios.patch(`${BASE_URL}/users/organization_groups/${currentGroup.id}`, cleanedData, {
        headers: {
          Authorization: `Token ${token}`,
          'Content-Type': 'application/json'
        },
      });

      console.log("Update response:", response.data);

      await fetchGroups();
      setIsEditDialogOpen(false);

      toast.success("Group updated successfully!", { id: toastId });
    } catch (err: any) {
      console.error("Error updating group:", err);
      console.error("Error response:", err.response?.data);
      const errorMessage = err.response?.data?.message || err.response?.data?.detail || "Failed to update group. Please try again.";
      setFormError(errorMessage);
      toast.error(errorMessage, { id: toastId });
    } finally {
      setIsSubmitting(false);
    }
  }, [currentGroup, token, fetchGroups]);

  // Handle group deletion
  const handleDelete = useCallback(async () => {
    if (!deleteId) return;

    const toastId = toast.loading("Deleting group...");

    try {
      setIsSubmitting(true);
      await axios.delete(`${BASE_URL}/users/organization_groups/${deleteId}`, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchGroups();
      setDeleteId(null);

      toast.success("Group deleted successfully!", { id: toastId });
    } catch (err: any) {
      console.error("Error deleting group:", err);
      const errorMessage = err.response?.data?.message || err.response?.data?.detail || "Failed to delete group. Please try again.";
      toast.error(errorMessage, { id: toastId });
    } finally {
      setIsSubmitting(false);
    }
  }, [deleteId, token, fetchGroups]);

  // Edit group handler
  const handleEdit = (group: Group) => {
    setCurrentGroup(group);
    form.reset({
      name: group.name,
      description: group.description,
      group_head: group.group_head,
      group_assistant: group.group_assistant || "",
      group_hr: group.group_hr || "",
      group_status_active: group.group_status_active,
      organisation: group.organisation,
      parent_group: group.parent_group,
    });
    setIsEditDialogOpen(true);
  };

  // Clear filters with useCallback
  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setStatusFilter(null);
    setSortConfig(null);
    toast.success("Filters cleared");
  }, []);

  // Breadcrumb navigation
  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink href="/" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block" />
        <BreadcrumbItem>
          <BreadcrumbLink href="/dashboard" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Dashboard
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Groups</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  if (loading && groups.length === 0) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading groups...</p>
          </div>
        </div>
      </Screen>
    );
  }

  if (error && groups.length === 0) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="bg-red-100 dark:bg-red-900/30 p-6 rounded-lg max-w-md text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-red-700 dark:text-red-400 mb-2">Error Loading Groups</h2>
            <p className="text-gray-700 dark:text-gray-300">{error}</p>
            <button
              onClick={() => fetchGroups()}
              className="mt-4 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
            >
              <RefreshCw className="w-4 h-4 inline-block mr-2" />
              Retry
            </button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen headerContent={breadcrumb}>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-7xl">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white flex items-center">
              <Building className="w-6 h-6 sm:w-7 sm:h-7 mr-2 text-green-600 dark:text-green-400 flex-shrink-0" />
              <span className="truncate">Groups Management</span>
            </h1>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">
              Create, view, update, and manage your organization&apos;s groups
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700 w-full sm:w-auto">
                <Plus className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Add Group</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto mx-4">
              <DialogHeader>
                <DialogTitle>Add New Group</DialogTitle>
                <DialogDescription>
                  Create a new group in your organization. Fill out the group details below.
                </DialogDescription>
              </DialogHeader>
              {formError && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 mb-4">
                  <p className="text-red-600 dark:text-red-400 text-sm">{formError}</p>
                </div>
              )}
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmitAdd)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Group Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter group name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Enter group description" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="group_head"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Group Head</FormLabel>
                        <FormControl>
                          <EmployeeSelect
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Select Group Head"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="group_assistant"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Group Assistant (Optional)</FormLabel>
                        <FormControl>
                          <EmployeeSelect
                            value={field.value || ""}
                            onChange={field.onChange}
                            placeholder="Select Group Assistant"
                            allowEmpty={true}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="group_hr"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Group HR (Optional)</FormLabel>
                        <FormControl>
                          <EmployeeSelect
                            value={field.value || ""}
                            onChange={field.onChange}
                            placeholder="Select Group HR"
                            allowEmpty={true}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="group_status_active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Active Status</FormLabel>
                          <FormDescription>
                            Mark this group as active
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="parent_group"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Parent Group (Optional)</FormLabel>
                        <FormControl>
                          <GroupSelect
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Select parent group (if any)"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        "Create Group"
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col space-y-4 lg:flex-row lg:space-y-0 lg:gap-4 lg:items-center lg:justify-between">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search groups..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full"
                />
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <Select value={statusFilter || "all"} onValueChange={(value) => setStatusFilter(value === "all" ? null : value)}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <div className="flex items-center">
                      <Filter className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="Filter by status" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" onClick={clearFilters} className="w-full sm:w-auto">
                  <span className="hidden sm:inline">Clear Filters</span>
                  <span className="sm:hidden">Clear</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0">
              <span className="text-lg sm:text-xl">Groups ({filteredGroups.length})</span>
              {loading && (
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  <span className="hidden sm:inline">Refreshing data...</span>
                  <span className="sm:hidden">Loading...</span>
                </div>
              )}
            </CardTitle>
            <CardDescription className="text-sm">
              Overview of all groups in your organization
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0 sm:p-6">
            {filteredGroups.length === 0 ? (
              <div className="text-center py-8 px-4">
                <Building className="w-12 h-12 mx-auto text-gray-400 mb-3" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">No groups found</h3>
                <p className="text-sm sm:text-base text-gray-500 dark:text-gray-400 mt-1">
                  {searchTerm || statusFilter
                    ? "Try adjusting your search or filters"
                    : "Create your first group to get started"}
                </p>
                {(searchTerm || statusFilter) && (
                  <Button variant="outline" onClick={clearFilters} className="mt-4">
                    Clear Filters
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <div className="rounded-md border min-w-full">
                  <Table className="min-w-[800px]">
                  <TableHeader>
                    <TableRow>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 w-[200px]"
                        onClick={() => requestSort("name")}
                      >
                        <div className="flex items-center">
                          Name
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead className="hidden md:table-cell max-w-[250px]">Description</TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 w-[150px]"
                        onClick={() => requestSort("group_head")}
                      >
                        <div className="flex items-center">
                          <span className="hidden sm:inline">Group </span>Head
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 hidden lg:table-cell w-[150px]"
                        onClick={() => requestSort("group_assistant")}
                      >
                        <div className="flex items-center">
                          Assistant
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 hidden xl:table-cell w-[120px]"
                        onClick={() => requestSort("group_hr")}
                      >
                        <div className="flex items-center">
                          HR
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 w-[100px]"
                        onClick={() => requestSort("group_status_active")}
                      >
                        <div className="flex items-center">
                          Status
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead className="text-right w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredGroups.map((group) => (
                      <TableRow key={group.id}>
                        <TableCell className="font-medium">
                          <div className="max-w-[180px] truncate">{group.name}</div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="max-w-[230px] truncate text-sm">{group.description}</div>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-[130px] truncate text-sm">{getEmployeeName(group.group_head)}</div>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <div className="max-w-[130px] truncate text-sm">
                            {group.group_assistant ? getEmployeeName(group.group_assistant) : "-"}
                          </div>
                        </TableCell>
                        <TableCell className="hidden xl:table-cell">
                          <div className="max-w-[100px] truncate text-sm">
                            {group.group_hr ? getEmployeeName(group.group_hr) : "-"}
                          </div>
                        </TableCell>
                        <TableCell>
                          {group.group_status_active ? (
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 text-xs">
                              <span className="hidden sm:inline">Active</span>
                              <span className="sm:hidden">✓</span>
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 text-xs">
                              <span className="hidden sm:inline">Inactive</span>
                              <span className="sm:hidden">✗</span>
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-1 sm:space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(group)}
                              className="h-8 w-8 p-0 sm:h-9 sm:w-9"
                            >
                              <Pencil className="h-3 w-3 sm:h-4 sm:w-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setDeleteId(group.id)}
                                  className="h-8 w-8 p-0 sm:h-9 sm:w-9"
                                >
                                  <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action will permanently delete the group "{group.name}" and cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel onClick={() => setDeleteId(null)}>
                                    Cancel
                                  </AlertDialogCancel>
                                  <AlertDialogAction onClick={handleDelete} disabled={isSubmitting}>
                                    {isSubmitting ? (
                                      <>
                                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Dialog - Moved outside the table loop */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle>Edit Group</DialogTitle>
              <DialogDescription>
                Update information for {currentGroup?.name}
              </DialogDescription>
            </DialogHeader>
            {formError && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 mb-4">
                <p className="text-red-600 dark:text-red-400 text-sm">{formError}</p>
              </div>
            )}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmitEdit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Group Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter group name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Enter group description" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="group_head"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Group Head</FormLabel>
                      <FormControl>
                        <EmployeeSelect
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select Group Head"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="group_assistant"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Group Assistant (Optional)</FormLabel>
                      <FormControl>
                        <EmployeeSelect
                          value={field.value || ""}
                          onChange={field.onChange}
                          placeholder="Select Group Assistant"
                          allowEmpty={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="group_hr"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Group HR (Optional)</FormLabel>
                      <FormControl>
                        <EmployeeSelect
                          value={field.value || ""}
                          onChange={field.onChange}
                          placeholder="Select Group HR"
                          allowEmpty={true}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="group_status_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active Status</FormLabel>
                        <FormDescription>
                          Mark this group as active
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="parent_group"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Parent Group (Optional)</FormLabel>
                      <FormControl>
                        <GroupSelect
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Select parent group (if any)"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      "Update Group"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </Screen>
  );
}
