"use client";

import React, { useState, useEffect } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ArrowLeft, Check, X, Eye, EyeOff } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { resetPassword, resetPasswordStatus } from "@/redux/features/auth/authSlice";
import { RootState, AppDispatch } from "@/redux/store";

const ResetPassword: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const dispatch = useDispatch<AppDispatch>();
  
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState(true);
  
  // Get loading and success states from Redux
  const { loading, success, error } = useSelector(
    (state: RootState) => state.auth.resetStatus
  );
  
  // Password strength criteria
  const [passwordStrength, setPasswordStrength] = useState({
    hasMinLength: false,
    hasUpperCase: false,
    hasLowerCase: false,
    hasNumber: false,
    hasSpecialChar: false
  });

  // Check token validity on mount
  useEffect(() => {
    const checkToken = async () => {
      try {
        // For now, just check if token exists
        if (!token) {
          setIsTokenValid(false);
          toast({
            title: "Invalid Reset Link",
            description: "The password reset link is invalid or has expired.",
            variant: "destructive",
          });
        }
      } catch (error) {
        setIsTokenValid(false);
        toast({
          title: "Invalid Reset Link",
          description: "The password reset link is invalid or has expired.",
          variant: "destructive",
        });
      }
    };

    checkToken();
  }, [token, toast]);

  // Check password strength
  useEffect(() => {
    setPasswordStrength({
      hasMinLength: newPassword.length >= 8,
      hasUpperCase: /[A-Z]/.test(newPassword),
      hasLowerCase: /[a-z]/.test(newPassword),
      hasNumber: /[0-9]/.test(newPassword),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(newPassword)
    });
  }, [newPassword]);

  // Reset the password status when component unmounts
  useEffect(() => {
    return () => {
      dispatch(resetPasswordStatus());
    };
  }, [dispatch]);

  // Redirect to login after successful password reset
  useEffect(() => {
    if (success) {
      setTimeout(() => {
        navigate("/login");
      }, 3000);
    }
  }, [success, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newPassword || !confirmPassword) {
      toast({
        title: "Missing Fields",
        description: "Please fill in both password fields.",
        variant: "destructive",
      });
      return;
    }
    
    if (newPassword !== confirmPassword) {
      toast({
        title: "Password Mismatch",
        description: "New password and confirmation do not match.",
        variant: "destructive",
      });
      return;
    }
    
    // Check if password meets all criteria
    const isPasswordStrong = Object.values(passwordStrength).every(value => value === true);
    if (!isPasswordStrong) {
      toast({
        title: "Weak Password",
        description: "Please ensure your password meets all the requirements.",
        variant: "destructive",
      });
      return;
    }
    
    try {
      // Use Redux thunk to reset password
      const resultAction = await dispatch(resetPassword({
        token: token || "",
        newPassword: newPassword
      }));
      
      if (resetPassword.fulfilled.match(resultAction)) {
        toast({
          title: "Success",
          description: "Your password has been reset successfully.",
          variant: "default",
        });
      } else if (resetPassword.rejected.match(resultAction)) {
        const errorMessage = resultAction.payload as string || "Failed to reset password";
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reset password",
        variant: "destructive",
      });
    }
  };

  // UI helper for password criteria
  const CriteriaItem = ({ met, text }: { met: boolean; text: string }) => (
    <div className="flex items-center space-x-2">
      {met ? (
        <Check className="h-4 w-4 text-green-500" />
      ) : (
        <X className="h-4 w-4 text-red-500" />
      )}
      <span className={met ? "text-green-500" : "text-gray-500"}>{text}</span>
    </div>
  );

  if (!isTokenValid) {
    return (
      <div className="flex w-full min-h-screen">
        <div className="w-full flex flex-col justify-center items-center p-4 bg-gray-50 dark:bg-gray-900">
          <div className="w-full max-w-md px-8 py-10 bg-white dark:bg-gray-800 rounded-xl shadow-lg text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 text-red-600 mb-4">
              <X className="h-8 w-8" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Invalid or Expired Link
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              The password reset link is invalid or has expired. Please request a new password reset link.
            </p>
            <Button
              onClick={() => navigate("/forgot-password")}
              className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 text-white font-medium rounded-md transition-colors"
            >
              Request New Link
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex w-full min-h-screen">
      {/* Left side */}
      <div 
        className="hidden lg:flex lg:w-1/2 justify-center items-center relative"
        style={{
          backgroundImage: "url(https://lh3.googleusercontent.com/p/AF1QipN0diuh8haESrAhd2TclRU5rQ3Qp2OlzstOU2BR=s680-w680-h510)",
          backgroundSize: "cover",
          backgroundPosition: "center"
        }}
      >
      </div>

      {/* Form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-4 bg-gray-50 dark:bg-gray-900">
        <div className="w-full max-w-md px-8 py-10 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
          <div className="flex justify-center mb-6">
            <img 
              src="https://workspace.optiven.co.ke/static/media/optiven-logo-full.f498da6255572ff1ab8a.png" 
              alt="Optiven Limited Logo" 
              className="h-16" 
            />
          </div>
          
          {!success ? (
            <>
              <h1 className="text-2xl font-bold text-center text-gray-900 dark:text-white mb-4">
                Reset Password
              </h1>
              <p className="text-center text-gray-600 dark:text-gray-400 mb-8">
                Please enter your new password below.
              </p>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="newPassword" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    New Password
                  </Label>
                  <div className="relative">
                    <Input
                      id="newPassword"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter new password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10"
                      disabled={loading}
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 flex items-center pr-3"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Password strength criteria */}
                <div className="space-y-1 text-xs">
                  <CriteriaItem met={passwordStrength.hasMinLength} text="At least 8 characters" />
                  <CriteriaItem met={passwordStrength.hasUpperCase} text="Contains uppercase letter" />
                  <CriteriaItem met={passwordStrength.hasLowerCase} text="Contains lowercase letter" />
                  <CriteriaItem met={passwordStrength.hasNumber} text="Contains number" />
                  <CriteriaItem met={passwordStrength.hasSpecialChar} text="Contains special character" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Confirm Password
                  </Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Confirm new password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className={`w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10 ${
                        confirmPassword && newPassword !== confirmPassword
                          ? "border-red-500 dark:border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      }`}
                      disabled={loading}
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 flex items-center pr-3"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  {confirmPassword && newPassword !== confirmPassword && (
                    <p className="text-xs text-red-500">Passwords do not match</p>
                  )}
                </div>

                <Button
                  type="submit"
                  variant="default"
                  disabled={loading}
                  className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 text-white font-medium rounded-md transition-colors"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Resetting...
                    </>
                  ) : (
                    "Reset Password"
                  )}
                </Button>
              </form>
            </>
          ) : (
            <div className="text-center space-y-4">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 text-green-600 mb-4">
                <Check className="h-8 w-8" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Password Reset Successful
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Your password has been reset successfully. You will be redirected to the login page shortly.
              </p>
            </div>
          )}

          <div className="mt-8">
            <Link
              to="/login"
              className="flex items-center justify-center text-sm font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Login
            </Link>
          </div>

          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-500">
              © {new Date().getFullYear()} Optiven Limited. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;