"use client";

import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectItem,
  SelectContent,
} from "@/components/ui/select";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";

import CandidateDetailsDialog from "../dialogs/CandidateDetailsDialog";
import RateCandidateDialog from "../dialogs/RateCandidateDialog";

import {
  JobVacancy,
  InterviewLevel,
  InterviewResult,
  InterviewQuestion,
  VacancyApplication,
} from "../types";

import { BASE_URL } from "@/config";

const InterviewsTab: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const [vacancies, setVacancies] = useState<JobVacancy[]>([]);
  const [allLevels, setAllLevels] = useState<InterviewLevel[]>([]);
  const [allResults, setAllResults] = useState<InterviewResult[]>([]);
  const [allQuestions, setAllQuestions] = useState<InterviewQuestion[]>([]);
  const [applications, setApplications] = useState<VacancyApplication[]>([]);
  const [candidateNameMap, setCandidateNameMap] = useState<Record<string, string>>({});
  const [selectedVacancyId, setSelectedVacancyId] = useState<number | null>(null);
  const [filteredLevels, setFilteredLevels] = useState<InterviewLevel[]>([]);
  const [selectedLevelId, setSelectedLevelId] = useState<number | null>(null);
  const [filteredQuestions, setFilteredQuestions] = useState<InterviewQuestion[]>([]);
  const [candidatesAtLevel, setCandidatesAtLevel] = useState<VacancyApplication[]>([]);
  const [candidateScores, setCandidateScores] = useState<{
    [vacAppId: number]: { [questionId: number]: number };
  }>({});
  const [viewCandidateOpen, setViewCandidateOpen] = useState(false);
  const [candidateToView, setCandidateToView] = useState<VacancyApplication | null>(null);
  const [rateCandidateOpen, setRateCandidateOpen] = useState(false);
  const [candidateToRate, setCandidateToRate] = useState<VacancyApplication | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Helper function to fetch all vacancy applications (handles paginated responses)
  const fetchAllVacancyApplications = async (url: string): Promise<VacancyApplication[]> => {
    let apps: VacancyApplication[] = [];
    let nextUrl: string | null = url;
    let page = 1;
    while (nextUrl) {
      console.log(`Fetching vacancy applications page ${page}: ${nextUrl}`);
      const res = await fetch(nextUrl);
      if (!res.ok) {
        console.error(`Failed to fetch applications from ${nextUrl}`);
        throw new Error("Failed to fetch applications");
      }
      const data = await res.json();
      if (data.results) {
        console.log(`Page ${page} returned ${data.results.length} applications`);
        apps = apps.concat(data.results);
        nextUrl = data.next;
        if (nextUrl) {
          console.log(`Next page URL: ${nextUrl}`);
        } else {
          console.log("No more pages to fetch.");
        }
      } else {
        console.log(`Fetched ${data.length} applications (non-paginated response)`);
        apps = data;
        nextUrl = null;
      }
      console.log(`Total applications fetched so far: ${apps.length}`);
      page++;
    }
    return apps;
  };

  useEffect(() => {
    const fetchAll = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log("Starting fetch of vacancies, levels, results, and biodata.");
        const [vacRes, lvlRes, resRes, bioRes] = await Promise.all([
          fetch(`${BASE_URL}/hrm/vacancies`),
          fetch(`${BASE_URL}/recruitment/interview-levels`),
          fetch(`${BASE_URL}/recruitment/interview-results`),
          fetch(`${BASE_URL}/recruitment/external-user-bio-data`),
        ]);
        if (!vacRes.ok) throw new Error("Failed to fetch vacancies");
        if (!lvlRes.ok) throw new Error("Failed to fetch levels");
        if (!resRes.ok) throw new Error("Failed to fetch results");
        if (!bioRes.ok) throw new Error("Failed to fetch biodata");

        const vacData: JobVacancy[] = await vacRes.json();
        const lvlData: InterviewLevel[] = await lvlRes.json();
        const resultsData: InterviewResult[] = await resRes.json();
        const biosData = await bioRes.json();

        console.log("Fetching vacancy applications with potential pagination.");
        const appsData: VacancyApplication[] = await fetchAllVacancyApplications(
          `${BASE_URL}/recruitment/vacancy-application`
        );
        console.log(`Total vacancy applications fetched: ${appsData.length}`);

        let questionData: InterviewQuestion[] = [];
        try {
          console.log(`Fetching interview questions for employee_no=${user?.employee_no}`);
          const qRes = await fetch(
            `${BASE_URL}/recruitment/interview-questions?employee_no=${user?.employee_no}`
          );
          if (qRes.ok) {
            questionData = await qRes.json();
            console.log(`Fetched ${questionData.length} interview questions.`);
          } else {
            console.warn("Failed to fetch interview questions, proceeding with empty array.");
          }
        } catch (e) {
          console.error("Error fetching interview questions", e);
          questionData = [];
        }

        setVacancies(vacData);
        setAllLevels(lvlData);
        setAllResults(resultsData);
        setAllQuestions(questionData);
        setApplications(appsData);

        const nameMap: Record<string, string> = {};
        biosData.forEach((b: any) => {
          const fullName = `${b.first_name} ${b.middle_name ?? ""} ${b.last_name}`.trim();
          nameMap[b.external_user_no] = fullName;
        });
        setCandidateNameMap(nameMap);
      } catch (err: any) {
        console.error("Error in fetchAll:", err);
        setError(err.message);
      } finally {
        setLoading(false);
        console.log("Fetch process completed.");
      }
    };
    if (user?.employee_no) {
      fetchAll();
    }
  }, [user?.employee_no]);

  useEffect(() => {
    if (!selectedVacancyId) {
      setFilteredLevels([]);
      setSelectedLevelId(null);
      return;
    }
    const lvls = allLevels.filter((lvl) => lvl.vacancy === selectedVacancyId);
    console.log(`Filtered ${lvls.length} levels for vacancy ${selectedVacancyId}`);
    setFilteredLevels(lvls);
    setSelectedLevelId(null);
  }, [selectedVacancyId, allLevels]);

  useEffect(() => {
    if (!selectedVacancyId || !selectedLevelId) {
      setFilteredQuestions([]);
      setCandidatesAtLevel([]);
      return;
    }
    // Filter questions for the selected level
    const qLevel = allQuestions.filter((q) => q.interview_level === selectedLevelId);
    setFilteredQuestions(qLevel);

    // Get all interview results for the selected level
    const resultsForLevel = allResults.filter((r) => r.interview_level === selectedLevelId);
    // Filter results to only those for applications for the selected vacancy
    const relevantResults = resultsForLevel.filter((r) => {
      const app = applications.find((a) => a.id === r.vacancy_application);
      return app && app.vacancy === selectedVacancyId;
    });
    console.log(`Found ${relevantResults.length} interview results for level ${selectedLevelId}`);

    // Create a set of application IDs from interview results
    const vacAppIdsFromResults = new Set(relevantResults.map((r) => r.vacancy_application));

    // Start with candidates that already have interview results
    let cands = applications.filter(
      (a) => a.vacancy === selectedVacancyId && vacAppIdsFromResults.has(a.id)
    );

    // Determine the first interview level for this vacancy
    const levelsForVacancy = allLevels.filter((lvl) => lvl.vacancy === selectedVacancyId);
    levelsForVacancy.sort((a, b) => a.id - b.id);
    const firstLevelId = levelsForVacancy.length ? levelsForVacancy[0].id : null;
    console.log(`First level for vacancy ${selectedVacancyId} is ${firstLevelId}`);

    // If the selected level is the first level, add shortlisted candidates
    if (selectedLevelId === firstLevelId) {
      const shortlistedCandidates = applications.filter(
        (a) =>
          a.vacancy === selectedVacancyId &&
          a.application_status === "shortlisted" &&
          !vacAppIdsFromResults.has(a.id)
      );
      console.log(
        `Adding ${shortlistedCandidates.length} shortlisted candidate(s) for first level ${firstLevelId}`
      );
      cands = cands.concat(shortlistedCandidates);
      // Remove any duplicates by candidate id
      cands = Array.from(new Map(cands.map((c) => [c.id, c])).values());
    }
    console.log(
      `Found ${cands.length} candidates for vacancy ${selectedVacancyId} and level ${selectedLevelId}`
    );
    setCandidatesAtLevel(cands);

    // Build candidate scores from interview results
    const newScores: {
      [vacAppId: number]: { [questionId: number]: number };
    } = {};
    for (const res of relevantResults) {
      if (!res.interview_Questions) continue;
      const qScore = res.question_score || 0;
      const existing = newScores[res.vacancy_application] || {};
      newScores[res.vacancy_application] = {
        ...existing,
        [res.interview_Questions]: qScore,
      };
    }
    setCandidateScores(newScores);
  }, [selectedVacancyId, selectedLevelId, allQuestions, allResults, applications, allLevels]);

  const handleViewCandidate = (cand: VacancyApplication) => {
    console.log("Viewing candidate:", cand);
    setCandidateToView(cand);
    setViewCandidateOpen(true);
  };

  const handleOpenRateDialog = () => {
    if (!candidateToView) return;
    console.log("Opening rate dialog for candidate:", candidateToView);
    setCandidateToRate(candidateToView);
    setRateCandidateOpen(true);
  };

  const handleSaveCandidateRatings = async (updatedScores: { [questionId: number]: number }) => {
    if (!candidateToRate || !selectedLevelId) return;
    try {
      setLoading(true);
      setError(null);
      const vacAppId = candidateToRate.id;
      console.log("Saving ratings for candidate application:", vacAppId, updatedScores);
      for (const [questionIdStr, score] of Object.entries(updatedScores)) {
        const questionId = Number(questionIdStr);
        const existingRecord = allResults.find(
          (r) =>
            r.vacancy_application === vacAppId &&
            r.interview_level === selectedLevelId &&
            r.interview_Questions === questionId
        );
        if (existingRecord) {
          console.log(`Updating existing record ${existingRecord.id} with score ${score}`);
          await fetch(`${BASE_URL}/recruitment/interview-results/${existingRecord.id}`, {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ question_score: score }),
          });
        } else {
          console.log(`Creating new record for question ${questionId} with score ${score}`);
          await fetch(`${BASE_URL}/recruitment/interview-results`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              question_score: score,
              external_user_no: candidateToRate.external_user_no,
              vacancy_application: vacAppId,
              interview_level: selectedLevelId,
              interview_Questions: questionId,
              panelist_employee_no: user?.employee_no, 
            }),
          });
        }
      }
      const totalScore = Object.values(updatedScores).reduce((a, b) => a + b, 0);
      const existingSummary = allResults.find(
        (r) =>
          r.vacancy_application === vacAppId &&
          r.interview_level === selectedLevelId &&
          r.interview_Questions === null
      );
      if (existingSummary) {
        console.log(`Updating summary record ${existingSummary.id} with total score ${totalScore}`);
        await fetch(`${BASE_URL}/recruitment/interview-results/${existingSummary.id}`, {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ total_level_score: totalScore }),
        });
      } else {
        console.log(`Creating summary record with total score ${totalScore}`);
        await fetch(`${BASE_URL}/recruitment/interview-results`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            question_score: null,
            total_level_score: totalScore,
            external_user_no: candidateToRate.external_user_no,
            vacancy_application: vacAppId,
            interview_level: selectedLevelId,
            interview_Questions: null,
            panelist_employee_no: user?.employee_no, 
          }),
        });
      }
      setCandidateScores((prev) => ({
        ...prev,
        [vacAppId]: updatedScores,
      }));
      const rRes = await fetch(`${BASE_URL}/recruitment/interview-results`);
      if (rRes.ok) {
        const newAllResults: InterviewResult[] = await rRes.json();
        setAllResults(newAllResults);
      }
      setRateCandidateOpen(false);
      console.log("Candidate ratings saved successfully.");
    } catch (err: any) {
      console.error("Error saving candidate ratings:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Manage Interviews</h2>
      {error && <p className="text-red-500">{error}</p>}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex-1">
          <Label>Select Vacancy</Label>
          <Select
            onValueChange={(val) => setSelectedVacancyId(Number(val))}
            value={selectedVacancyId ? selectedVacancyId.toString() : ""}
          >
            <SelectTrigger className="w-full mt-1">
              <SelectValue placeholder="Choose a Vacancy" />
            </SelectTrigger>
            <SelectContent>
              {vacancies.map((v) => (
                <SelectItem key={v.id} value={v.id.toString()}>
                  {v.job_details.job_title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex-1">
          <Label>Select Interview Level</Label>
          <Select
            onValueChange={(val) => setSelectedLevelId(Number(val))}
            value={selectedLevelId ? selectedLevelId.toString() : ""}
          >
            <SelectTrigger className="w-full mt-1">
              <SelectValue placeholder="Choose a Level" />
            </SelectTrigger>
            <SelectContent>
              {filteredLevels.map((lvl) => (
                <SelectItem key={lvl.id} value={lvl.id.toString()}>
                  {lvl.interview_level}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="border p-4 rounded mt-4">
        <h3 className="font-semibold mb-2">Candidates in This Level</h3>
        {loading ? (
          <p>Loading...</p>
        ) : candidatesAtLevel.length === 0 ? (
          <p>No candidates found for this level.</p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Candidate Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {candidatesAtLevel.map((cand) => {
                const name = candidateNameMap[cand.external_user_no] || cand.external_user_no;
                return (
                  <TableRow key={cand.id}>
                    <TableCell>{name}</TableCell>
                    <TableCell>{cand.application_status}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" onClick={() => handleViewCandidate(cand)}>
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        )}
      </div>
      <CandidateDetailsDialog
        open={viewCandidateOpen}
        application={candidateToView}
        onClose={() => setViewCandidateOpen(false)}
        onRate={handleOpenRateDialog}
      />
      <RateCandidateDialog
        open={rateCandidateOpen}
        onClose={() => setRateCandidateOpen(false)}
        candidateApp={candidateToRate}
        questions={filteredQuestions}
        existingScores={candidateToRate ? candidateScores[candidateToRate.id] || {} : {}}
        onSave={handleSaveCandidateRatings}
      />
    </div>
  );
};

export default InterviewsTab;
