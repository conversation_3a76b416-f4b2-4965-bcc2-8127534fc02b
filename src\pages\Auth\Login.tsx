"use client";

import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { loginUser, sendPasswordResetLink } from "@/redux/features/auth/authSlice";
import toast, { Toaster } from "react-hot-toast";
import { Loader2 } from "lucide-react";
import backgroundVideo from "../../assets/optiven-background.mp4"
const Login: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const videoRef = useRef<HTMLVideoElement>(null);

  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [processingInitialLogin, setProcessingInitialLogin] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);

  const { user, token, error, resetStatus } = useSelector((state: RootState) => state.auth);

  // Successful login
  useEffect(() => {
    if (user && token) {
      setIsLoading(false);
      toast.success("Login successful! Redirecting…");
      setTimeout(() => navigate("/"), 1500);
    }
  }, [user, token, navigate]);

  // API error
  useEffect(() => {
    if (submitted && error && !processingInitialLogin) {
      setIsLoading(false);
      toast.error(error);
    }
  }, [error, submitted, processingInitialLogin]);

  // Handle when password reset link is sent successfully
  useEffect(() => {
    if (processingInitialLogin && resetStatus?.success) {
      setProcessingInitialLogin(false);
      setIsLoading(false);
    }
  }, [resetStatus?.success, processingInitialLogin, navigate, username]);

  // Improved video loop handling
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;
    
    const handleVideoEnded = () => {
      requestAnimationFrame(() => {
        video.currentTime = 0.01;
        video.play().catch(err => console.error("Video play error:", err));
      });
    };

    video.preload = "auto";
    video.load();
    
    video.addEventListener('ended', handleVideoEnded);
    
    // Cleanup event listener
    return () => {
      video.removeEventListener('ended', handleVideoEnded);
    };
  }, [videoLoaded]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);

    if (!username.trim() || !password) {
      toast.error("Please fill in both Email/ID and Password.");
      return;
    }

    if (username.includes("@")) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(username)) {
        toast.error("Please enter a valid email address.");
        return;
      }
    }

    if (password.length < 8) {
      toast.error("Password must be at least 8 characters.");
      return;
    }

    setIsLoading(true);
    
    // If using default password, redirect directly to the force-password-reset page
    if (password === "12345678") {
      setIsLoading(false);
      // Redirect directly to the staff.optiven.co.ke force-password-reset URL
      window.location.href = "https://staff.optiven.co.ke/force-password-reset";
    } else {
      // Normal login flow
      dispatch(loginUser({ username: username.trim(), password }));
    }
  };

  return (
    <div className="relative flex w-full min-h-screen flex-col items-center justify-center overflow-hidden">
      {/* Single Toaster, top-center */}
      <Toaster position="top-center" reverseOrder={false} />

      {/* Video Background with overlay */}
      <div className="absolute inset-0 w-full h-full">
        <div className={`absolute inset-0 bg-black/30 z-10 ${videoLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-700`}></div>
        <video 
          ref={videoRef}
          autoPlay 
          muted 
          playsInline
          onLoadedData={() => setVideoLoaded(true)}
          className={`object-cover w-full h-full ${videoLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-700`}
        >
          <source src={backgroundVideo} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>

      {/* Fallback background color before video loads */}
      <div className={`absolute inset-0 bg-gradient-to-br from-green-900 to-green-700 ${videoLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity duration-700`}></div>

      {/* Login Form Card */}
      <div className="relative z-20 w-full max-w-md px-8 py-10 rounded-xl border border-gray-200/30 backdrop-blur-md bg-white/90 shadow-xl transition-all duration-300 hover:shadow-2xl">
        <div className="flex justify-center mb-6">
          <img
            src="https://workspace.optiven.co.ke/static/media/optiven-logo-full.f498da6255572ff1ab8a.png"
            alt="Optiven Limited Logo"
            className="h-16 drop-shadow-md"
          />
        </div>

        <h1 className="text-2xl font-bold text-center text-gray-900 mb-8">
          ERP Login
        </h1>

        <form onSubmit={handleLogin} className="space-y-6">
          <div className="space-y-2">
            <Label
              htmlFor="username"
              className="text-sm font-medium text-gray-800"
            >
              Email / Employee ID
            </Label>
            <Input
              id="username"
              type="text"
              placeholder="Enter your email or ID"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              disabled={isLoading || processingInitialLogin}
              className="w-full px-4 py-2 bg-white/80 border-gray-300 text-gray-800 focus:ring-green-500 focus:border-green-500 rounded-md"
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label
                htmlFor="password"
                className="text-sm font-medium text-gray-800"
              >
                Password
              </Label>
              <Link
                to="/forgot-password"
                className="text-xs font-medium text-green-600 hover:text-green-800 hover:underline"
              >
                Forgot Password?
              </Link>
            </div>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isLoading || processingInitialLogin}
              className="w-full px-4 py-2 bg-white/80 border-gray-300 text-gray-800 focus:ring-green-500 focus:border-green-500 rounded-md"
            />
          </div>

          <Button
            type="submit"
            variant="default"
            disabled={isLoading || processingInitialLogin}
            className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 text-white font-medium rounded-md transition-all duration-300 shadow-md hover:shadow-lg"
          >
            {isLoading || processingInitialLogin ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {processingInitialLogin ? "Processing..." : "Signing in..."}
              </>
            ) : (
              "Sign In"
            )}
          </Button>
        </form>

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            Need help? Contact IT Support
          </p>
          <p className="text-xs text-gray-500 mt-4">
            © {new Date().getFullYear()} Optiven Limited. All rights reserved.
          </p>
        </div>
      </div>

      {/* Branding overlay */}
      <div className="absolute bottom-4 right-4 z-20">
        <div className="text-white text-sm backdrop-blur-sm bg-black/20 px-3 py-1 rounded-full">
          Inspiring Possibilities
        </div>
      </div>
    </div>
  );
};

export default Login;