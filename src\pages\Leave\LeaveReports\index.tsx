import React, { useEffect, useState, ChangeEvent } from "react";
import axios from "axios";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { BASE_URL } from "../../../config";

// Shadcn UI components
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Screen } from "@/app-components/layout/screen";

// ----------- Types -----------
interface LeaveApplication {
  id: number;
  no_of_days_applied: number;
  start_date: string;
  end_date: string;
  return_date: string;
  leave_status: string;
  leave_type_id: number;
  leave_type_name: string;
  employee_no: string;
  // (Assuming leave applications do not include a department field.)
}

interface LeaveReportRow {
  department: string;
  total_leaves: number;
  most_frequent_type: string;
}

interface Employee {
  employee_no: string;
  first_name: string;
  last_name: string;
  department_id: number;
  // ... other fields as needed
}

interface Department {
  id: number;
  name: string; // Using "name" from department details (may come as title or name)
  // ... other fields as needed
}

// Helper function: Given a date, returns its ISO week number and year.
function getISOWeek(date: Date): { year: number; week: number } {
  const tmpDate = new Date(date.getTime());
  // Set to nearest Thursday: current date + 4 - current day number
  // Make Sunday's day number 7
  const dayNumber = (date.getDay() + 6) % 7;
  tmpDate.setDate(date.getDate() - dayNumber + 3);
  const firstThursday = new Date(tmpDate.getFullYear(), 0, 4);
  const diff = tmpDate.getTime() - firstThursday.getTime();
  const week = 1 + Math.round(diff / (7 * 24 * 3600 * 1000));
  return { year: tmpDate.getFullYear(), week };
}

// Helper function: Returns true if the given date string matches the filter.
function matchesFilter(
  dateStr: string,
  filterType: string,
  filterValue: string
): boolean {
  const date = new Date(dateStr);
  if (filterType === "day") {
    // Expect filterValue format "YYYY-MM-DD"
    return date.toISOString().split("T")[0] === filterValue;
  } else if (filterType === "week") {
    // Expect filterValue format "YYYY-Www", e.g., "2023-W05"
    const { year, week } = getISOWeek(date);
    const weekStr = week.toString().padStart(2, "0");
    return `${year}-W${weekStr}` === filterValue;
  } else if (filterType === "month") {
    // Expect filterValue format "YYYY-MM"
    const parts = date.toISOString().split("T")[0].split("-");
    return `${parts[0]}-${parts[1]}` === filterValue;
  } else if (filterType === "year") {
    // Expect filterValue format "YYYY"
    return date.getFullYear().toString() === filterValue;
  }
  return true;
}

export default function LeaveReports() {
  const { token } = useSelector((state: RootState) => state.auth);

  // Aggregated report rows
  const [data, setData] = useState<LeaveReportRow[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [page, setPage] = useState(0);
  const rowsPerPage = 10;

  // Filter state
  // filterType: one of "day", "week", "month", "year"
  const [filterType, setFilterType] = useState("year");
  // filterValue: for "year" we'll use a year string (e.g., "2023")
  // For "day", "week", "month" these should be in the corresponding input format.
  const currentYear = new Date().getFullYear().toString();
  const [filterValue, setFilterValue] = useState(currentYear);

  // Handler for filter type change
  const handleFilterTypeChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const type = e.target.value;
    setFilterType(type);
    // Reset filterValue based on type
    if (type === "year") {
      setFilterValue(currentYear);
    } else {
      setFilterValue("");
    }
  };

  // Handler for filter value change
  const handleFilterValueChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFilterValue(e.target.value);
  };

  // Fetch and aggregate data (refetch when token, filterType, or filterValue change)
  useEffect(() => {
    if (!token) return;
    async function fetchData() {
      setLoading(true);
      setError("");
      try {
        // Fetch leave applications, employee details, and departments concurrently
        const [leavesRes, employeesRes, departmentsRes] = await Promise.all([
          axios.get<LeaveApplication[]>(`${BASE_URL}/hrm/leave-application-details`, {
            headers: { Authorization: `Token ${token}` },
          }),
          axios.get<Employee[]>(`${BASE_URL}/users/all-employees`, {
            headers: { Authorization: `Token ${token}` },
          }),
          axios.get<Department[]>(`${BASE_URL}/users/departments`, {
            headers: { Authorization: `Token ${token}` },
          }),
        ]);

        const leaveApps = leavesRes.data;
        const employees = employeesRes.data;
        const departments = departmentsRes.data;

        // Build maps for quick lookup
        const employeeMap: Record<string, Employee> = {};
        employees.forEach((emp) => {
          employeeMap[emp.employee_no] = emp;
        });

        const departmentMap: Record<number, string> = {};
        departments.forEach((dept) => {
          // Some APIs return department name as "name" or "title". Adjust as needed.
          departmentMap[dept.id] = (dept as any).name || (dept as any).title || "Unknown";
        });

        // Apply the date filter on leave applications (using the start_date)
        const filteredApps = leaveApps.filter((app) =>
          filterValue ? matchesFilter(app.start_date, filterType, filterValue) : true
        );

        // Aggregate data by department name
        const aggregated: Record<
          string,
          { totalLeaves: number; typeFreq: Record<string, number> }
        > = {};

        filteredApps.forEach((app) => {
          // Look up employee details using employee_no
          const emp = employeeMap[app.employee_no];
          // If employee details not found, assign "Unknown" department
          const deptName =
            emp && emp.department_id && departmentMap[emp.department_id]
              ? departmentMap[emp.department_id]
              : "Unknown";

          if (!aggregated[deptName]) {
            aggregated[deptName] = { totalLeaves: 0, typeFreq: {} };
          }
          // Sum up total leaves (using no_of_days_applied)
          aggregated[deptName].totalLeaves += app.no_of_days_applied;
          // Count frequency of leave type
          const type = app.leave_type_name;
          aggregated[deptName].typeFreq[type] = (aggregated[deptName].typeFreq[type] || 0) + 1;
        });

        // Convert aggregated data into rows
        const rows: LeaveReportRow[] = Object.entries(aggregated).map(([dept, stats]) => {
          let mostFrequent = "";
          let maxCount = 0;
          for (const type in stats.typeFreq) {
            if (stats.typeFreq[type] > maxCount) {
              maxCount = stats.typeFreq[type];
              mostFrequent = type;
            }
          }
          return {
            department: dept,
            total_leaves: stats.totalLeaves,
            most_frequent_type: mostFrequent,
          };
        });
        setData(rows);
      } catch (err: any) {
        console.error(err);
        setError("No data available.");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [token, filterType, filterValue]);

  // Pagination
  const paginatedData = data.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  const totalPages = Math.ceil(data.length / rowsPerPage);

  // For year filter, generate a list of recent years (e.g., current year and previous 10 years)
  const yearOptions = Array.from({ length: 11 }, (_, i) => (new Date().getFullYear() - i).toString());

  // Render filter UI above the table
  const renderFilterControls = () => {
    return (
      <div className="mb-4 flex flex-col gap-2 sm:flex-row sm:items-center">
        <label className="text-sm text-gray-700">Filter By:</label>
        <select
          value={filterType}
          onChange={handleFilterTypeChange}
          className="border rounded px-2 py-1"
        >
          <option value="day">Day</option>
          <option value="week">Week</option>
          <option value="month">Month</option>
          <option value="year">Year</option>
        </select>
        {filterType === "day" && (
          <input
            type="date"
            value={filterValue}
            onChange={handleFilterValueChange}
            className="border rounded px-2 py-1"
          />
        )}
        {filterType === "week" && (
          <input
            type="week"
            value={filterValue}
            onChange={handleFilterValueChange}
            className="border rounded px-2 py-1"
          />
        )}
        {filterType === "month" && (
          <input
            type="month"
            value={filterValue}
            onChange={handleFilterValueChange}
            className="border rounded px-2 py-1"
          />
        )}
        {filterType === "year" && (
          <select
            value={filterValue}
            onChange={handleFilterValueChange}
            className="border rounded px-2 py-1"
          >
            {yearOptions.map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
        )}
      </div>
    );
  };

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Leave Reports
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  return (
    <Screen headerContent={breadcrumb}>
      <div className="p-6 space-y-4">
        <h1 className="text-2xl font-semibold">Leave Reports</h1>
        {renderFilterControls()}
        {error && (
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        {loading ? (
          <p className="text-sm text-gray-500">Loading...</p>
        ) : data.length === 0 ? (
          <p className="text-sm text-gray-500">No data available.</p>
        ) : (
          <div className="overflow-x-auto border rounded-md">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {["Department", "Total Leaves Taken", "Most Frequent Type"].map(
                    (heading) => (
                      <th
                        key={heading}
                        className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase"
                      >
                        {heading}
                      </th>
                    )
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedData.map((row, index) => (
                  <tr key={index}>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                      {row.department}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                      {row.total_leaves}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                      {row.most_frequent_type}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination Controls */}
        {data.length > 0 && (
          <div className="flex items-center justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((prev) => prev - 1)}
              disabled={page === 0}
            >
              Previous
            </Button>
            <span className="text-sm text-gray-700">
              Page {page + 1} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((prev) => prev + 1)}
              disabled={page >= totalPages - 1}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </Screen>
  );
}
