import { useEffect, useState } from "react";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { EmployeeContactInfo } from "../../types/EmployeeTypes";
import { updateOrCreateRecord } from "../../utils/Records";
import { BASE_URL } from "@/config";

interface ContactInfoTabProps {
  employeeNo: string;
  onSaveSuccess?: () => void;
}

export default function ContactInfoTab({ employeeNo, onSaveSuccess }: ContactInfoTabProps) {
  const token = useSelector((state: RootState) => state.auth.token);
  const [contactInfo, setContactInfo] = useState<EmployeeContactInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }

    (async function fetchData() {
      setLoading(true);
      try {
        const headers = { Authorization: `Token ${token}` };
        const params = { employee_no: employeeNo };
        const res = await axios.get(`${BASE_URL}/users/employee-contact-info-details`, {
          headers,
          params,
        });
        if (Array.isArray(res.data) && res.data.length > 0) {
          setContactInfo(res.data[0]);
        } else {
          setContactInfo({ employee_no: employeeNo });
        }
      } catch (err: any) {
        setContactInfo({ employee_no: employeeNo });
        toast.error("Failed to fetch Contact Info");
      } finally {
        setLoading(false);
      }
    })();
  }, [token, employeeNo]);

  // Validate at least one phone exists before submitting
  async function handleSave() {
    if (!contactInfo || !token || !employeeNo) return;

    if (
      !contactInfo.home_phone_number &&
      !contactInfo.alternate_phone_number
    ) {
      toast.error("At least one phone number is required");
      return;
    }

    try {
      const updated = await updateOrCreateRecord<EmployeeContactInfo>(
        `${BASE_URL}/users/employee-contact-info-details`,
        token,
        { ...contactInfo, employee_no: employeeNo }
      );
      if (updated) {
        setContactInfo(updated);
        toast.success("Contact Info saved successfully");
        if (onSaveSuccess) onSaveSuccess();
      }
    } catch (error) {
      toast.error("Failed to save Contact Info");
    }
  }

  if (loading) return <div className="p-4">Loading Contact Info...</div>;
  if (!contactInfo) return null;

  // Google Maps help text
  const googleMapsHelp = (
    <span className="text-xs text-muted-foreground">
      Paste the Google Maps link to your current location (e.g., from the "Share" button in Google Maps).
    </span>
  );

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-2">
        Contact Info for Employee: {employeeNo}
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
          <Label>Postal Address</Label>
          <Input
            value={contactInfo.postal_address || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, postal_address: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>Home Phone Number</Label>
          <Input
            value={contactInfo.home_phone_number || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, home_phone_number: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>Alternate Phone Number</Label>
          <Input
            value={contactInfo.alternate_phone_number || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, alternate_phone_number: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>Residential Address</Label>
          <Input
            value={contactInfo.residential_address || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, residential_address: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>City</Label>
          <Input
            value={contactInfo.city || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, city: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>County</Label>
          <Input
            value={contactInfo.county || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, county: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>Company Email</Label>
          <Input
            type="email"
            value={contactInfo.company_email || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, company_email: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>Personal Email</Label>
          <Input
            type="email"
            value={contactInfo.personal_email || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, personal_email: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>Alternate Email</Label>
          <Input
            type="email"
            value={contactInfo.alternate_email || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, alternate_email: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>Country</Label>
          <Input
            value={contactInfo.country || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, country: e.target.value } : prev
              )
            }
          />
        </div>
        <div>
          <Label>Pin Location (Google Maps Link)</Label>
          <Input
            type="url"
            value={contactInfo.location_coordinates || ""}
            onChange={(e) =>
              setContactInfo((prev) =>
                prev ? { ...prev, location_coordinates: e.target.value } : prev
              )
            }
            placeholder="https://maps.app.goo.gl/..."
          />
          {googleMapsHelp}
        </div>
      </div>
      <Button onClick={handleSave} className="mt-4">
        Save Contact
      </Button>
    </div>
  );
}
