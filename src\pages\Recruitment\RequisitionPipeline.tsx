import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { motion } from "framer-motion";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert } from "@/components/ui/alert";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Loader2, X } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  B<PERSON>crumbList,
  <PERSON><PERSON>crumb<PERSON><PERSON>,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Screen } from "@/app-components/layout/screen";
import { BASE_URL } from "@/config";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";

// ------------------ Interfaces ------------------
interface JobVacancy {
  id: number;
  created_by: string;
  vacanc_status: string;
  job_code: string | null;
  vacancy_start_date?: string;
  vacancy_end_date?: string;
}

interface JobPosition {
  id: number;
  job_title: string;
  job_code: string;
  job_description: string;
}

interface JobRequisition {
  vacancyId: number;
  jobPositionId: number;
  job_code: string | null;
  vacanc_status: string;
  vacancy_start_date?: string;
  vacancy_end_date?: string;
  jobTitle: string;
  jobDescription: string;
}

interface InterviewQuestion {
  id: number;
  question: string;
  interview_level: number;
  employee_no: string;
  vacancy: number;
  assigned_to: string;
}

interface InterviewLevel {
  id: number;
  interview_level_no: number;
  interview_level: string;
  interview_total_score: number;
  level_status: string;
  vacancy: number;
  level_description?: string;
}

interface TempQuestion {
  localId: string;
  text: string;
  assignedTo: string;
  existing?: boolean;
  serverId?: number;
}

export interface TempLevel {
  localId: string;
  title: string;
  score: number;
  level_description: string;
  existing?: boolean;
  serverId?: number;
  questions: TempQuestion[];
}

interface EmployeeBio {
  id: number;
  first_name: string;
  middle_name: string;
  last_name: string;
  employee_no: string;
}

// ------------------ Main Page ------------------
export default function RecruitmentPage() {
  const breadcrumbs = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Recruitment Pipeline
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  return (
    <Screen headerContent={breadcrumbs}>
      <div className="p-4">
        <h1 className="text-3xl font-bold mb-4">Recruitment Pipeline</h1>
        <Tabs defaultValue="approvals">
          <TabsList className="mb-4">
            <TabsTrigger value="approvals">Approvals</TabsTrigger>
            <TabsTrigger value="vacancy-details">Vacancy Details</TabsTrigger>
            <TabsTrigger value="publishing">Publishing</TabsTrigger>
          </TabsList>
          <TabsContent value="approvals">
            <ApprovalsTab />
          </TabsContent>
          <TabsContent value="vacancy-details">
            <VacancyDetailsTab />
          </TabsContent>
          <TabsContent value="publishing">
            <PublishingTab />
          </TabsContent>
        </Tabs>
      </div>
    </Screen>
  );
}

// ------------------ ApprovalsTab ------------------
function ApprovalsTab() {
  const token = useSelector((state: RootState) => state.auth.token);
  const [requisitions, setRequisitions] = useState<JobVacancy[]>([]);
  const [jobPositionCache, setJobPositionCache] = useState<{ [code: string]: JobPosition }>({});
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: "success" | "error"; text: string } | null>(null);
  const [actionLoading, setActionLoading] = useState<number | null>(null);

  useEffect(() => {
    const fetchApprovals = async () => {
      setLoading(true);
      setMessage(null);
      try {
        const resp = await fetch(`${BASE_URL}/hrm/vacancies`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        });
        if (!resp.ok) {
          setMessage({ type: "error", text: "Failed to fetch pending approvals." });
          setLoading(false);
          return;
        }
        const data: JobVacancy[] = await resp.json();
        const pending = data.filter((v) => v.vacanc_status === "Pending Approval");
        setRequisitions(pending);
      } catch (err) {
        setMessage({ type: "error", text: "An error occurred fetching approvals." });
      } finally {
        setLoading(false);
      }
    };
    if (token) fetchApprovals();
    else setMessage({ type: "error", text: "No auth token found." });
  }, [token]);

  const fetchJobPosition = async (code: string) => {
    if (!code) return null;
    if (jobPositionCache[code]) return jobPositionCache[code];
    try {
      const resp = await fetch(`${BASE_URL}/hrm/job-positions?job_code=${code}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Token ${token}`,
        },
      });
      if (!resp.ok) return null;
      const data: JobPosition[] = await resp.json();
      if (data.length === 0) return null;
      const jobPos = data[0];
      setJobPositionCache((prev) => ({ ...prev, [code]: jobPos }));
      return jobPos;
    } catch {
      return null;
    }
  };

  useEffect(() => {
    const loadPositions = async () => {
      const codes = Array.from(new Set(requisitions.map((r) => r.job_code).filter(Boolean)));
      for (const c of codes) {
        if (c) await fetchJobPosition(c);
      }
    };
    if (requisitions.length > 0) loadPositions();
  }, [requisitions]);

  const handleAction = async (vacancyId: number, action: "approve" | "reject") => {
    setActionLoading(vacancyId);
    setMessage(null);
    try {
      const newStatus = action === "approve" ? "Approved" : "Closed";
      const resp = await fetch(`${BASE_URL}/hrm/vacancies/${vacancyId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Token ${token}`,
        },
        body: JSON.stringify({ vacanc_status: newStatus }),
      });
      if (!resp.ok) {
        setMessage({ type: "error", text: `Failed to ${action} vacancy.` });
        setActionLoading(null);
        return;
      }
      setMessage({ type: "success", text: `Vacancy ${action}d successfully.` });
      setRequisitions((prev) => prev.filter((r) => r.id !== vacancyId));
    } catch {
      setMessage({ type: "error", text: "An error occurred. Please try again later." });
    } finally {
      setActionLoading(null);
    }
  };

  return (
    <motion.div
      className="max-w-3xl mx-auto"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <h2 className="text-xl font-bold mb-4">Pending Approvals</h2>
      {message && (
        <Alert
          variant={message.type === "success" ? "default" : "destructive"}
          className="mb-4"
        >
          {message.text}
        </Alert>
      )}
      {loading && (
        <div className="text-center mt-4">
          <Loader2 className="animate-spin mx-auto" size={24} />
        </div>
      )}
      {!loading && requisitions.length === 0 && (
        <Alert variant="default" className="mt-4">
          No pending approvals.
        </Alert>
      )}
      {!loading && requisitions.length > 0 && (
        <ul className="divide-y">
          {requisitions.map((req) => {
            const pos = req.job_code ? jobPositionCache[req.job_code] : null;
            const title = pos?.job_title ?? `(No data for code: ${req.job_code})`;
            return (
              <li key={req.id} className="py-4 flex flex-col md:flex-row items-start">
                <div>
                  <p className="font-semibold">
                    {title} ({req.job_code ?? "null code"})
                  </p>
                  <p className="text-sm text-gray-600">Created by: {req.created_by}</p>
                  <p className="text-sm text-gray-600">Status: {req.vacanc_status}</p>
                  {pos && (
                    <p className="text-sm text-gray-600">Description: {pos.job_description}</p>
                  )}
                </div>
                <div className="flex space-x-2 ml-auto mt-2 md:mt-0">
                  <Button
                    variant="default"
                    onClick={() => handleAction(req.id, "approve")}
                    disabled={actionLoading === req.id}
                  >
                    {actionLoading === req.id ? "Approving..." : "Approve"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleAction(req.id, "reject")}
                    disabled={actionLoading === req.id}
                  >
                    {actionLoading === req.id ? "Rejecting..." : "Reject"}
                  </Button>
                </div>
              </li>
            );
          })}
        </ul>
      )}
    </motion.div>
  );
}

// ------------------ VacancyDetailsTab ------------------
function VacancyDetailsTab() {
  const INTERVIEW_LEVEL_OPTIONS = [
    "1st Interview",
    "2nd Interview",
    "3rd Interview",
    "4th Interview",
    "5th Interview",
  ];
  const INTERVIEW_SCORE_OPTIONS = [1, 2, 3, 4, 5];

  const token = useSelector((state: RootState) => state.auth.token);
  const employeeNo = useSelector((state: RootState) => state.auth.user?.employee_no || "EMP-001");
  const [vacancies, setVacancies] = useState<JobRequisition[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: "success" | "error"; text: string } | null>(null);
  const [levelsData, setLevelsData] = useState<{ [vacancyId: number]: TempLevel[] }>({});
  const [manageOpen, setManageOpen] = useState(false);
  const [selectedVacancy, setSelectedVacancy] = useState<JobRequisition | null>(null);

  // Fields for adding a new Level
  const [newLevelTitle, setNewLevelTitle] = useState("");
  const [newLevelScore, setNewLevelScore] = useState<number | "">("");
  const [newLevelDescription, setNewLevelDescription] = useState("");

  // ---------- NEW: We'll store all employees for "Assigned To" ----------
  const [employees, setEmployees] = useState<EmployeeBio[]>([]);
  // We fetch them once here:
  useEffect(() => {
    const fetchEmployees = async () => {
      if (!token) return;
      try {
        const resp = await fetch(`${BASE_URL}/users/employee-bio-details`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        });
        if (!resp.ok) return;
        const data: EmployeeBio[] = await resp.json();
        setEmployees(data);
      } catch (err) {
        // handle error if needed
      }
    };
    fetchEmployees();
  }, [token]);

  // ---------- Add Question Dialog states ----------
  const [addQuestionDialog, setAddQuestionDialog] = useState({ open: false, levelId: "" });
  const [questionText, setQuestionText] = useState("");
  // Instead of storing assignedTo as text, we store it as the selected employee's ID or employee_no
  const [questionAssignedTo, setQuestionAssignedTo] = useState("");
  // local search text in the Command for employees
  const [employeeSearchTerm, setEmployeeSearchTerm] = useState("");

  const [submittingAll, setSubmittingAll] = useState(false);

  const getLevels = (vacId: number) => levelsData[vacId] || [];
  const setLevels = (vacId: number, newLevels: TempLevel[]) => {
    setLevelsData((prev) => ({ ...prev, [vacId]: newLevels }));
  };

  // -------- Fetching Approved Vacancies --------
  useEffect(() => {
    const fetchApprovedVacancies = async () => {
      setLoading(true);
      setMessage(null);
      try {
        const resp = await fetch(`${BASE_URL}/hrm/vacancies`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        });
        if (!resp.ok) {
          setMessage({ type: "error", text: "Failed to fetch vacancies." });
          setLoading(false);
          return;
        }
        const data: JobVacancy[] = await resp.json();
        const approved = data.filter((v) => v.vacanc_status === "Approved");
        const merged: JobRequisition[] = [];
        for (const vac of approved) {
          let jobPosId = 0;
          let jobTitle = "N/A";
          let jobDesc = "N/A";
          if (vac.job_code) {
            try {
              const jobResp = await fetch(
                `${BASE_URL}/hrm/job-positions?job_code=${vac.job_code}`,
                {
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Token ${token}`,
                  },
                }
              );
              if (jobResp.ok) {
                const jobData: JobPosition[] = await jobResp.json();
                if (jobData.length > 0) {
                  const jobPos = jobData[0];
                  jobPosId = jobPos.id;
                  jobTitle = jobPos.job_title || "N/A";
                  jobDesc = jobPos.job_description || "N/A";
                }
              }
            } catch {}
          }
          merged.push({
            vacancyId: vac.id,
            jobPositionId: jobPosId,
            job_code: vac.job_code,
            vacanc_status: vac.vacanc_status,
            vacancy_start_date: vac.vacancy_start_date,
            vacancy_end_date: vac.vacancy_end_date,
            jobTitle,
            jobDescription: jobDesc,
          });
        }
        setVacancies(merged);
      } catch {
        setMessage({ type: "error", text: "Error fetching approved vacancies." });
      } finally {
        setLoading(false);
      }
    };
    if (token) fetchApprovedVacancies();
    else setMessage({ type: "error", text: "No auth token provided." });
  }, [token]);

  // -------- Fetch existing interview levels & questions --------
  const fetchInterviewLevels = async (vacancyId: number) => {
    setLevels(vacancyId, []); // reset first
    try {
      const levelResp = await fetch(`${BASE_URL}/recruitment/interview-levels`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Token ${token}`,
        },
      });
      if (!levelResp.ok) return;
      const allLevels: InterviewLevel[] = await levelResp.json();

      // Filter for the selected vacancy
      const vacancyLevels = allLevels.filter((lvl) => lvl.vacancy === vacancyId);

      const tempLevels: TempLevel[] = await Promise.all(
        vacancyLevels.map(async (lvl) => {
          let questions: InterviewQuestion[] = [];
          try {
            const qResp = await fetch(`${BASE_URL}/recruitment/interview-questions`, {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Token ${token}`,
              },
            });
            if (qResp.ok) {
              const allQuestions: InterviewQuestion[] = await qResp.json();
              // Filter for this vacancy & this level
              questions = allQuestions.filter(
                (q) => q.vacancy === vacancyId && q.interview_level === lvl.id
              );
            }
          } catch {}
          return {
            localId: lvl.id.toString(),
            title: lvl.interview_level,
            score: lvl.interview_total_score,
            level_description: lvl.level_description || "",
            existing: true,
            serverId: lvl.id,
            questions: questions.map((q) => ({
              localId: q.id.toString(),
              text: q.question,
              assignedTo: q.assigned_to,
              existing: true,
              serverId: q.id,
            })),
          };
        })
      );
      setLevels(vacancyId, tempLevels);
    } catch {
      setLevels(vacancyId, []);
    }
  };

  // -------- Dialog opening/closing for Manage Vacancy --------
  const openManage = (vac: JobRequisition) => {
    setSelectedVacancy(vac);
    setManageOpen(true);
  };

  const closeManage = () => {
    setSelectedVacancy(null);
    setManageOpen(false);
  };

  useEffect(() => {
    if (selectedVacancy) {
      fetchInterviewLevels(selectedVacancy.vacancyId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedVacancy]);

  // -------- Add a new Level locally --------
  const handleAddLevel = () => {
    if (!selectedVacancy) return;
    if (
      !newLevelTitle.trim() ||
      !newLevelScore ||
      newLevelScore < 0 ||
      !newLevelDescription.trim()
    ) {
      setMessage({
        type: "error",
        text: "Please provide a valid title, score, and description.",
      });
      return;
    }
    const newLvl: TempLevel = {
      localId: Math.random().toString(36).substr(2, 9),
      title: newLevelTitle.trim(),
      score: Number(newLevelScore),
      level_description: newLevelDescription.trim(),
      existing: false,
      questions: [],
    };
    const current = getLevels(selectedVacancy.vacancyId);
    setLevels(selectedVacancy.vacancyId, [...current, newLvl]);
    setNewLevelTitle("");
    setNewLevelScore("");
    setNewLevelDescription("");
  };

  const removeLevel = (localId: string) => {
    if (!selectedVacancy) return;
    const current = getLevels(selectedVacancy.vacancyId);
    const updated = current.filter((l) => l.localId !== localId);
    setLevels(selectedVacancy.vacancyId, updated);
  };

  // -------- Add a new Question to an existing/new Level --------
  const openAddQuestionDialog = (lvlId: string) => {
    setQuestionText("");
    setQuestionAssignedTo("");
    setEmployeeSearchTerm("");
    setAddQuestionDialog({ open: true, levelId: lvlId });
  };

  const closeAddQuestionDialog = () => {
    setAddQuestionDialog({ open: false, levelId: "" });
  };

  const handleSaveQuestion = () => {
    if (!selectedVacancy) return;
    if (!questionText.trim() || !questionAssignedTo.trim()) {
      setMessage({
        type: "error",
        text: "Please provide a question and select 'Assigned To'.",
      });
      return;
    }
    const current = getLevels(selectedVacancy.vacancyId);
    const updated = current.map((lvl) => {
      if (lvl.localId === addQuestionDialog.levelId) {
        return {
          ...lvl,
          questions: [
            ...lvl.questions,
            {
              localId: Math.random().toString(36).substr(2, 9),
              text: questionText.trim(),
              assignedTo: questionAssignedTo, // store the selected employee's number
              existing: false,
            },
          ],
        };
      }
      return lvl;
    });
    setLevels(selectedVacancy.vacancyId, updated);
    setQuestionText("");
    setQuestionAssignedTo("");
    closeAddQuestionDialog();
  };

  const removeQuestion = (levelLocalId: string, questionLocalId: string) => {
    if (!selectedVacancy) return;
    const current = getLevels(selectedVacancy.vacancyId);
    const updated = current.map((lvl) => {
      if (lvl.localId === levelLocalId) {
        return {
          ...lvl,
          questions: lvl.questions.filter((q) => q.localId !== questionLocalId),
        };
      }
      return lvl;
    });
    setLevels(selectedVacancy.vacancyId, updated);
  };

  // -------- Helpers for Display --------
  const getNumberOfLevels = (vacId: number) => getLevels(vacId).length;
  const getTotalScore = (vacId: number) =>
    getLevels(vacId).reduce((sum, l) => sum + l.score, 0);

  // -------- Submit all Levels & Questions to server --------
  const handleSubmitAll = async () => {
    if (!selectedVacancy) return;
    const allLocalLevels = getLevels(selectedVacancy.vacancyId);
    if (allLocalLevels.length === 0) {
      setMessage({ type: "error", text: "No levels to submit." });
      return;
    }
    setSubmittingAll(true);
    setMessage(null);
    try {
      const vacancyId = selectedVacancy.vacancyId;

      for (let i = 0; i < allLocalLevels.length; i++) {
        const lvl = allLocalLevels[i];
        let levelServerId = lvl.serverId;
        // Create the level if it's new
        if (!lvl.existing) {
          const payloadLevel = {
            interview_level_no: i + 1,
            interview_level: lvl.title,
            level_description: lvl.level_description,
            level_status: "Open",
            interview_total_score: lvl.score,
            vacancy: vacancyId,
          };
          const resp = await fetch(`${BASE_URL}/recruitment/interview-levels`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Token ${token}`,
            },
            body: JSON.stringify(payloadLevel),
          });
          if (!resp.ok) throw new Error("Failed to create interview level on the server.");
          const createdLevel = (await resp.json()) as { id: number };
          levelServerId = createdLevel.id;
        }

        // Create new questions under each level
        if (levelServerId) {
          for (const q of lvl.questions) {
            if (!q.existing) {
              const payloadQ = {
                question: q.text,
                employee_no: employeeNo,
                vacancy: vacancyId,
                interview_level: levelServerId,
                assigned_to: q.assignedTo, // store the selected employee's employee_no
              };
              const respQ = await fetch(`${BASE_URL}/recruitment/interview-questions`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Token ${token}`,
                },
                body: JSON.stringify(payloadQ),
              });
              if (!respQ.ok) {
                throw new Error("Failed to create interview question on the server.");
              }
            }
          }
        }
      }
      setMessage({ type: "success", text: "Levels & questions submitted successfully!" });
      // Refetch to ensure we have up-to-date IDs, etc.
      fetchInterviewLevels(selectedVacancy.vacancyId);
    } catch (err: any) {
      setMessage({ type: "error", text: err.message || "Submission failed." });
    } finally {
      setSubmittingAll(false);
    }
  };

  return (
    <motion.div
      className="max-w-5xl mx-auto"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <h2 className="text-xl font-bold mb-4">Approved Vacancies - Manage Interview Details</h2>
      {loading && (
        <div className="text-center mt-4">
          <Loader2 className="animate-spin mx-auto" size={24} />
        </div>
      )}
      {message && (
        <Alert
          variant={message.type === "success" ? "default" : "destructive"}
          className="my-4"
        >
          {message.text}
        </Alert>
      )}
      {!loading && vacancies.length === 0 && (
        <Alert variant="default" className="mt-4">
          No approved vacancies found.
        </Alert>
      )}
      <motion.div layout className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-4">
        {vacancies.map((vac) => (
          <Card key={vac.vacancyId} className="border">
            <CardContent>
              <h3 className="text-lg font-semibold">
                {vac.jobTitle} ({vac.job_code || "N/A"})
              </h3>
              <p className="text-sm text-gray-600 mt-1">Position ID: {vac.jobPositionId}</p>
              <p className="text-sm text-gray-600">Vacancy ID: {vac.vacancyId}</p>
              <p className="text-sm text-gray-600 mt-1">
                <strong>Dates:</strong> {vac.vacancy_start_date || "N/A"} -{" "}
                {vac.vacancy_end_date || "N/A"}
              </p>
            </CardContent>
            <CardFooter>
              <Button onClick={() => openManage(vac)}>Manage Vacancy</Button>
            </CardFooter>
          </Card>
        ))}
      </motion.div>

      {/* -------- Manage Vacancy Dialog -------- */}
      <Dialog
        open={manageOpen}
        onOpenChange={(open) => {
          if (!open) closeManage();
        }}
      >
        <DialogContent className="max-w-6xl w-full h-[80vh] overflow-y-auto">
          {selectedVacancy && (
            <>
              <DialogHeader className="flex justify-between items-center">
                <DialogTitle>Manage Vacancy</DialogTitle>
                <Button variant="ghost" onClick={closeManage}>
                  <X size={20} />
                </Button>
              </DialogHeader>
              <motion.div initial={{ y: -10, opacity: 0 }} animate={{ y: 0, opacity: 1 }}>
                <div>
                  <h3 className="text-lg font-semibold">
                    {selectedVacancy.jobTitle} ({selectedVacancy.job_code || "N/A"})
                  </h3>
                  <p className="text-sm text-gray-600">
                    Position ID: {selectedVacancy.jobPositionId} | Vacancy ID:{" "}
                    {selectedVacancy.vacancyId}
                  </p>
                </div>
                <div className="mt-2">
                  <p className="font-semibold">
                    Total Levels: {getNumberOfLevels(selectedVacancy.vacancyId)} | Total Score:{" "}
                    {getTotalScore(selectedVacancy.vacancyId)}
                  </p>

                  {getLevels(selectedVacancy.vacancyId).length === 0 ? (
                    <Alert variant="default" className="mt-2">
                      No levels added yet.
                    </Alert>
                  ) : (
                    <motion.div
                      layout
                      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2"
                    >
                      {getLevels(selectedVacancy.vacancyId).map((lvl) => (
                        <motion.div
                          key={lvl.localId}
                          layout
                          className="border rounded p-2"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                        >
                          <p className="font-semibold">
                            Level: {lvl.title}, Score: {lvl.score}
                            {lvl.existing && (
                              <span className="text-xs text-green-600 ml-2">(Saved)</span>
                            )}
                          </p>
                          <p className="text-sm text-gray-600">Description: {lvl.level_description}</p>
                          <div className="ml-4 mt-1">
                            {lvl.questions.length === 0 ? (
                              <p className="text-sm text-gray-600">No questions yet.</p>
                            ) : (
                              lvl.questions.map((q) => (
                                <motion.div
                                  key={q.localId}
                                  layout
                                  className="flex flex-col md:flex-row justify-between items-start md:items-center mt-1"
                                >
                                  <div>
                                    <p className="text-sm">- {q.text}</p>
                                    <p className="text-xs text-gray-500">
                                      Assigned to: {q.assignedTo}
                                    </p>
                                  </div>
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => removeQuestion(lvl.localId, q.localId)}
                                  >
                                    Remove
                                  </Button>
                                </motion.div>
                              ))
                            )}
                          </div>
                          <div className="mt-2 flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openAddQuestionDialog(lvl.localId)}
                            >
                              + Add Question
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => removeLevel(lvl.localId)}
                            >
                              Remove Level
                            </Button>
                          </div>
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </div>

                <div className="border rounded p-2 mt-4">
                  <h4 className="font-semibold mb-2">Add Another Level</h4>
                  <label className="text-sm font-semibold">Interview Level</label>
                  <Select value={newLevelTitle} onValueChange={(val) => setNewLevelTitle(val)}>
                    <SelectTrigger className="mb-2 w-full">
                      <SelectValue placeholder="Select interview level" />
                    </SelectTrigger>
                    <SelectContent>
                      {INTERVIEW_LEVEL_OPTIONS.map((lvl) => (
                        <SelectItem key={lvl} value={lvl}>
                          {lvl}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <label className="text-sm font-semibold">Score</label>
                  <Select
                    value={newLevelScore ? String(newLevelScore) : ""}
                    onValueChange={(val) => setNewLevelScore(Number(val))}
                  >
                    <SelectTrigger className="mb-2 w-full">
                      <SelectValue placeholder="Select score" />
                    </SelectTrigger>
                    <SelectContent>
                      {INTERVIEW_SCORE_OPTIONS.map((score) => (
                        <SelectItem key={score} value={String(score)}>
                          {score}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <label className="text-sm font-semibold">Level Description</label>
                  <Input
                    placeholder="Enter level description"
                    value={newLevelDescription}
                    onChange={(e) => setNewLevelDescription(e.target.value)}
                    className="mb-2"
                  />
                  <Button onClick={handleAddLevel}>+ Add Level</Button>
                </div>
                <div className="mt-4">
                  <Button variant="default" onClick={handleSubmitAll} disabled={submittingAll}>
                    {submittingAll ? "Submitting..." : "Submit All Levels & Questions"}
                  </Button>
                </div>
              </motion.div>
              <DialogFooter>
                <Button onClick={closeManage}>Close</Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* -------- Add Question Dialog -------- */}
      <Dialog
        open={addQuestionDialog.open}
        onOpenChange={(open) => {
          if (!open) closeAddQuestionDialog();
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Question</DialogTitle>
          </DialogHeader>

          <p className="text-sm text-gray-600 mb-2">Employee No (creator): {employeeNo}</p>

          {/* Question input */}
          <label className="block text-sm font-medium">Question</label>
          <textarea
            placeholder="Question"
            value={questionText}
            onChange={(e) => setQuestionText(e.target.value)}
            className="w-full p-2 mb-2 border rounded"
          />

          {/* Searchable dropdown for "Assigned To" */}
          <label className="block text-sm font-medium mb-1">Assigned To</label>
          <Command className="border rounded mb-2">
            <CommandInput
              placeholder="Search employee by name..."
              value={employeeSearchTerm}
              onValueChange={(val) => setEmployeeSearchTerm(val)}
            />
            <CommandEmpty>No employees found.</CommandEmpty>
            <CommandGroup>
              {employees
                .filter((emp) => {
                  const fullName = (
                    emp.first_name +
                    " " +
                    (emp.middle_name || "") +
                    " " +
                    emp.last_name
                  )
                    .toLowerCase()
                    .trim();
                  return fullName.includes(employeeSearchTerm.toLowerCase().trim());
                })
                .map((emp) => {
                  const fullName = `${emp.first_name} ${
                    emp.middle_name ? emp.middle_name + " " : ""
                  }${emp.last_name}`;
                  return (
                    <CommandItem
                      key={emp.id}
                      onSelect={() => {
                        setQuestionAssignedTo(emp.employee_no);
                        setEmployeeSearchTerm(fullName); // show the name in the input
                      }}
                    >
                      {fullName} &middot; <span className="text-xs text-gray-500">{emp.employee_no}</span>
                    </CommandItem>
                  );
                })}
            </CommandGroup>
          </Command>

          <DialogFooter>
            <Button variant="outline" onClick={closeAddQuestionDialog}>
              Cancel
            </Button>
            <Button variant="secondary" onClick={handleSaveQuestion}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
}

// ------------------ PublishingTab ------------------
export function PublishingTab() {
  const token = useSelector((state: RootState) => state.auth.token);
  const [approvedJobs, setApprovedJobs] = useState<JobRequisition[]>([]);
  const [selectedJob, setSelectedJob] = useState<JobRequisition | null>(null);

  const [vacancyStartDate, setVacancyStartDate] = useState("");
  const [vacancyEndDate, setVacancyEndDate] = useState("");
  const [publishOption, setPublishOption] = useState(""); 
  // ^--- new state to hold 'internal' | 'external' | 'both'

  const [loading, setLoading] = useState(true);
  const [publishing, setPublishing] = useState(false);
  const [message, setMessage] = useState<{ type: "success" | "error"; text: string } | null>(null);

  // ------------------ Fetch all approved vacancies ------------------
  useEffect(() => {
    const fetchApproved = async () => {
      setLoading(true);
      setMessage(null);
      try {
        const resp = await fetch(`${BASE_URL}/hrm/vacancies`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        });
        if (!resp.ok) {
          setMessage({ type: "error", text: "Failed to fetch vacancies." });
          setLoading(false);
          return;
        }
        // transform the data into JobRequisition objects
        const data: JobVacancy[] = await resp.json();
        const filtered = data.filter((v) => v.vacanc_status === "Approved");

        const merged: JobRequisition[] = [];
        for (const vac of filtered) {
          let jobPosId = 0;
          let jobTitle = "N/A";
          let jobDesc = "N/A";
          if (vac.job_code) {
            try {
              const jobResp = await fetch(
                `${BASE_URL}/hrm/job-positions?job_code=${vac.job_code}`,
                {
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Token ${token}`,
                  },
                }
              );
              if (jobResp.ok) {
                const jobData: JobPosition[] = await jobResp.json();
                if (jobData.length > 0) {
                  const jobPos = jobData[0];
                  jobPosId = jobPos.id;
                  jobTitle = jobPos.job_title || "N/A";
                  jobDesc = jobPos.job_description || "N/A";
                }
              }
            } catch {}
          }
          merged.push({
            vacancyId: vac.id,
            jobPositionId: jobPosId,
            job_code: vac.job_code,
            vacanc_status: vac.vacanc_status,
            vacancy_start_date: vac.vacancy_start_date,
            vacancy_end_date: vac.vacancy_end_date,
            jobTitle,
            jobDescription: jobDesc,
          });
        }
        setApprovedJobs(merged);
      } catch {
        setMessage({ type: "error", text: "Error fetching approved jobs." });
      } finally {
        setLoading(false);
      }
    };
    if (token) fetchApproved();
    else {
      setMessage({ type: "error", text: "No auth token provided." });
      setLoading(false);
    }
  }, [token]);

  // ------------------ Handle publishing a job ------------------
  const handlePublish = async () => {
    if (!selectedJob) return;
    if (!vacancyStartDate || !vacancyEndDate) {
      setMessage({ type: "error", text: "Please provide both Start and End date." });
      return;
    }
    if (new Date(vacancyStartDate) > new Date(vacancyEndDate)) {
      setMessage({ type: "error", text: "Start date cannot be after end date." });
      return;
    }
    // We likely also want to ensure user chose a publish method
    if (!publishOption) {
      setMessage({ type: "error", text: "Please specify where to publish (internal, external, or both)." });
      return;
    }

    setPublishing(true);
    setMessage(null);

    try {
      // PATCH the existing vacancy with updated dates + publish method
      const resp = await fetch(`${BASE_URL}/hrm/vacancies/${selectedJob.vacancyId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Token ${token}`,
        },
        body: JSON.stringify({
          vacancy_start_date: vacancyStartDate,
          vacancy_end_date: vacancyEndDate,
          publish: publishOption,      // <-- pass the publish enum value
        }),
      });

      if (!resp.ok) {
        setMessage({ type: "error", text: "Failed to publish job." });
        setPublishing(false);
        return;
      }

      setMessage({ type: "success", text: "Job published successfully." });

      // Update local list so the user sees it
      setApprovedJobs((prev) =>
        prev.map((j) =>
          j.vacancyId === selectedJob.vacancyId
            ? {
                ...j,
                vacancy_start_date: vacancyStartDate,
                vacancy_end_date: vacancyEndDate,
              }
            : j
        )
      );

      // Reset the form
      setSelectedJob(null);
      setVacancyStartDate("");
      setVacancyEndDate("");
      setPublishOption("");

    } catch {
      setMessage({ type: "error", text: "An error occurred. Please try again." });
    } finally {
      setPublishing(false);
    }
  };

  // ------------------ Rendering ------------------
  return (
    <motion.div
      className="max-w-3xl mx-auto"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <h2 className="text-xl font-bold mb-4">Publish Approved Jobs</h2>

      {/* Show a loading spinner if needed */}
      {loading && (
        <div className="text-center mt-4">
          <Loader2 className="animate-spin mx-auto" size={24} />
        </div>
      )}

      {/* Show message alerts */}
      {message && (
        <Alert
          variant={message.type === "success" ? "default" : "destructive"}
          className="my-4"
        >
          {message.text}
        </Alert>
      )}

      {/* If not loading but no approved jobs */}
      {!loading && approvedJobs.length === 0 && (
        <Alert variant="default" className="mt-4">
          No approved jobs to publish.
        </Alert>
      )}

      {/* List of approved jobs */}
      {!loading && approvedJobs.length > 0 && (
        <motion.div layout className="mt-4 border rounded">
          <ul>
            {approvedJobs.map((job) => (
              <li
                key={job.vacancyId}
                className="flex justify-between items-center p-4 border-b last:border-0"
              >
                <div>
                  <p className="font-semibold">
                    {job.jobTitle} ({job.job_code || "N/A"})
                  </p>
                  {job.vacancy_start_date && job.vacancy_end_date ? (
                    <p className="text-sm text-gray-600">
                      Current: {job.vacancy_start_date} - {job.vacancy_end_date}
                    </p>
                  ) : (
                    <p className="text-sm text-gray-600">Not published yet.</p>
                  )}
                </div>
                <Button
                  onClick={() => {
                    setSelectedJob(job);
                    // prefill any existing values
                    setVacancyStartDate(job.vacancy_start_date || "");
                    setVacancyEndDate(job.vacancy_end_date || "");
                    // If you track any previously chosen publishOption, set that here
                    setPublishOption("");
                  }}
                >
                  Publish
                </Button>
              </li>
            ))}
          </ul>
        </motion.div>
      )}

      {/* Dialog: Publish Job */}
      <Dialog
        open={Boolean(selectedJob)}
        onOpenChange={(open) => {
          if (!open) {
            // Reset everything if the dialog is closed
            setSelectedJob(null);
            setPublishOption("");
          }
        }}
      >
        <DialogContent className="max-w-md w-full p-4">
          {selectedJob && (
            <>
              <DialogHeader className="flex justify-between items-center">
                <DialogTitle>Publish Job</DialogTitle>
                <Button variant="ghost" onClick={() => setSelectedJob(null)}>
                  <X size={20} />
                </Button>
              </DialogHeader>

              <motion.div initial={{ y: -10, opacity: 0 }} animate={{ y: 0, opacity: 1 }}>
                <h3 className="text-lg font-semibold">
                  {selectedJob.jobTitle} ({selectedJob.job_code || "N/A"})
                </h3>

                <div className="mt-2">
                  <label className="block text-sm font-medium">Start Date</label>
                  <Input
                    type="date"
                    value={vacancyStartDate}
                    onChange={(e) => setVacancyStartDate(e.target.value)}
                    className="mt-1"
                  />
                </div>

                <div className="mt-2">
                  <label className="block text-sm font-medium">End Date</label>
                  <Input
                    type="date"
                    value={vacancyEndDate}
                    onChange={(e) => setVacancyEndDate(e.target.value)}
                    className="mt-1"
                  />
                </div>

                {/* ---------------- PUBLISH DROPDOWN ---------------- */}
                <div className="mt-2">
                  <label className="block text-sm font-medium">Publish</label>
                  <Select value={publishOption} onValueChange={setPublishOption}>
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue placeholder="Select where to publish" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="internal">Internal</SelectItem>
                      <SelectItem value="external">External</SelectItem>
                      <SelectItem value="both">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </motion.div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setSelectedJob(null)}>
                  Cancel
                </Button>
                <Button variant="default" onClick={handlePublish} disabled={publishing}>
                  {publishing ? "Publishing..." : "Publish"}
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </motion.div>
  );
}
