"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectItem,
  SelectContent,
} from "@/components/ui/select";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
  TableCaption,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { BASE_URL } from "@/config";
import {
  JobVacancy,
  ExternalUserBioData,
  TalentPoolEntry,
  VacancyApplication,
} from "../types";

/**
 * If you haven't already defined these in `types.ts`,
 * ensure you do, or define them inline here.
 *
 * export type TalentPoolEntry = {
 *   id: number;
 *   date_added: string;
 *   date_updated: string;
 *   status: boolean;
 *   external_user_no: string;
 *   job_vacancy: number;
 * };
 *
 * export type VacancyApplication = {
 *   id: number;
 *   application_status: string;
 *   external_user_no: string;
 *   vacancy: number;
 *   // etc. (other fields)
 * };
 */

const TalentPoolTab: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);

  // Data from the server
  const [talentPool, setTalentPool] = useState<TalentPoolEntry[]>([]);
  const [vacancies, setVacancies] = useState<JobVacancy[]>([]);
  const [bioData, setBioData] = useState<ExternalUserBioData[]>([]);
  const [applications, setApplications] = useState<VacancyApplication[]>([]);

  // Mappings
  const [candidateNameMap, setCandidateNameMap] = useState<Record<string, string>>({});
  const [vacancyTitleMap, setVacancyTitleMap] = useState<Record<number, string>>({});

  // UI states
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Create Dialog State
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<boolean>(true);

  // We'll store the selected candidate in `selectedExternalUserNo`
  const [selectedExternalUserNo, setSelectedExternalUserNo] = useState("");
  // We'll also store the new job vacancy here
  const [newJobVacancy, setNewJobVacancy] = useState<number | null>(null);

  // For embedded search inside the candidate dropdown
  const [candidateSearchQuery, setCandidateSearchQuery] = useState("");

  // Delete Dialog State
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [entryToDelete, setEntryToDelete] = useState<TalentPoolEntry | null>(null);

  /**
   * Fetch:
   * - Talent Pool
   * - Vacancies
   * - ExternalUserBioData
   * - VacancyApplication
   */
  useEffect(() => {
    const fetchAllData = async () => {
      setLoading(true);
      setError(null);

      try {
        // 1) Talent Pool
        const poolRes = await fetch(`${BASE_URL}/recruitment/talent-pool`, {
          headers: { "Content-Type": "application/json" },
        });
        if (!poolRes.ok) throw new Error("Failed to fetch talent pool data");
        const poolData: TalentPoolEntry[] = await poolRes.json();

        // 2) Vacancies
        const vacRes = await fetch(`${BASE_URL}/hrm/vacancies`, {
          headers: { "Content-Type": "application/json" },
        });
        if (!vacRes.ok) throw new Error("Failed to fetch vacancies");
        const vacData: JobVacancy[] = await vacRes.json();

        // 3) Bio Data
        const bioRes = await fetch(`${BASE_URL}/recruitment/external-user-bio-data`, {
          headers: { "Content-Type": "application/json" },
        });
        if (!bioRes.ok) throw new Error("Failed to fetch external user bio data");
        const bioJson: ExternalUserBioData[] = await bioRes.json();

        // 4) Vacancy Applications
        const appsRes = await fetch(`${BASE_URL}/recruitment/vacancy-application`, {
          headers: { "Content-Type": "application/json" },
        });
        if (!appsRes.ok) throw new Error("Failed to fetch vacancy applications");
        const appsData: VacancyApplication[] = await appsRes.json();

        // Build candidate name map
        const nameMap: Record<string, string> = {};
        bioJson.forEach((b) => {
          const fullName = `${b.first_name} ${b.middle_name ?? ""} ${b.last_name}`.trim();
          nameMap[b.external_user_no] = fullName;
        });

        // Build vacancy title map
        const vacMap: Record<number, string> = {};
        vacData.forEach((v) => {
          vacMap[v.id] = v.job_details.job_title;
        });

        setTalentPool(poolData);
        setVacancies(vacData);
        setBioData(bioJson);
        setApplications(appsData);
        setCandidateNameMap(nameMap);
        setVacancyTitleMap(vacMap);
      } catch (err: any) {
        console.error(err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, [token]);

  /**
   * Filter the `bioData` list locally by the candidateSearchQuery
   */
  const filteredCandidates = useMemo(() => {
    if (!candidateSearchQuery.trim()) {
      return bioData;
    }
    const query = candidateSearchQuery.toLowerCase();
    return bioData.filter((c) => {
      const fullName = `${c.first_name} ${c.middle_name ?? ""} ${c.last_name}`.trim().toLowerCase();
      return (
        fullName.includes(query) ||
        (c.email && c.email.toLowerCase().includes(query)) ||
        (c.id_number && c.id_number.toLowerCase().includes(query))
      );
    });
  }, [bioData, candidateSearchQuery]);

  /**
   * Called when user selects a candidate from the drop-down
   * We'll auto-fill newJobVacancy from that candidate’s `vacancy_application`
   */
  const handleSelectCandidate = (externalNo: string) => {
    setSelectedExternalUserNo(externalNo);

    // find the application(s) for that user
    const userApps = applications.filter((app) => app.external_user_no === externalNo);

    if (userApps.length === 1) {
      // If exactly one, set newJobVacancy = that one
      setNewJobVacancy(userApps[0].vacancy);
    } else if (userApps.length > 1) {
      // Possibly handle multiple; for now, let's pick the first as an example
      setNewJobVacancy(userApps[0].vacancy);
    } else {
      // If no matching apps found, or user has never applied
      setNewJobVacancy(null);
    }
  };

  /**
   * Create the new TalentPool entry
   */
  const handleCreateTalentPool = async () => {
    if (!selectedExternalUserNo) {
      setError("Please select a candidate.");
      return;
    }
    if (!newJobVacancy) {
      setError("No vacancy found for this candidate’s application.");
      return;
    }
    try {
      setLoading(true);
      setError(null);

      const res = await fetch(`${BASE_URL}/recruitment/talent-pool`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          external_user_no: selectedExternalUserNo,
          job_vacancy: newJobVacancy,
          status: newStatus,
        }),
      });

      if (!res.ok) {
        const text = await res.text();
        throw new Error("Failed to create talent pool entry: " + text);
      }

      // Reset
      setCreateDialogOpen(false);
      setSelectedExternalUserNo("");
      setNewJobVacancy(null);
      setNewStatus(true);
      setCandidateSearchQuery("");

      // Refresh
      const refreshed = await fetch(`${BASE_URL}/recruitment/talent-pool`);
      if (refreshed.ok) {
        const newData: TalentPoolEntry[] = await refreshed.json();
        setTalentPool(newData);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Remove from talent pool
   */
  const handleRemove = (entry: TalentPoolEntry) => {
    setEntryToDelete(entry);
    setDeleteDialogOpen(true);
  };

  const confirmRemove = async () => {
    if (!entryToDelete) return;
    try {
      setLoading(true);
      setError(null);

      const deleteRes = await fetch(
        `${BASE_URL}/recruitment/talent-pool/${entryToDelete.id}`,
        {
          method: "DELETE",
          headers: { "Content-Type": "application/json" },
        }
      );

      if (!deleteRes.ok) {
        const text = await deleteRes.text();
        throw new Error(`Failed to delete from talent pool: ${text}`);
      }

      // Remove locally
      setTalentPool((prev) => prev.filter((tp) => tp.id !== entryToDelete.id));
    } catch (err: any) {
      setError(err.message);
    } finally {
      setDeleteDialogOpen(false);
      setEntryToDelete(null);
      setLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded shadow space-y-4">
      <h2 className="text-xl font-semibold">Talent Pool</h2>
      <p className="text-sm text-gray-600">
        This is a pool of potential recruits (including interns) who showed promise
        but did not qualify previously. HR can revisit this pool when new positions
        open up.
      </p>

      {error && <p className="text-red-500">{error}</p>}
      {loading && <p>Loading...</p>}

      <div className="text-right">
        <Button onClick={() => setCreateDialogOpen(true)}>Add to Talent Pool</Button>
      </div>

      {/* Talent Pool Table */}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Job Title</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date Added</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {talentPool.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5}>No entries in the talent pool.</TableCell>
              </TableRow>
            ) : (
              talentPool.map((entry) => {
                const fullName =
                  candidateNameMap[entry.external_user_no] ?? entry.external_user_no;
                const jobTitle =
                  vacancyTitleMap[entry.job_vacancy] ?? `Vacancy #${entry.job_vacancy}`;

                return (
                  <TableRow key={entry.id}>
                    <TableCell>{fullName}</TableCell>
                    <TableCell>{jobTitle}</TableCell>
                    <TableCell>
                      {entry.status ? (
                        <Badge variant="outline">Active</Badge>
                      ) : (
                        <Badge variant="destructive">Inactive</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(entry.date_added).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleRemove(entry)}
                      >
                        Remove
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
          <TableCaption>
            {talentPool.length} candidate
            {talentPool.length === 1 ? "" : "s"} in the Talent Pool
          </TableCaption>
        </Table>
      </div>

      {/* Create / Add Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-xl">
          <DialogHeader>
            <DialogTitle>Add Candidate to Talent Pool</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 mt-2">
            {/* Single dropdown with search */}
            <div>
              <Label className="mb-1">Candidate</Label>
              <Select
                value={selectedExternalUserNo}
                // When user picks a candidate from the dropdown:
                onValueChange={(val) => handleSelectCandidate(val)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pick a candidate" />
                </SelectTrigger>
                <SelectContent>
                  {/* Input for searching within the dropdown */}
                  <div className="p-2">
                    <Input
                      placeholder="Search candidates..."
                      value={candidateSearchQuery}
                      onChange={(e) => setCandidateSearchQuery(e.target.value)}
                    />
                  </div>
                  {filteredCandidates.map((c) => {
                    const displayName = `${c.first_name} ${c.middle_name ?? ""} ${c.last_name}`.trim();
                    return (
                      <SelectItem key={c.id} value={c.external_user_no}>
                        {displayName}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* We show the chosen vacancy but keep it read-only or hidden
                because we auto-filled it from the candidate's application */}
            <div>
              <Label>Job Vacancy</Label>
              <Select
                disabled
                onValueChange={() => {}}
                value={newJobVacancy ? newJobVacancy.toString() : ""}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="(auto-filled from application)" />
                </SelectTrigger>
                <SelectContent>
                  {/* Typically we won't let user override, but 
                      if you want them to override, remove `disabled` above. */}
                  {vacancies.map((v) => (
                    <SelectItem key={v.id} value={v.id.toString()}>
                      {v.job_details.job_title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                This is auto-filled from the candidate’s latest application.
              </p>
            </div>

            {/* Active / Inactive Status */}
            <div>
              <Label>Status</Label>
              <Select
                onValueChange={(val) => setNewStatus(val === "true")}
                value={newStatus ? "true" : "false"}
              >
                <SelectTrigger className="w-full mt-1">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTalentPool}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirm Removal Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove from Talent Pool?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this candidate from the talent pool?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setDeleteDialogOpen(false);
                setEntryToDelete(null);
              }}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmRemove}>
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default TalentPoolTab;
