"use client";

import React, { useEffect, useState, useMemo } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useNavigate } from "react-router-dom";

import { BASE_URL } from "@/config";

// Shadcn/UI components
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

// Recharts components
import {
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Bar,
  Label,
} from "recharts";

// Example chart container & tooltip components (adjust these imports as needed)
import {
  <PERSON><PERSON><PERSON>r,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

// Define the Calendar Efficiency Data type based on your API
interface CalendarEfficiencyData {
  calendar_name: string;
  calendar_description: string;
  start_date: string;
  end_date: string;
  total_days: number;
  working_days: number;
  non_working_days: number;
  working_days_ratio: number;
  non_working_by_reason: { [reason: string]: number };
  special_days_impact: { [day: string]: number };
  restricted_days: number;
  public_holidays: number;
  status: string;
}

const CalendarAnalyticsDashboard: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();

  const [data, setData] = useState<CalendarEfficiencyData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch calendar efficiency data from the API
  useEffect(() => {
    const fetchCalendarData = async () => {
      setLoading(true);
      setError(null);

      try {
        const endpoint = `${BASE_URL}/analytics/calendar-efficiency/`;
        const res = await fetch(endpoint, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            // Use "Token <token>" if that's what your API expects
            Authorization: token ? `Token ${token}` : "",
          },
        });

        if (!res.ok) {
          const text = await res.text();
          if (text.startsWith("<!DOCTYPE")) {
            throw new Error(
              "Server returned an HTML error page instead of JSON. Check your endpoint or authorization."
            );
          } else {
            throw new Error(`Request failed with status ${res.status}: ${text}`);
          }
        }

        const json: CalendarEfficiencyData = await res.json();
        setData(json);
      } catch (err: any) {
        setError(err.message || "Failed to fetch calendar efficiency data.");
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      fetchCalendarData();
    } else {
      setError("Missing authorization token.");
    }
  }, [token]);

  // Prepare data for the Non-Working Days by Reason pie chart
  const nonWorkingPieData = useMemo(() => {
    if (!data?.non_working_by_reason) return [];
    return Object.entries(data.non_working_by_reason).map(([reason, count]) => ({
      name: reason,
      value: count,
    }));
  }, [data]);

  // Define a palette of colors for non-working reasons
  const nonWorkingColors = [
    "#4caf50",
    "#2196f3",
    "#ff9800",
    "#9c27b0",
    "#e91e63",
    "#00bcd4",
  ];

  // Prepare data for the Special Days Impact bar chart
  const specialDaysBarData = useMemo(() => {
    if (!data?.special_days_impact) return [];
    return Object.entries(data.special_days_impact).map(([day, count]) => ({
      day,
      count,
    }));
  }, [data]);

  if (loading) {
    return (
      <Screen>
        <div className="p-4">Loading calendar analytics...</div>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <div className="p-4 text-red-500">{error}</div>
      </Screen>
    );
  }

  if (!data) {
    return (
      <Screen>
        <div className="p-4">No calendar efficiency data found.</div>
      </Screen>
    );
  }

  return (
    <Screen>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Calendar Analytics</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="p-4 space-y-8">
        {/* Calendar Metadata */}
        <Card>
          <CardHeader>
            <CardTitle>{data.calendar_name}</CardTitle>
            <CardDescription>{data.calendar_description}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              {`From: ${data.start_date} To: ${data.end_date}`}
            </p>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Total Days</CardTitle>
              <CardDescription>Overall days in the calendar</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{data.total_days}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Working Days</CardTitle>
              <CardDescription>Days used for work</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{data.working_days}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Non-Working Days</CardTitle>
              <CardDescription>Days off, holidays, etc.</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{data.non_working_days}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Working Days Ratio</CardTitle>
              <CardDescription>Working/Total days ratio</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{(data.working_days_ratio * 100).toFixed(2)}%</p>
            </CardContent>
          </Card>
        </div>

        {/* Non-Working Days by Reason and Special Days Impact side-by-side */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Non-Working Days by Reason (Pie Chart) */}
          <Card>
            <CardHeader>
              <CardTitle>Non-Working Days by Reason</CardTitle>
              <CardDescription>Breakdown of non-working days</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              {nonWorkingPieData.length === 0 ? (
                <p>No data available.</p>
              ) : (
                <ChartContainer config={{}} className="w-full max-h-[300px]">
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Tooltip
                        cursor={false}
                        content={<ChartTooltipContent hideLabel />}
                      />
                      <Pie
                        data={nonWorkingPieData}
                        dataKey="value"
                        nameKey="name"
                        innerRadius={60}
                        outerRadius={100}
                        strokeWidth={5}
                      >
                        <Label
                          content={({ viewBox }) => {
                            if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                              const total = nonWorkingPieData.reduce(
                                (acc, curr) => acc + curr.value,
                                0
                              );
                              return (
                                <text
                                  x={viewBox.cx}
                                  y={viewBox.cy}
                                  textAnchor="middle"
                                  dominantBaseline="middle"
                                >
                                  <tspan className="fill-foreground text-2xl font-bold">
                                    {total}
                                  </tspan>
                                  <tspan
                                    x={viewBox.cx}
                                    y={(viewBox.cy || 0) + 20}
                                    className="fill-muted-foreground text-sm"
                                  >
                                    Days
                                  </tspan>
                                </text>
                              );
                            }
                            return null;
                          }}
                        />
                        {nonWorkingPieData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={nonWorkingColors[index % nonWorkingColors.length]}
                          />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              )}
            </CardContent>
          </Card>

          {/* Special Days Impact (Bar Chart) */}
          <Card>
            <CardHeader>
              <CardTitle>Special Days Impact</CardTitle>
              <CardDescription>Occurrences of special days</CardDescription>
            </CardHeader>
            <CardContent>
              {specialDaysBarData.length === 0 ? (
                <p>No data available.</p>
              ) : (
                <ChartContainer config={{}} className="w-full h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={specialDaysBarData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" tick={{ fontSize: 12 }} />
                      <YAxis />
                      <Tooltip
                        cursor={false}
                        content={<ChartTooltipContent hideLabel />}
                      />
                      <Bar dataKey="count" fill="#8884d8">
                        {specialDaysBarData.map((entry, index) => (
                          <Cell key={`cell-${index}`} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              )}
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={() => navigate("/calender-management")}>
                Go to Calendar Management
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default CalendarAnalyticsDashboard;
