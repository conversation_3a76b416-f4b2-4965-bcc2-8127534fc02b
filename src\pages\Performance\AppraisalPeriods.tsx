import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { RootState } from '../../redux/store';
import axios from 'axios';
import { Button } from '@/components/ui/button'; // Shadcn Button
import { CreateAppraisalModal } from "@/app-components/Performance/appraisal/CreateAppraisalPeriodModal";
import { BASE_URL } from '../../config';
import { AppraisalPeriodFilters } from "@/app-components/Performance/appraisal/AppraisalPeriodFilters";
import { AppraisalPeriodList } from "@/app-components/Performance/appraisal/AppraisalPeriodList";
import { Plus, Calendar, Eye } from 'lucide-react'; // Added Eye icon for the "View" button
import { Screen } from "@/app-components/layout/screen";
import { AppraisalDetails } from "../../app-components/Performance/appraisal/AppraisalDetails" // Import the details view component

type CycleType = 'Monthly' | 'Quarterly' | 'Yearly';

interface AppraisalPeriod {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  cycle_type: CycleType;
  description: string;
  is_active: boolean;
  created_by: string;
  status?: 'active' | 'upcoming' | 'completed';
  progress?: number;
}

export const AppraisalPeriods = () => {
  const navigate = useNavigate();
  const { token } = useSelector((state: RootState) => state.auth);
  const [periods, setPeriods] = useState<AppraisalPeriod[]>([]);
  const [selectedType, setSelectedType] = useState<CycleType | 'all'>('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [viewPeriodId, setViewPeriodId] = useState<any | null>(null); // State to manage which period to view

  useEffect(() => {
    if (!token) navigate('/login');
  }, [token, navigate]);

  const calculateProgress = (startDate: string, endDate: string): number => {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();
    const now = Date.now();
    if (now < start) return 0;
    if (now > end) return 100;
    return ((now - start) / (end - start)) * 100;
  };

  const fetchPeriods = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (selectedType !== 'all') {
        params.cycle_type = selectedType;
      }

      const response = await axios.get(`${BASE_URL}/hrm/appraisal-periods`, {
        headers: { Authorization: `Token ${token}` },
        params,
      });

      const periodsWithStatus = response.data.map((period: AppraisalPeriod) => {
        const start = new Date(period.start_date).getTime();
        const end = new Date(period.end_date).getTime();
        const now = Date.now();
        let status: 'active' | 'upcoming' | 'completed' = 'upcoming';

        if (now >= start && now <= end) status = 'active';
        else if (now > end) status = 'completed';

        return {
          ...period,
          status,
          progress: calculateProgress(period.start_date, period.end_date),
        };
      });

      setPeriods(periodsWithStatus);
    } catch (error) {
      console.error('Error fetching appraisal periods:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) fetchPeriods();
  }, [token, selectedType]);

  const handleCreateSuccess = () => {
    fetchPeriods();
    setModalOpen(false);
  };

  const handleViewPeriod = (id: string) => {
    setViewPeriodId(id); // Set the period ID to view details
  };

  if (!token) {
    return (
      <div className="flex justify-center mt-4">
        <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-green-500"></div> {/* Custom loading spinner */}
      </div>
    );
  }

  return (
    <Screen>
      <div className="p-4 bg-background min-h-screen">
        {/* Header Card */}
        <div className="p-8 mb-8 rounded-lg bg-gradient-to-br from-green-500 to-green-700 text-white shadow-lg">
          <div className="flex items-center space-x-4">
            <Calendar className="text-white text-4xl" />
            <div className="flex-1">
              <h3 className="text-3xl font-bold">Appraisal Cycle Management</h3>
              <p className="text-lg">Design and manage performance evaluation periods for your organization</p>
            </div>
            <Button
              variant="secondary" // Use secondary variant
              onClick={() => setModalOpen(true)}
              className="bg-white text-green-500 hover:bg-green-100 flex items-center"
            >
              <Plus className="mr-2" />
              New Cycle
            </Button>
          </div>
        </div>

        {/* Filters */}
        <AppraisalPeriodFilters
          selectedType={selectedType}
          onTypeChange={setSelectedType}
          onAdvancedFilter={() => console.log('Advanced filters')}
        />

        {/* Appraisal Period List */}
        {loading ? (
          <div className="flex justify-center mt-4">
            <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-green-500"></div> {/* Custom loading spinner */}
          </div>
        ) : (
          <AppraisalPeriodList
            periods={periods}
            onView={handleViewPeriod} // View button will now trigger the handleViewPeriod function
            onEdit={(id) => console.log('Editing period:', id)}
            onDelete={(id) => console.log('Deleting period:', id)}
          />
        )}

        {/* Create Appraisal Modal */}
        <CreateAppraisalModal
          open={modalOpen}
          onClose={() => setModalOpen(false)}
          onSuccess={handleCreateSuccess}
        />

        {/* Show the AppraisalDetails component when a period is selected */}
        {viewPeriodId && <AppraisalDetails id={viewPeriodId} />}
      </div>
    </Screen>
  );
};
