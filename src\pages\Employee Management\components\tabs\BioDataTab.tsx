import { useEffect, useState } from "react";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { EmployeeBioDetails } from "../../types/EmployeeTypes";
import { updateOrCreateRecord } from "../../utils/Records";

import { BASE_URL } from "@/config";

interface BioDataTabProps {
  employeeNo: string;
  onSaveSuccess?: () => void;
}

export default function BioDataTab({ employeeNo, onSaveSuccess }: BioDataTabProps) {
  const token = useSelector((state: RootState) => state.auth.token);
  const [bioData, setBioData] = useState<EmployeeBioDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Preset enumerations
  const GENDER_OPTIONS = ["M", "F", "O"];
  const MARITAL_OPTIONS = ["Single", "Married", "Divorced", "Widowed", "Separated"];

  useEffect(() => {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }
    (async () => {
      setLoading(true);
      try {
        const headers = { Authorization: `Token ${token}` };
        const params = { employee_no: employeeNo };

        const res = await axios.get(`${BASE_URL}/users/employee-bio-details`, {
          headers,
          params,
        });

        if (Array.isArray(res.data) && res.data.length > 0) {
          setBioData(res.data[0]);
        } else {
          // No data found => create a new record in state
          setBioData({
            employee_no: employeeNo,
            first_name: "",
            last_name: "",
          });
        }
      } catch (err: any) {
        console.error("Error fetching Bio:", err);
        toast.error("Failed to fetch Bio data");
        // Fallback with empty fields so data can be posted
        setBioData({
          employee_no: employeeNo,
          first_name: "",
          last_name: "",
        });
      } finally {
        setLoading(false);
      }
    })();
  }, [token, employeeNo]);

  async function handleSave() {
    if (!bioData || !token) return;
    try {
      const updated = await updateOrCreateRecord<EmployeeBioDetails>(
        `${BASE_URL}/users/employee-bio-details`,
        token,
        { ...bioData, employee_no: employeeNo }
      );
      if (updated) {
        setBioData(updated);
        toast.success("Bio data saved");

        // Call the onSaveSuccess callback if provided
        if (onSaveSuccess) {
          onSaveSuccess();
        }
      }
    } catch (err) {
      console.error("Error saving Bio Data:", err);
      toast.error("Failed to save Bio data");
    }
  }

  if (loading) return <div className="p-4">Loading Bio Data...</div>;
  if (!bioData) return null;

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-2">Bio Data</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* FIRST NAME */}
        <div>
          <Label>First Name</Label>
          <Input
            value={bioData.first_name || ""}
            onChange={(e) =>
              setBioData((prev) =>
                prev ? { ...prev, first_name: e.target.value } : null
              )
            }
          />
        </div>
        {/* MIDDLE NAME */}
        <div>
          <Label>Middle Name</Label>
          <Input
            value={bioData.middle_name || ""}
            onChange={(e) =>
              setBioData((prev) =>
                prev ? { ...prev, middle_name: e.target.value } : null
              )
            }
          />
        </div>
        {/* LAST NAME */}
        <div>
          <Label>Last Name</Label>
          <Input
            value={bioData.last_name || ""}
            onChange={(e) =>
              setBioData((prev) =>
                prev ? { ...prev, last_name: e.target.value } : null
              )
            }
          />
        </div>
        {/* GENDER (SELECT) */}
        <div>
          <Label>Gender</Label>
          <select
            className="w-full border rounded px-2 py-1"
            value={bioData.gender || ""}
            onChange={(e) =>
              setBioData((prev) =>
                prev ? { ...prev, gender: e.target.value } : null
              )
            }
          >
            <option value="">Select...</option>
            {GENDER_OPTIONS.map((opt) => (
              <option key={opt} value={opt}>
                {opt}
              </option>
            ))}
          </select>
        </div>
        {/* MARITAL STATUS (SELECT) */}
        <div>
          <Label>Marital Status</Label>
          <select
            className="w-full border rounded px-2 py-1"
            value={bioData.marital_status || ""}
            onChange={(e) =>
              setBioData((prev) =>
                prev ? { ...prev, marital_status: e.target.value } : null
              )
            }
          >
            <option value="">Select...</option>
            {MARITAL_OPTIONS.map((opt) => (
              <option key={opt} value={opt}>
                {opt}
              </option>
            ))}
          </select>
        </div>
        {/* DATE OF BIRTH */}
        <div>
          <Label>Date of Birth</Label>
          <Input
            type="date"
            value={bioData.date_of_birth || ""}
            onChange={(e) =>
              setBioData((prev) =>
                prev ? { ...prev, date_of_birth: e.target.value } : null
              )
            }
          />
        </div>
        {/* PASSPORT NO */}
        <div>
          <Label>Passport Number</Label>
          <Input
            value={bioData.passport_number || ""}
            onChange={(e) =>
              setBioData((prev) =>
                prev ? { ...prev, passport_number: e.target.value } : null
              )
            }
          />
        </div>
        {/* ID NUMBER */}
        <div>
          <Label>ID Number</Label>
          <Input
            value={bioData.id_number || ""}
            onChange={(e) =>
              setBioData((prev) =>
                prev ? { ...prev, id_number: e.target.value } : null
              )
            }
          />
        </div>
        {/* DATE OF FIRST APPOINTMENT */}
        <div>
          <Label>Date of First Appointment</Label>
          <Input
            type="date"
            value={bioData.date_of_first_appointment || ""}
            onChange={(e) =>
              setBioData((prev) =>
                prev
                  ? { ...prev, date_of_first_appointment: e.target.value }
                  : null
              )
            }
          />
        </div>
      </div>
      <Button onClick={handleSave} className="mt-4">
        Save Bio
      </Button>
    </div>
  );
}
