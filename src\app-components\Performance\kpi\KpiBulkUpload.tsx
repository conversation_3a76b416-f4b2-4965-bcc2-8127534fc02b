"use client";
import React, { useState,useEffect } from "react";
import axios from "axios";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { BASE_URL } from "../../../config";
import * as XLSX from 'xlsx';

// Shadcn UI components
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { KPIWhat } from "./KpiWizardModal";
import { AssignmentTarget } from "./KpiWizardModal";

// Lucide-react icons
import {
  FileSpreadsheet,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Info,
  Search,
  X,
} from "lucide-react";
import { Input } from "@/components/ui/input";

// Types for bulk upload data
export interface KpiBulkData {
  what: string;
  total_marks: number;
  how1: string;
  how2?: string;
  how3?: string;
  how4?: string;
  how5?: string;
  [key: string]: any;
}

// Update the type definition for departments
interface Department {
  id: number;
  name: string;
  description?: string;
  dep_head?: string;
  dep_head_assistant?: string;
  dep_hr?: string;
  department_status_active: boolean;
  organisation?: number;
  parent_department?: number;
}

// Update the state declaration


interface KpiBulkUploadProps {
  open: boolean;
  onClose: () => void;
  employees: any[];
  jobs: any[];
  onSuccess?: (kpis: KPIWhat[], assignment: AssignmentTarget) => void;
  embedded?: boolean;
}

const KpiBulkUpload: React.FC<KpiBulkUploadProps> = ({
  open,
  onClose,
  employees,
  jobs,
  embedded,
  onSuccess,
}) => {
  console.log("Employees data received:", employees);
  const { token, user } = useSelector((state: RootState) => state.auth);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [assignmentType, setAssignmentType] = useState<"employee" | "job">("employee");
  const [targetIds, setTargetIds] = useState<string[]>([]);
  const [kpiData, setKpiData] = useState<KpiBulkData[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<{
    success: number;
    failed: number;
    errors: string[];
  }>({ success: 0, failed: 0, errors: [] });
  const [step, setStep] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState("all");
  const [departments, setDepartments] = useState<Department[]>([]);

  // Enhanced search function for employees
  const searchEmployees = (emp: any, searchTerm: string) => {
    if (!searchTerm) return true;

    const search = searchTerm.toLowerCase().trim();
    const deptName = getDepartmentName(emp.department_id).toLowerCase();

    return (
      emp.name.toLowerCase().includes(search) ||
      emp.employee_no.toLowerCase().includes(search) ||
      deptName.includes(search) ||
      `${emp.name} ${emp.employee_no}`.toLowerCase().includes(search)
    );
  };

  // Enhanced search function for jobs
  const searchJobs = (job: any, searchTerm: string) => {
    if (!searchTerm) return true;

    const search = searchTerm.toLowerCase().trim();

    return (
      job.title.toLowerCase().includes(search) ||
      job.code.toLowerCase().includes(search) ||
      `${job.title} ${job.code}`.toLowerCase().includes(search)
    );
  };


  useEffect(() => {
    const fetchDepartments = async () =>{
      try{
        const response = await axios.get(   `${BASE_URL}/users/departments`,
          { headers: { Authorization: `Token ${token}` } }
        )

        setDepartments(response.data);
        console.log("Departments data:", response.data);
      }catch(error){
        console.error("Error fetching departments:", error);
      }
    }
    fetchDepartments();
  },[token])

  const getDepartmentName = (departmentId: number) => {
    const department = departments.find(dept => dept.id === departmentId);
    return department ? department.name : "Unknown Department";
  };

  // Generate sample Excel template based on selection
  const generateTemplate = () => {
    // Create worksheet data
    const wsData = [
      ["KPI (What)", "Total Marks", "How 1", "How 2", "How 3", "How 4", "How 5"],
      ["Improve Customer Satisfaction", 25, "Reduce complaint rate to <5%", "Achieve 90% positive feedback", "Respond to queries within 24 hrs", "", ""],
      ["Increase Team Productivity", 30, "Complete projects on schedule", "Reduce rework by 15%", "Implement 2 process improvements", "", ""],
      ["Enhance Technical Skills", 20, "Complete required certifications", "Train junior team members", "", "", ""],
    ];

    // Create a workbook and add worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(wsData);

    // Set column widths
    const wscols = [
      { wch: 35 }, // KPI what
      { wch: 15 }, // Total Marks
      { wch: 35 }, // How 1
      { wch: 35 }, // How 2
      { wch: 35 }, // How 3
      { wch: 35 }, // How 4
      { wch: 35 }, // How 5
    ];
    ws['!cols'] = wscols;

    XLSX.utils.book_append_sheet(wb, ws, "KPI Template");
    
    // Create style explaining column requirements
    const infoWs = XLSX.utils.aoa_to_sheet([
      ["KPI Bulk Upload Instructions"],
      [""],
      ["Column", "Description", "Requirements"],
      ["KPI (What)", "The main key performance indicator description", "Required. Text field describing the KPI objective"],
      ["Total Marks", "The total marks allocated to this KPI", "Required. Number between 1-100"],
      ["How 1", "First performance metric for this KPI", "Required. Text field describing how the KPI will be measured"],
      ["How 2-5", "Additional performance metrics (optional)", "Optional. Additional ways the KPI will be measured"],
      [""],
      ["Notes:"],
      ["1. Each row represents one KPI with its performance metrics (hows)"],
      ["2. You must provide at least one 'How' for each KPI"],
      ["3. Leave cells blank for unused 'How' columns"],
      ["4. All KPIs will be assigned to selected employees or job positions"],
    ]);
    
    // Set column widths for info sheet
    const infoCols = [
      { wch: 15 },
      { wch: 40 },
      { wch: 50 },
    ];
    infoWs['!cols'] = infoCols;
    
    XLSX.utils.book_append_sheet(wb, infoWs, "Instructions");

    // Generate blob and download
    XLSX.writeFile(wb, "kpi_bulk_upload_template.xlsx");
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setUploadFile(e.target.files[0]);
      setStep(2);
      parseExcelFile(e.target.files[0]);
    }
  };

  // Parse Excel file content
  const parseExcelFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get first sheet
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json<any>(worksheet);
        
        // Get the actual column headers from the first row
        const headers = Object.keys(jsonData[0]);
        
        // Validate and clean data
        const validData = jsonData
          .filter(row => {
            // Find the "what" column (could be "KPI (What)" or "what")
            const whatColumn = headers.find(h => h.toLowerCase().includes('what') || h === 'what');
            return whatColumn && row[whatColumn];
          })
          .map(row => {
            // Find the appropriate header names
            const whatColumn = headers.find(h => h.toLowerCase().includes('what') || h === 'what');
            const marksColumn = headers.find(h => h.toLowerCase().includes('marks') || h === 'total_marks');
            
            // Find all "How" columns (case insensitive)
            const howColumns = headers.filter(h => 
              h.toLowerCase().includes('how') || 
              /^how\d+$/.test(h.toLowerCase())
            ).sort();
            
            // Standardize field names
            const item: KpiBulkData = {
              what: row[whatColumn || "KPI (What)"] || row.what || "",
              total_marks: Number(row[marksColumn || "Total Marks"] || row.total_marks || 0),
              how1: row[howColumns[0]] || "",
            };
            
            // Add optional how fields if present
            for (let i = 1; i < howColumns.length && i < 5; i++) {
              const value = row[howColumns[i]];
              if (value) {
                item[`how${i+1}`] = value;
              }
            }
            
            return item;
          });
        
        setKpiData(validData);
      } catch (error) {
        console.error("Error parsing Excel file:", error);
        setResults(prev => ({
          ...prev,
          errors: [...prev.errors, "Failed to parse Excel file. Please ensure it's in the correct format."]
        }));
      }
    };
    
    reader.readAsArrayBuffer(file);
  };

  // Process and upload the data
  const processUpload = async () => {
    if (kpiData.length === 0 || targetIds.length === 0) {
      setResults(prev => ({
        ...prev,
        errors: [...prev.errors, "Please select targets and ensure KPI data is loaded."]
      }));
      return;
    }
  
    setIsUploading(true);
    setProgress(0);
    setResults({ success: 0, failed: 0, errors: [] });
    setStep(3);
  
    let successCount = 0;
    let failureCount = 0;
    const errors: string[] = [];
  
    try {
      // Process each KPI
      for (let i = 0; i < kpiData.length; i++) {
        const kpi = kpiData[i];
        setProgress(Math.floor((i / kpiData.length) * 100));
        
        try {
          // 1. Create the KPI what
          const whatRes = await axios.post(
            `${BASE_URL}/hrm/kpi-whats`,
            {
              Kpi_type: "default",
              what: kpi.what,
              total_marks: kpi.total_marks,
              created_by: user?.employee_no,
            },
            { headers: { Authorization: `Token ${token}` } }
          );
          
          const kpiId = whatRes.data.id;
          
          // 2. Create the KPI hows
          const howPromises = [];
          for (let j = 1; j <= 5; j++) {
            const howContent = kpi[`how${j}`];
            if (howContent) {
              howPromises.push(
                axios.post(
                  `${BASE_URL}/hrm/kpi-hows`,
                  { 
                    how: howContent,
                    whats: kpiId,
                    created_by: user?.employee_no
                  },
                  { headers: { Authorization: `Token ${token}` } }
                )
              );
            }
          }
          
          await Promise.all(howPromises);
          
          // 3. Assign KPI to targets
          const assignmentPromises = targetIds.map(targetId => {
            if (assignmentType === "employee") {
              return axios.post(
                `${BASE_URL}/hrm/kpi-to-employee`,
                { employeeid: targetId, kpi: kpiId },
                { headers: { Authorization: `Token ${token}` } }
              );
            } else {
              const job = jobs.find(j => String(j.id) === targetId);
              if (job) {
                return axios.post(
                  `${BASE_URL}/hrm/kpi-to-jobpositions`,
                  { job_code: job.code, kpi: kpiId },
                  { headers: { Authorization: `Token ${token}` } }
                );
              }
              return Promise.resolve(); // Handle case where job is not found
            }
          });
          
          await Promise.all(assignmentPromises);
          
          successCount++;
        } catch (error) {
          failureCount++;
          if (axios.isAxiosError(error)) {
            errors.push(`Error with KPI "${kpi.what}": ${error.response?.data?.detail || error.message}`);
          } else {
            errors.push(`Error with KPI "${kpi.what}": Unknown error`);
          }
        }
      }
      
      setProgress(100);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        errors.push(`General error: ${error.response?.data?.detail || error.message}`);
      } else {
        errors.push("Unknown error occurred during upload");
      }
    } finally {
      setIsUploading(false);
      setResults({
        success: successCount,
        failed: failureCount,
        errors
      });
  
      if (embedded && onSuccess) {
        // Only call onSuccess if we have successful uploads and we're not currently uploading
        // This prevents double creation of KPIs (once here and once in KpiWizardModal)
        if (!isUploading && successCount > 0){
          const parsedKpis = kpiData.map(kpi => {
            const howArray: string[] = [kpi.how1, kpi.how2, kpi.how3, kpi.how4, kpi.how5]
              .filter((item): item is string => typeof item === 'string' && item.length > 0)
              return{
                what: kpi.what,
                total_marks: kpi.total_marks,
                hows: howArray
              }
          })
          onSuccess(parsedKpis, {
            type: assignmentType,
            ids: targetIds
          });
        }
        onClose(); // Close modal if embedded
      }
    }
  };
  

  return embedded ? (
    <div className="p-4">
      {/* Keep all internal UI elements */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          {[1, 2, 3].map((stepNum) => (
            <div
              key={stepNum}
              className={`flex items-center gap-2 ${step >= stepNum ? "text-primary" : "text-muted-foreground"}`}
            >
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${step > stepNum ? "bg-primary text-white" : ""} ${step === stepNum ? "border-2 border-primary" : "border border-muted"}`}
              >
                {step > stepNum ? <CheckCircle className="h-5 w-5" /> : stepNum}
              </div>
              <span className="text-sm font-medium">
                {stepNum === 1 ? "Upload" : stepNum === 2 ? "Assign" : "Results"}
              </span>
              {stepNum < 3 && <span className="mx-2 text-muted-foreground">→</span>}
            </div>
          ))}
        </div>
      </div>
  
      {/* Render Steps Content */}
      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="w-full">
          <TabsTrigger className="w-1/2" value="upload">Import KPIs</TabsTrigger>
          <TabsTrigger className="w-1/2" value="template">Download Template</TabsTrigger>
        </TabsList>
  
        <TabsContent value="upload" className="mt-4">
          {step === 1 && (
            <Card className="border-dashed border-2 hover:border-primary transition-colors">
              <CardContent className="flex flex-col items-center justify-center p-6">
                <input
                  type="file"
                  id="file-upload"
                  className="hidden"
                  accept=".xlsx,.xls"
                  onChange={handleFileChange}
                />
                <Upload className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-xl font-semibold mb-2">Upload KPI Excel File</h3>
                <p className="text-muted-foreground text-center mb-4">
                  Drag and drop your Excel file here or click to browse
                </p>
                <label htmlFor="file-upload">
                <Button 
                    className="bg-gradient-to-r from-primary to-secondary"
                    onClick={() => document.getElementById('file-upload')?.click()}
                    >
                Select Excel File
                </Button>
                </label>
                <div className="mt-4 flex items-center gap-2 text-sm text-muted-foreground">
                  <Info className="h-4 w-4" />
                  <span>Only Excel files (.xlsx, .xls) are supported</span>
                </div>
              </CardContent>
            </Card>
          )}
  
          {step === 2 && (
            <div>
              <Card className="mb-4">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <FileSpreadsheet className="h-5 w-5 text-primary" />
                    <span className="font-medium">{uploadFile?.name} ({kpiData.length} KPIs found)</span>
                  </div>
  
                  {/* Assignment Type Selection */}
                  <div className="grid gap-4 mb-4">
                    <div>
                      <Label htmlFor="assignment-type">Assignment Type</Label>
                      <Select
                        value={assignmentType}
                        onValueChange={(val: "employee" | "job") => {
                          setAssignmentType(val);
                          setTargetIds([]);
                        }}
                      >
                        <SelectTrigger className="w-full mt-1">
                          <SelectValue placeholder="Select assignment type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="employee">Employees</SelectItem>
                          <SelectItem value="job">Job Positions</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {/* Search and Filter Section */}
                    <div className="space-y-3 mt-1 mb-4">
                      <div className="flex gap-2">
                        <div className="relative flex-1">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="text"
                            placeholder={`Search ${assignmentType === "employee" ? "employees by name, employee number, or department" : "job positions by title or code"}`}
                            className="pl-10 pr-10 h-10"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                          {searchTerm && (
                            <button
                              type="button"
                              onClick={() => setSearchTerm("")}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                        {/* Department Filter */}
                        {assignmentType === "employee" && departments.length > 0 && (
                          <Select
                            onValueChange={val => setDepartmentFilter(val)}
                            defaultValue="all"
                          >
                            <SelectTrigger className="w-[200px]">
                              <SelectValue placeholder="Filter by Department" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Departments</SelectItem>
                              {departments
                                .filter(dept => dept.department_status_active)
                                .sort((a, b) => a.name.localeCompare(b.name))
                                .map(dept =>(
                                  <SelectItem key={dept.id} value={dept.id.toString()}>
                                    {dept.name}
                                  </SelectItem>
                                )
                                )}
                            </SelectContent>
                          </Select>
                        )}
                      </div>

                      {/* Search Results Counter */}
                      {(searchTerm || departmentFilter !== "all") && (
                        <div className="text-xs text-muted-foreground">
                          {(() => {
                            const filteredCount = assignmentType === "employee" ?
                              employees.filter(emp => {
                                const departmentMatches = departmentFilter === "all" ||
                                  emp.department_id.toString() === departmentFilter;
                                const searchMatches = searchEmployees(emp, searchTerm);
                                return departmentMatches && searchMatches;
                              }).length :
                              jobs.filter(job => searchJobs(job, searchTerm)).length;

                            const totalCount = assignmentType === "employee" ? employees.length : jobs.length;

                            return `Showing ${filteredCount} of ${totalCount} ${assignmentType === "employee" ? "employees" : "job positions"}`;
                          })()}
                        </div>
                      )}
                    </div>
  
                    {/* Target Selection */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <Label htmlFor="targets">
                          Select {assignmentType === "employee" ? "Employees" : "Job Positions"}
                        </Label>
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const filteredItems = assignmentType === "employee" ?
                                employees
                                  .filter(emp => {
                                    const departmentMatches = departmentFilter === "all" ||
                                      emp.department_id.toString() === departmentFilter;
                                    const searchMatches = searchEmployees(emp, searchTerm);
                                    return departmentMatches && searchMatches;
                                  })
                                  .map(emp => emp.id.toString()) :
                                jobs
                                  .filter(job => searchJobs(job, searchTerm))
                                  .map(job => job.id.toString());
                              setTargetIds(filteredItems);
                            }}
                          >
                            Select All Filtered
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setTargetIds([])}
                          >
                            Clear All
                          </Button>
                        </div>
                      </div>
                      <select
                        multiple
                        className="w-full h-60 mt-1 rounded border p-2 bg-background"
                        value={targetIds}
                        onChange={(e) => {
                          const selected = Array.from(e.target.selectedOptions).map(opt => opt.value);
                          setTargetIds(selected);
                        }}
                      >
                         {(() => {
                           const filteredItems = assignmentType === "employee" ?
                             employees
                               .filter(emp => {
                                 // Handle department filtering based on department_id
                                 const departmentMatches = departmentFilter === "all" ||
                                   emp.department_id.toString() === departmentFilter;

                                 // Handle enhanced search term filtering
                                 const searchMatches = searchEmployees(emp, searchTerm);

                                 return departmentMatches && searchMatches;
                               })
                               .sort((a, b) => a.name.localeCompare(b.name))
                               .map((item) => {
                                 // Get department name for display
                                 const deptName = getDepartmentName(item.department_id);

                                 return (
                                   <option key={item.id} value={item.id}>
                                     {`${item.name} - ${deptName} (${item.employee_no})`}
                                   </option>
                                 );
                               }) :
                             // Jobs mapping with enhanced search
                             jobs
                               .filter(job => searchJobs(job, searchTerm))
                               .sort((a, b) => a.title.localeCompare(b.title))
                               .map((item) => (
                                 <option key={item.id} value={item.id}>
                                   {`${item.title} (${item.code})`}
                                 </option>
                               ));

                           if (filteredItems.length === 0) {
                             return (
                               <option disabled className="text-muted-foreground italic">
                                 No {assignmentType === "employee" ? "employees" : "job positions"} found matching your search
                               </option>
                             );
                           }

                           return filteredItems;
                         })()}
                          
                        </select>
                        <div className="flex items-center justify-between mt-2">
                          <p className="text-xs text-muted-foreground">
                            {targetIds.length} {assignmentType === "employee" ? "employees" : "job positions"} selected
                            {assignmentType === "employee" && (
                              <span className="ml-2 text-blue-600">
                                • Hold Ctrl/Cmd to select multiple
                              </span>
                            )}
                          </p>
                          {targetIds.length > 0 && (
                            <p className="text-xs text-green-600 font-medium">
                              ✓ Ready to assign KPIs
                            </p>
                          )}
                        </div>
                      </div>
                  </div>
                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setStep(1);
                        setUploadFile(null);
                        setKpiData([]);
                      }}
                    >
                      Back
                    </Button>
                    <Button
                      className="bg-gradient-to-r from-primary to-secondary"
                      onClick={processUpload}
                      disabled={targetIds.length === 0}
                    >
                      Process Upload
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
  
          {step === 3 && (
            <Card>
              <CardContent className="p-6">
                {isUploading ? (
                  <div className="flex flex-col items-center py-4">
                    <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Processing Upload</h3>
                    <p className="text-muted-foreground mb-4">
                      Please wait while we process your KPIs...
                    </p>
                    <Progress value={progress} className="w-full h-2 mb-2" />
                    <p className="text-sm text-muted-foreground">
                      {progress}% complete ({Math.floor((progress / 100) * kpiData.length)} of {kpiData.length} KPIs)
                    </p>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center gap-2 mb-4">
                      {results.success > 0 && results.failed === 0 ? (
                        <CheckCircle className="h-8 w-8 text-green-500" />
                      ) : results.success === 0 ? (
                        <XCircle className="h-8 w-8 text-red-500" />
                      ) : (
                        <AlertTriangle className="h-8 w-8 text-amber-500" />
                      )}
                      <h3 className="text-xl font-semibold">Upload Results</h3>
                    </div>
  
                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                        <span className="text-3xl font-bold text-green-500">{results.success}</span>
                        <span className="text-sm text-muted-foreground">Successful KPIs</span>
                      </div>
                      <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                        <span className="text-3xl font-bold text-red-500">{results.failed}</span>
                        <span className="text-sm text-muted-foreground">Failed KPIs</span>
                      </div>
                    </div>
  
                    {results.errors.length > 0 && (
                      <div className="mb-6">
                        <h4 className="text-sm font-semibold mb-2 flex items-center gap-1">
                          <AlertTriangle className="h-4 w-4 text-amber-500" />
                          Error Details
                        </h4>
                        <div className="bg-muted rounded-lg p-4 max-h-40 overflow-y-auto">
                          <ul className="text-sm space-y-1">
                            {results.errors.map((error, idx) => (
                              <li key={idx} className="text-red-500">• {error}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}
  
                    <div className="flex justify-between">
                      <Button variant="outline" onClick={() => setStep(1)}>
                        Upload Another File
                      </Button>
                      <Button onClick={onClose}>Done</Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>
  
        <TabsContent value="template" className="mt-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center py-8">
                <FileSpreadsheet className="h-16 w-16 text-primary mb-4" />
                <h3 className="text-xl font-semibold mb-2">Download Template</h3>
                <p className="text-muted-foreground text-center mb-6 max-w-md">
                  Download our Excel template to ensure your KPI data is properly formatted for bulk upload
                </p>
                <Button onClick={generateTemplate} className="bg-gradient-to-r from-primary to-secondary">
                  <Download className="mr-2 h-4 w-4" />
                  Download Excel Template
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  ) : (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl w-full p-6 rounded-3xl bg-background overflow-auto max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <FileSpreadsheet className="h-6 w-6 text-primary" />
            Bulk KPI Upload
          </DialogTitle>
          <DialogDescription>
            Import multiple KPIs and metrics from an Excel file and assign them to employees or job positions
          </DialogDescription>
        </DialogHeader>
  
        <div className="mt-4">
          {/* Render steps and content here */}
        </div>
      </DialogContent>
    </Dialog>
  );
  
};

export default KpiBulkUpload;


