import React, { createContext, useContext, useState } from 'react';

interface RefreshContextType {
  refreshTriggers: Record<string, number>;
  triggerRefresh: (tabName: string) => void;
}

const RefreshContext = createContext<RefreshContextType | undefined>(undefined);

interface RefreshProviderProps {
  children: (context: RefreshContextType) => React.ReactNode;
}

export function RefreshProvider({ children }: RefreshProviderProps) {
  const [refreshTriggers, setRefreshTriggers] = useState<Record<string, number>>({
    'Bio Data': 0,
    'Contact Info': 0,
    'Contract Info': 0,
    'Education': 0,
    'Important Dates': 0,
    'Job Info': 0,
    'Payment': 0,
    'Next of Kin': 0,
    'Guardians': 0,
  });

  const triggerRefresh = (tabName: string) => {
    setRefreshTriggers(prev => ({
      ...prev,
      [tabName]: (prev[tabName] || 0) + 1
    }));
  };

  const contextValue = { refreshTriggers, triggerRefresh };

  return (
    <RefreshContext.Provider value={contextValue}>
      {children(contextValue)}
    </RefreshContext.Provider>
  );
}

export function useRefresh() {
  const context = useContext(RefreshContext);
  if (context === undefined) {
    throw new Error('useRefresh must be used within a RefreshProvider');
  }
  return context;
}
