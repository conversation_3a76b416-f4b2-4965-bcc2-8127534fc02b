"use client";

import React, { useEffect, useState } from "react";
import { X, MapPin, Mail, Phone } from "lucide-react";
import { useSelector } from "react-redux";
import axios from "axios";
import { RootState } from "../../../redux/store/index";

// New Dialog components from your Radix-based dialog
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from "@/components/ui/dialog";

import { Button } from "@/components/ui/button";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Alert } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { InfoChip } from "./InfoChip";

interface ViewEmployeeModalProps {
  open: boolean;
  onClose: () => void;
  employeeNo: string;
}

import { BASE_URL } from "@/config";

interface EmployeeData {
  bio: any;
  contact: any;
  contractInfo: any;
  importantDates: any;
  jobInfo: any;
  paymentInfo: any;
  groups: any;
  permissions: any;
  role: any;
  warningList: any[];
}

const initialEmployeeData: EmployeeData = {
  bio: null,
  contact: null,
  contractInfo: null,
  importantDates: null,
  jobInfo: null,
  paymentInfo: null,
  groups: null,
  permissions: null,
  role: null,
  warningList: [],
};

const ViewEmployeeModal: React.FC<ViewEmployeeModalProps> = ({
  open,
  onClose,
  employeeNo,
}) => {
  const token = useSelector((state: RootState) => state.auth.token);
  const [tabValue, setTabValue] = useState<number>(0);
  const [employeeData, setEmployeeData] =
    useState<EmployeeData>(initialEmployeeData);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    variant: "success" | "error" | "info" | "warning";
  }>({ open: false, message: "", variant: "success" });

  useEffect(() => {
    if (open) {
      fetchEmployeeData();
    } else {
      // Reset state when modal is closed
      setEmployeeData(initialEmployeeData);
    }
  }, [open, employeeNo]);

  const fetchEmployeeData = async () => {
    setIsLoading(true);
    const endpoints = [
      { key: "bio", url: `${BASE_URL}/users/employee-bio-details` },
      { key: "contact", url: `${BASE_URL}/users/employee-contact-info-details` },
      { key: "contractInfo", url: `${BASE_URL}/users/employee-contract-info-details` },
      { key: "importantDates", url: `${BASE_URL}/users/employee-important-dates-details` },
      { key: "jobInfo", url: `${BASE_URL}/users/employee-job-info-details` },
      { key: "paymentInfo", url: `${BASE_URL}/users/employee-payment-info-details` },
      { key: "groups", url: `${BASE_URL}/users/employee-group-details` },
      { key: "permissions", url: `${BASE_URL}/users/employee-permissions` },
      { key: "role", url: `${BASE_URL}/users/employee-roles` },
      { key: "warningList", url: `${BASE_URL}/users/employee-warnings` },
    ];

    try {
      const promises = endpoints.map((endpoint) =>
        axios.get(endpoint.url, {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        })
      );
      const results = await Promise.allSettled(promises);
      const newData: Partial<EmployeeData> = {};

      results.forEach((result, index) => {
        const key = endpoints[index].key as keyof EmployeeData;
        if (result.status === "fulfilled") {
          // For warningList, store the full array; otherwise take the first record if available.
          if (key === "warningList") {
            newData[key] = result.value.data;
          } else {
            newData[key] =
              result.value.data.length > 0 ? result.value.data[0] : null;
          }
        } else {
          newData[key] = key === "warningList" ? [] : null;
        }
      });
      setEmployeeData((prev) => ({ ...prev, ...newData }));
    } catch (error) {
      console.error("Error fetching employee data:", error);
      setSnackbar({
        open: true,
        message: "Failed to fetch employee data.",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = (newValue: string) => {
    setTabValue(Number(newValue));
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-3xl">
        {/* Hero Section */}
        <div className="relative bg-primary h-44" />

        {/* Employee Avatar & Basic Info */}
        <div className="flex justify-center -mt-16">
          <div className="relative">
            <Avatar className="w-24 h-24 border-4 border-white">
              <img
                src="https://images.unsplash.com/photo-1531727991582-cfd25ce79613"
                alt="Employee Avatar"
                className="w-full h-full object-cover rounded-full"
              />
            </Avatar>
            <div className="absolute bottom-0 right-0 bg-secondary text-white rounded-full px-2 text-xs">
              {employeeNo}
            </div>
          </div>
        </div>

        <DialogHeader className="text-center mt-2">
          <DialogTitle>
            {employeeData.bio
              ? `${employeeData.bio.first_name} ${employeeData.bio.last_name}`
              : "Employee"}
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            {employeeData.jobInfo?.job_title || "Employee Position"}
          </p>
        </DialogHeader>

        {/* Basic Info Chips */}
        <div className="flex flex-wrap justify-center gap-2 mb-4">
          <InfoChip
            icon={<MapPin className="w-4 h-4" />}
            label={employeeData.contact?.city || "No City"}
          />
          <InfoChip
            icon={<Mail className="w-4 h-4" />}
            label={employeeData.contact?.company_email || "No Email"}
          />
          <InfoChip
            icon={<Phone className="w-4 h-4" />}
            label={employeeData.contact?.home_phone_number || "No Phone"}
          />
        </div>

        {/* Tabs for all sections */}
        {isLoading ? (
          <div className="p-4 text-center">Loading employee data...</div>
        ) : (
          <Tabs
            value={tabValue.toString()}
            onValueChange={handleTabChange}
            className="border-b"
          >
            <TabsList className="w-full justify-center">
              <TabsTrigger value="0">Bio</TabsTrigger>
              <TabsTrigger value="1">Contact</TabsTrigger>
              <TabsTrigger value="2">Contract</TabsTrigger>
              <TabsTrigger value="3">Payment</TabsTrigger>
              <TabsTrigger value="4">Dates</TabsTrigger>
              <TabsTrigger value="5">Job</TabsTrigger>
              <TabsTrigger value="6">Groups</TabsTrigger>
              <TabsTrigger value="7">Permissions</TabsTrigger>
              <TabsTrigger value="8">Role</TabsTrigger>
              <TabsTrigger value="9">Disciplinary</TabsTrigger>
            </TabsList>

            {/* TAB 0: Bio Details */}
            <TabsContent value="0">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">Bio Details</h3>
                {employeeData.bio ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex flex-col gap-1">
                      <Label>First Name</Label>
                      <Input value={employeeData.bio.first_name} disabled />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Middle Name</Label>
                      <Input
                        value={employeeData.bio.middle_name || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Last Name</Label>
                      <Input value={employeeData.bio.last_name} disabled />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Gender</Label>
                      <Input value={employeeData.bio.gender || ""} disabled />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Marital Status</Label>
                      <Input
                        value={employeeData.bio.marital_status || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Date of Birth</Label>
                      <Input
                        value={employeeData.bio.date_of_birth || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Passport Number</Label>
                      <Input
                        value={employeeData.bio.passport_number || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>ID Number</Label>
                      <Input
                        value={employeeData.bio.id_number || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Date of First Appointment</Label>
                      <Input
                        type="date"
                        value={
                          employeeData.bio.date_of_first_appointment || ""
                        }
                        disabled
                      />
                    </div>
                  </div>
                ) : (
                  <p>No Bio record found.</p>
                )}
              </div>
            </TabsContent>

            {/* TAB 1: Contact Info */}
            <TabsContent value="1">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">Contact Info</h3>
                {employeeData.contact ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col gap-1">
                      <Label>Postal Address</Label>
                      <Input
                        value={employeeData.contact.postal_address || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Home Phone</Label>
                      <Input
                        value={employeeData.contact.home_phone_number || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Residential Address</Label>
                      <Input
                        value={employeeData.contact.residential_address || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>City</Label>
                      <Input
                        value={employeeData.contact.city || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>County</Label>
                      <Input
                        value={employeeData.contact.county || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Company Email</Label>
                      <Input
                        value={employeeData.contact.company_email || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Country</Label>
                      <Input
                        value={employeeData.contact.country || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Personal Email</Label>
                      <Input
                        value={employeeData.contact.personal_email || ""}
                        disabled
                      />
                    </div>
                  </div>
                ) : (
                  <p>No Contact record found.</p>
                )}
              </div>
            </TabsContent>

            {/* TAB 2: Contract Info */}
            <TabsContent value="2">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">Contract Info</h3>
                {employeeData.contractInfo ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col gap-1">
                      <Label>Contract Type</Label>
                      <Input
                        value={employeeData.contractInfo.contract_type || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Contract Start Date</Label>
                      <Input
                        value={employeeData.contractInfo.contract_start_date || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Current Contract End</Label>
                      <Input
                        value={employeeData.contractInfo.current_contract_end || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>End of Probation Date</Label>
                      <Input
                        value={employeeData.contractInfo.end_of_probation_date || ""}
                        disabled
                      />
                    </div>
                  </div>
                ) : (
                  <p>No Contract record found.</p>
                )}
              </div>
            </TabsContent>

            {/* TAB 3: Payment Info */}
            <TabsContent value="3">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">Payment Info</h3>
                {employeeData.paymentInfo ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex flex-col gap-1">
                      <Label>Pension Scheme Join</Label>
                      <Input
                        value={employeeData.paymentInfo.pension_scheme_join || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Salary</Label>
                      <Input
                        value={
                          employeeData.paymentInfo.salary?.toString() || ""
                        }
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Bonus</Label>
                      <Input
                        value={
                          employeeData.paymentInfo.bonus?.toString() || ""
                        }
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Bank Name</Label>
                      <Input
                        value={employeeData.paymentInfo.bank_name || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Account Number</Label>
                      <Input
                        value={employeeData.paymentInfo.account_number || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Payment Frequency</Label>
                      <Input
                        value={employeeData.paymentInfo.payment_frequency || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>KRA Pin</Label>
                      <Input
                        value={employeeData.paymentInfo.KRA_pin || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>NHIF Number</Label>
                      <Input
                        value={employeeData.paymentInfo.NHIF_SHIF_number || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>HELB Number</Label>
                      <Input
                        value={employeeData.paymentInfo.HELB_number || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Tax Status</Label>
                      <Input
                        value={
                          employeeData.paymentInfo.tax_status ? "Yes" : "No"
                        }
                        disabled
                      />
                    </div>
                  </div>
                ) : (
                  <p>No Payment record found.</p>
                )}
              </div>
            </TabsContent>

            {/* TAB 4: Important Dates */}
            <TabsContent value="4">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">Important Dates</h3>
                {employeeData.importantDates ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col gap-1">
                      <Label>Date of Current Appointment</Label>
                      <Input
                        type="date"
                        value={
                          employeeData.importantDates.date_of_current_appointment ||
                          ""
                        }
                        disabled
                      />
                      <p className="text-xs text-muted-foreground">
                        The date when the employee was appointed to their current position
                      </p>
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Date of Leaving</Label>
                      <Input
                        type="date"
                        value={employeeData.importantDates.date_of_leaveing || ""}
                        disabled
                      />
                      <p className="text-xs text-muted-foreground">
                        The date when the employee left or will leave the organization (optional)
                      </p>
                    </div>
                  </div>
                ) : (
                  <p>No Important Dates record found.</p>
                )}
              </div>
            </TabsContent>

            {/* TAB 5: Job Info */}
            <TabsContent value="5">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">Job Info</h3>
                {employeeData.jobInfo ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col gap-1">
                      <Label>Category</Label>
                      <Input
                        value={employeeData.jobInfo.category || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Directorate</Label>
                      <Input
                        value={employeeData.jobInfo.directorate || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Job Title Code</Label>
                      <Input
                        value={employeeData.jobInfo.job_title_code || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Job Title</Label>
                      <Input
                        value={employeeData.jobInfo.job_title || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Teams Code</Label>
                      <Input
                        value={employeeData.jobInfo.teams_code || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Department ID</Label>
                      <Input
                        value={employeeData.jobInfo.department?.toString() || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Work Location</Label>
                      <Input
                        value={employeeData.jobInfo.work_location || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Business Unit</Label>
                      <Input
                        value={employeeData.jobInfo.business_unit || ""}
                        disabled
                      />
                    </div>
                  </div>
                ) : (
                  <p>No Job Info found.</p>
                )}
              </div>
            </TabsContent>

            {/* TAB 6: Groups */}
            <TabsContent value="6">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">Groups</h3>
                {employeeData.groups ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col gap-1">
                      <Label>Group Name</Label>
                      <Input
                        value={employeeData.groups.name || ""}
                        disabled
                      />
                    </div>
                  </div>
                ) : (
                  <p>No Group record found.</p>
                )}
              </div>
            </TabsContent>

            {/* TAB 7: Permissions */}
            <TabsContent value="7">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">Permissions</h3>
                {employeeData.permissions ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col gap-1">
                      <Label>Permission Name</Label>
                      <Input
                        value={employeeData.permissions.permission_name || ""}
                        disabled
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <Label>Permission Description</Label>
                      <Input
                        value={employeeData.permissions.permission_description || ""}
                        disabled
                      />
                    </div>
                  </div>
                ) : (
                  <p>No Permissions record found.</p>
                )}
              </div>
            </TabsContent>

            {/* TAB 8: Role */}
            <TabsContent value="8">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">Role</h3>
                {employeeData.role ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col gap-1">
                      <Label>Role ID</Label>
                      <Input
                        value={employeeData.role.role?.toString() || ""}
                        disabled
                      />
                    </div>
                  </div>
                ) : (
                  <p>No Role record found.</p>
                )}
              </div>
            </TabsContent>

            {/* TAB 9: Disciplinary & Warnings */}
            <TabsContent value="9">
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">
                  Disciplinary & Warnings
                </h3>
                <Separator className="mb-2" />
                {employeeData.warningList &&
                employeeData.warningList.length > 0 ? (
                  employeeData.warningList.map((warn: any) => (
                    <div key={warn.id} className="border p-4 mb-2 rounded">
                      <p className="font-semibold">Warning #{warn.id}</p>
                      <p>Date: {warn.date}</p>
                      <p>Reason: {warn.reason}</p>
                      <Badge variant="outline" className="mt-1">
                        {warn.acknowledged ? "Acknowledged" : "Unacknowledged"}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <p>No warnings issued.</p>
                )}
              </div>
            </TabsContent>
          </Tabs>
        )}

        <DialogFooter className="mt-4">
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>

        {snackbar.open && (
          <Alert variant="destructive" onClick={handleCloseSnackbar}>
            {snackbar.message}
          </Alert>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ViewEmployeeModal;
