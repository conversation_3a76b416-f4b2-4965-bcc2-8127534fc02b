import * as XLSX from 'xlsx';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import dayjs from 'dayjs';

// Set up pdfMake fonts
pdfMake.vfs = pdfFonts.vfs;

export interface StaffOnLeaveData {
  employee_no: string;
  employee_name: string;
  leave_type: string;
  start_date: string;
  end_date: string;
  days_applied: number;
  status: string;
}

export const exportStaffOnLeaveToPDF = (
  staffData: StaffOnLeaveData[],
  date: string,
  organizationName: string = 'Organization'
) => {
  const formattedDate = dayjs(date).format('MMMM DD, YYYY');
  
  const docDefinition = {
    content: [
      // Header
      {
        text: `${organizationName}`,
        fontSize: 18,
        alignment: 'center' as const,
        margin: [0, 0, 0, 10] as [number, number, number, number]
      },
      {
        text: `Staff on Leave - ${formattedDate}`,
        fontSize: 14,
        alignment: 'center' as const,
        margin: [0, 0, 0, 20] as [number, number, number, number]
      },
      
      // Summary
      {
        text: `Total Staff on Leave: ${staffData.length}`,
        fontSize: 12,
        margin: [0, 0, 0, 15] as [number, number, number, number]
      },
      
      // Table
      {
        table: {
          headerRows: 1,
          widths: ['15%', '25%', '20%', '15%', '15%', '10%'],
          body: [
            // Header row
            [
              { text: 'Employee No', fontSize: 10, color: 'white', alignment: 'center' as const },
              { text: 'Employee Name', fontSize: 10, color: 'white', alignment: 'center' as const },
              { text: 'Leave Type', fontSize: 10, color: 'white', alignment: 'center' as const },
              { text: 'Start Date', fontSize: 10, color: 'white', alignment: 'center' as const },
              { text: 'End Date', fontSize: 10, color: 'white', alignment: 'center' as const },
              { text: 'Days', fontSize: 10, color: 'white', alignment: 'center' as const }
            ],
            // Data rows
            ...staffData.map(staff => [
              { text: staff.employee_no, fontSize: 9 },
              { text: staff.employee_name, fontSize: 9 },
              { text: staff.leave_type || 'N/A', fontSize: 9 },
              { text: dayjs(staff.start_date).format('MMM DD, YYYY'), fontSize: 9 },
              { text: dayjs(staff.end_date).format('MMM DD, YYYY'), fontSize: 9 },
              { text: staff.days_applied.toString(), fontSize: 9, alignment: 'center' as const }
            ])
          ]
        },
        layout: {
          fillColor: function (rowIndex: number) {
            return rowIndex === 0 ? '#3B82F6' : (rowIndex % 2 === 0 ? '#F8FAFC' : null);
          },
          hLineWidth: function () { return 0.5; },
          vLineWidth: function () { return 0.5; },
          hLineColor: function () { return '#E2E8F0'; },
          vLineColor: function () { return '#E2E8F0'; }
        }
      },
      
      // Footer
      {
        text: `Generated on ${dayjs().format('MMMM DD, YYYY [at] HH:mm')}`,
        fontSize: 8,
        alignment: 'center' as const,
        margin: [0, 30, 0, 0] as [number, number, number, number]
      }
    ],
    
    defaultStyle: {
      fontSize: 10
    },
    
    pageMargins: [40, 60, 40, 60] as [number, number, number, number]
  };

  const fileName = `Staff_on_Leave_${dayjs(date).format('YYYY-MM-DD')}.pdf`;
  pdfMake.createPdf(docDefinition).download(fileName);
};

export const exportStaffOnLeaveToExcel = (
  staffData: StaffOnLeaveData[],
  date: string,
  organizationName: string = 'Organization'
) => {
  const formattedDate = dayjs(date).format('MMMM DD, YYYY');
  
  // Prepare data for Excel
  const excelData = [
    // Title rows
    [`${organizationName}`],
    [`Staff on Leave - ${formattedDate}`],
    [], 
    [`Total Staff on Leave: ${staffData.length}`],
    [], 
    
    // Header row
    ['Employee No', 'Employee Name', 'Leave Type', 'Start Date', 'End Date', 'Days Applied', 'Status'],
    
    // Data rows
    ...staffData.map(staff => [
      staff.employee_no,
      staff.employee_name,
      staff.leave_type || 'N/A',
      dayjs(staff.start_date).format('MMM DD, YYYY'),
      dayjs(staff.end_date).format('MMM DD, YYYY'),
      staff.days_applied,
      staff.status
    ]),
    
    [], 
    [`Generated on ${dayjs().format('MMMM DD, YYYY [at] HH:mm')}`]
  ];

  // Create workbook and worksheet
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(excelData);

  // Set column widths
  const colWidths = [
    { wch: 15 }, // Employee No
    { wch: 25 }, // Employee Name
    { wch: 20 }, // Leave Type
    { wch: 15 }, // Start Date
    { wch: 15 }, // End Date
    { wch: 12 }, // Days Applied
    { wch: 15 }  // Status
  ];
  ws['!cols'] = colWidths;

  // Style the header row (row 6, 0-indexed as row 5)
  const headerRowIndex = 5;
  const headerCells = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
  headerCells.forEach(col => {
    const cellRef = `${col}${headerRowIndex + 1}`;
    if (ws[cellRef]) {
      ws[cellRef].s = {
        font: { bold: true, color: { rgb: 'FFFFFF' } },
        fill: { fgColor: { rgb: '3B82F6' } },
        alignment: { horizontal: 'center' }
      };
    }
  });

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(wb, ws, 'Staff on Leave');

  // Generate filename and download
  const fileName = `Staff_on_Leave_${dayjs(date).format('YYYY-MM-DD')}.xlsx`;
  XLSX.writeFile(wb, fileName);
};

// Helper function to transform leave application data to export format
export const transformLeaveDataForExport = (
  leaveApplications: any[],
  employeeMap: Record<string, string>
): StaffOnLeaveData[] => {
  return leaveApplications.map(app => ({
    employee_no: app.employee_no,
    employee_name: employeeMap[app.employee_no] || `${app.employee_no}`,
    leave_type: app.leave_type_name || 'N/A',
    start_date: app.start_date,
    end_date: app.end_date,
    days_applied: app.no_of_days_applied || 0,
    status: app.leave_status || 'N/A'
  }));
};