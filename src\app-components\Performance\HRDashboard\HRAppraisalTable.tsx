// src/components/HRAppraisalTable.tsx

import React from 'react';
import { AppraisalViewModel, Employee, Department } from './types';

interface HRAppraisalTableProps {
  currentRecords: AppraisalViewModel[];
  employees: Employee[];
  departments: Department[];
  handleViewDetails: (recordId: number, employeeId: string, periodId: number) => void;
}

const HRAppraisalTable: React.FC<HRAppraisalTableProps> = ({
  currentRecords,
  employees,
  departments,
  handleViewDetails,
}) => {
  return (
    <table className="w-full table-auto">
      <thead className="bg-gray-200">
        <tr>
          <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">Employee Name</th>
          <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">Employee No</th>
          <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">Department</th>
          <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">Status</th>
          <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">Actions</th>
        </tr>
      </thead>
      <tbody>
        {currentRecords.map((record) => {
          const employee = employees.find((emp) => emp.employee_no === record.employeeid);
          const department = departments.find((dept) => dept.id === employee?.department_id);

          return (
            <tr key={record.id} className="hover:bg-gray-50 border-b border-gray-200">
              <td className="px-4 py-3">
                {employee ? `${employee.first_name} ${employee.last_name}` : 'Loading...'}
              </td>
              <td className="px-4 py-3">{record.employeeid || 'N/A'}</td>
              <td className="px-4 py-3">
                {department ? department.name : 'Loading...'}
              </td>
              <td className="px-4 py-3">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${record.status === 'HR Appraised' && record.supervisor_acceptance
                      ? 'bg-green-100 text-green-800'
                      : record.status === 'HR Appraised' && !record.supervisor_acceptance
                        ? 'bg-red-100 text-red-800'
                        : record.status === 'Supervisor Appraised'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}
                >
                  {record.status}
                </span>
              </td>
              <td className="px-4 py-3">
                <button
                  onClick={() => handleViewDetails(record.id, record.employeeid, record.period_id || 0)}
                  className="text-green-600 hover:text-green-800 text-sm font-medium"
                  disabled={record.status === 'Draft'}
                >
                  {record.status === 'Draft' ? 'Pending Employee Submission' : 'View Details'}
                </button>
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
};

export default HRAppraisalTable;
