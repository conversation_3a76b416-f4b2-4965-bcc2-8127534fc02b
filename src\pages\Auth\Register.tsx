"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { registerUser } from "@/redux/features/auth/authSlice";
import { useToast } from "@/hooks/use-toast";
import { ToastAction } from "@/components/ui/toast";

const Register: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [organisationId, setOrganisationId] = useState(1);
  const [departmentId, setDepartmentId] = useState(1);
  const [groupId, setGroupId] = useState(1);
  const [submitted, setSubmitted] = useState(false);

  const { user, error } = useSelector((state: RootState) => state.auth);

  // When registration is successful, navigate away and show a success toast with an action.
  useEffect(() => {
    if (user) {
      navigate("/");
      toast({
        title: "Registration Successful",
        description: "You have registered successfully.",
        action: (
          <ToastAction altText="Go to dashboard" onClick={() => navigate("/")}>
            Dashboard
          </ToastAction>
        ),
      });
    }
  }, [user, navigate, toast]);

  const handleRegister = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);

    if (!email || !password) {
      toast({
        title: "Missing Fields",
        description: "Please fill in email and password.",
        variant: "destructive",
      });
      return;
    }

    dispatch(
      registerUser({
        email,
        password,
        organisation_id: organisationId,
        department_id: departmentId,
        group_id: groupId,
      })
    );
  };

  // Show error toast if registration fails.
  useEffect(() => {
    if (submitted && error) {
      toast({
        title: "Registration Error",
        description: error,
        variant: "destructive",
      });
    }
  }, [error, submitted, toast]);

  return (
    <div className="flex bg-white dark:bg-gray-900 w-full min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-sm rounded-md border border-gray-200 dark:border-gray-700 p-6 shadow">
        <h1 className="mb-4 text-2xl text-black dark:text-white font-semibold">
          Register
        </h1>

        <form onSubmit={handleRegister} className="space-y-4">
          <div>
            <Label htmlFor="email" className="text-black dark:text-white">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-2 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-black dark:text-white"
              required
            />
          </div>

          <div>
            <Label htmlFor="password" className="text-black dark:text-white">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              placeholder="********"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-2 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-black dark:text-white"
              required
            />
          </div>

          <div>
            <Label
              htmlFor="organisationId"
              className="text-black dark:text-white"
            >
              Organisation ID
            </Label>
            <Input
              id="organisationId"
              type="number"
              placeholder="Organisation ID"
              value={organisationId}
              onChange={(e) => setOrganisationId(Number(e.target.value))}
              className="mt-2 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-black dark:text-white"
              required
            />
          </div>

          <div>
            <Label
              htmlFor="departmentId"
              className="text-black dark:text-white"
            >
              Department ID
            </Label>
            <Input
              id="departmentId"
              type="number"
              placeholder="Department ID"
              value={departmentId}
              onChange={(e) => setDepartmentId(Number(e.target.value))}
              className="mt-2 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-black dark:text-white"
              required
            />
          </div>

          <div>
            <Label htmlFor="groupId" className="text-black dark:text-white">
              Group ID
            </Label>
            <Input
              id="groupId"
              type="number"
              placeholder="Group ID"
              value={groupId}
              onChange={(e) => setGroupId(Number(e.target.value))}
              className="mt-2 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-black dark:text-white"
              required
            />
          </div>

          <Button
            type="submit"
            variant="default"
            className="w-full bg-gray-800 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 focus:bg-gray-700 dark:focus:bg-gray-600"
          >
            Register
          </Button>

          <div className="flex items-center justify-center mt-2">
            <Link
              to="/login"
              className="text-sm text-indigo-600 dark:text-indigo-400 hover:underline"
            >
              Already have an account? Login
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Register;
