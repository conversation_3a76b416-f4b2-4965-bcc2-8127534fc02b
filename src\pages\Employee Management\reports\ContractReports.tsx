"use client";

import React, { useEffect, useState, useC<PERSON>back } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useNavigate } from "react-router-dom";
import { BASE_URL } from "@/config";
import { useDebounce } from "@/hooks/use-debounce";
import { useTheme } from "@/hooks/use-theme";

// Icons
import {
  Search,
  Calendar as CalendarIcon,
  Filter,
  X,
  Download,
  RefreshCw,
  User,
  FileText,
  Clock,
  AlertTriangle,
  CheckCircle
} from "lucide-react";

// Shadcn/UI components
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";

interface EmployeeBio {
  id: number;
  first_name: string;
  middle_name: string | null;
  last_name: string;
  employee_no: string;
}

interface EmployeeContractInfo {
  id: number;
  employee_no: string;
  contract_type: string;
  contract_start_date: string;
  current_contract_end: string | null;
  end_of_probation_date: string | null;
  on_pip: boolean;
}

interface ContractReportData {
  employee_no: string;
  full_name: string;
  contract_type: string;
  contract_start_date: string;
  current_contract_end: string | null;
  end_of_probation_date: string | null;
  on_pip: boolean;
  contract_status: "active" | "expiring_soon" | "expired";
  days_until_expiry: number | null;
}

interface ContractReportsFilters {
  contract_type?: string;
  search?: string;
  contract_start_date?: string;
  current_contract_end?: string;
  contract_status?: string;
  on_pip?: string;
  ordering?: string;
}

const CONTRACT_TYPE_OPTIONS = [
  "Permanent", "Temporary", "Internship", "Consultant", 
  "Contractor", "Volunteer", "Probation", "management trainee"
];

const CONTRACT_STATUS_OPTIONS = [
  { value: "active", label: "Active" },
  { value: "expiring_soon", label: "Expiring Soon (30 days)" },
  { value: "expired", label: "Expired" }
];

// Badge variants for filter pills
const BADGE_VARIANTS = [
  "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100",
  "bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100",
  "bg-yellow-100 text-yellow-900 dark:bg-yellow-900 dark:text-yellow-100",
  "bg-red-100 text-red-900 dark:bg-red-900 dark:text-red-100",
  "bg-purple-100 text-purple-900 dark:bg-purple-900 dark:text-purple-100",
  "bg-pink-100 text-pink-900 dark:bg-pink-900 dark:text-pink-100",
];

// Helper function to calculate contract status
const getContractStatus = (endDate: string | null): { status: "active" | "expiring_soon" | "expired", daysUntilExpiry: number | null } => {
  if (!endDate) return { status: "active", daysUntilExpiry: null };
  
  const today = new Date();
  const contractEnd = new Date(endDate);
  const diffTime = contractEnd.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return { status: "expired", daysUntilExpiry: diffDays };
  } else if (diffDays <= 30) {
    return { status: "expiring_soon", daysUntilExpiry: diffDays };
  } else {
    return { status: "active", daysUntilExpiry: diffDays };
  }
};

export default function ContractReports() {
  const { token } = useSelector((s: RootState) => s.auth);
  const navigate = useNavigate();
  const { theme } = useTheme();

  // State
  const [filters, setFilters] = useState<ContractReportsFilters>({});
  const [contractStartDate, setContractStartDate] = useState<Date>();
  const [contractEndDate, setContractEndDate] = useState<Date>();
  const [reportData, setReportData] = useState<ContractReportData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);

  // Debounce the filters to prevent excessive API calls
  const debouncedFilters = useDebounce(filters, 500);

  // Fetch contract report data
  const fetchContractReport = useCallback(async (filterParams: ContractReportsFilters) => {
    if (!token) return setError("Missing token. Please log in.");
    setLoading(true);
    setError(null);
    try {
      // Fetch employee bio details and contract info in parallel
      const [bioRes, contractRes] = await Promise.all([
        fetch(`${BASE_URL}/users/employee-bio-details${filterParams.search ? `?search=${encodeURIComponent(filterParams.search)}` : ""}`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        }),
        fetch(`${BASE_URL}/users/employee-contract-info-details`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        }),
      ]);

      if (!bioRes.ok || !contractRes.ok) {
        throw new Error("Failed to fetch employee data");
      }

      const [bioData, contractData]: [EmployeeBio[], EmployeeContractInfo[]] = await Promise.all([
        bioRes.json(),
        contractRes.json(),
      ]);

      // Combine bio and contract data
      const combinedData: ContractReportData[] = bioData
        .map(bio => {
          const contractInfo = contractData.find(contract => contract.employee_no === bio.employee_no);
          if (!contractInfo) return null;

          const { status, daysUntilExpiry } = getContractStatus(contractInfo.current_contract_end);

          return {
            employee_no: bio.employee_no,
            full_name: `${bio.first_name}${bio.middle_name ? ` ${bio.middle_name}` : ""} ${bio.last_name}`,
            contract_type: contractInfo.contract_type,
            contract_start_date: contractInfo.contract_start_date,
            current_contract_end: contractInfo.current_contract_end,
            end_of_probation_date: contractInfo.end_of_probation_date,
            on_pip: contractInfo.on_pip,
            contract_status: status,
            days_until_expiry: daysUntilExpiry,
          };
        })
        .filter(Boolean) as ContractReportData[];

      // Apply filters
      let filteredData = combinedData;

      if (filterParams.contract_type && filterParams.contract_type !== "all") {
        filteredData = filteredData.filter(item => item.contract_type === filterParams.contract_type);
      }

      if (filterParams.contract_status && filterParams.contract_status !== "all") {
        filteredData = filteredData.filter(item => item.contract_status === filterParams.contract_status);
      }

      if (filterParams.on_pip && filterParams.on_pip !== "all") {
        const isPip = filterParams.on_pip === "true";
        filteredData = filteredData.filter(item => item.on_pip === isPip);
      }

      if (filterParams.contract_start_date) {
        filteredData = filteredData.filter(item => 
          item.contract_start_date >= filterParams.contract_start_date!
        );
      }

      if (filterParams.current_contract_end) {
        filteredData = filteredData.filter(item => 
          item.current_contract_end && item.current_contract_end <= filterParams.current_contract_end!
        );
      }

      setReportData(filteredData);
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  }, [token]);

  // Auto-fetch when debounced filters change
  useEffect(() => {
    if (Object.keys(debouncedFilters).length > 0) {
      fetchContractReport(debouncedFilters);
    }
  }, [debouncedFilters, fetchContractReport]);

  // Filter management functions
  const setFilterValue = useCallback((k: keyof ContractReportsFilters, v?: string) => {
    setFilters((prev) => ({ ...prev, [k]: v || "" }));
  }, []);

  const removeFilter = useCallback((k: keyof ContractReportsFilters) => {
    setFilters((prev) => {
      const copy = { ...prev };
      delete copy[k];
      return copy;
    });
  }, []);

  // Handle date filter changes
  useEffect(() => {
    if (contractStartDate) {
      setFilterValue("contract_start_date", contractStartDate.toISOString().slice(0, 10));
    } else {
      removeFilter("contract_start_date");
    }
  }, [contractStartDate, setFilterValue, removeFilter]);

  useEffect(() => {
    if (contractEndDate) {
      setFilterValue("current_contract_end", contractEndDate.toISOString().slice(0, 10));
    } else {
      removeFilter("current_contract_end");
    }
  }, [contractEndDate, setFilterValue, removeFilter]);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setFilters({});
    setReportData([]);
    setError(null);
    setContractStartDate(undefined);
    setContractEndDate(undefined);
  }, []);

  // Export data to CSV (mock function)
  const handleExportData = useCallback(() => {
    if (reportData.length === 0) return;

    setIsExporting(true);
    setTimeout(() => {
      setIsExporting(false);
      // In a real implementation, you would generate and download a CSV here
      alert("Export functionality would download a CSV file in a real implementation");
    }, 1000);
  }, [reportData]);

  return (
    <Screen>
      {/* Header with breadcrumb and title */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <Breadcrumb className="mb-2">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/employees-list">Employees</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Contract Reports</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <h1 className="text-2xl font-bold tracking-tight">Contract Reports</h1>
          <p className="text-muted-foreground">View employee contracts and their status</p>
        </div>

        <div className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                  className="h-9"
                >
                  <RefreshCw size={16} className="mr-1" />
                  Reset
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Clear all filters</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={handleExportData}
                  disabled={reportData.length === 0 || isExporting}
                  className="h-9"
                >
                  {isExporting ? (
                    <RefreshCw size={16} className="mr-1 animate-spin" />
                  ) : (
                    <Download size={16} className="mr-1" />
                  )}
                  Export
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export data to CSV</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            onClick={() => navigate("/employees-list")}
            className="h-9"
          >
            Back to Employees
          </Button>
        </div>
      </div>

      {/* Main content area */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters panel - 1 column on mobile, 1/4 on large screens */}
        <Card className="lg:col-span-1 h-fit">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Filters</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="h-8 px-2 text-muted-foreground"
              >
                <X size={16} className="mr-1" />
                Clear all
              </Button>
            </div>
            <CardDescription>
              Filters apply automatically
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-5">
            {/* Active filters */}
            {Object.entries(filters).filter(([_, v]) => !!v).length > 0 && (
              <div className="space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Active Filters</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(filters)
                    .filter(([_, v]) => !!v)
                    .map(([k, v], i) => (
                      <Badge
                        key={k}
                        className={`${BADGE_VARIANTS[i % BADGE_VARIANTS.length]} cursor-pointer transition-all hover:opacity-80`}
                        onClick={() => removeFilter(k as any)}
                      >
                        {k.replace(/_/g, ' ')}: {v}
                        <X size={12} className="ml-1" />
                      </Badge>
                    ))}
                </div>
              </div>
            )}

            {/* Search */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by employee name..."
                  value={filters.search || ""}
                  onChange={(e) => setFilterValue("search", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Contract Type filter */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Contract Type</Label>
              <Select
                value={filters.contract_type || "all"}
                onValueChange={(value) => setFilterValue("contract_type", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All contract types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All contract types</SelectItem>
                  {CONTRACT_TYPE_OPTIONS.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Contract Status filter */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Contract Status</Label>
              <Select
                value={filters.contract_status || "all"}
                onValueChange={(value) => setFilterValue("contract_status", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  {CONTRACT_STATUS_OPTIONS.map((status) => (
                    <SelectItem key={status.value} value={status.value}>{status.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* PIP Status filter */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">PIP Status</Label>
              <Select
                value={filters.on_pip || "all"}
                onValueChange={(value) => setFilterValue("on_pip", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All PIP statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All PIP statuses</SelectItem>
                  <SelectItem value="true">On PIP</SelectItem>
                  <SelectItem value="false">Not on PIP</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date filters */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Date Filters</Label>

              <div className="grid grid-cols-1 gap-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={`w-full justify-start text-left font-normal ${!contractStartDate && "text-muted-foreground"}`}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {contractStartDate ? (
                        contractStartDate.toLocaleDateString()
                      ) : (
                        "Contract Start Date"
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={contractStartDate}
                      onSelect={setContractStartDate}
                      initialFocus
                    />
                    {contractStartDate && (
                      <div className="p-3 border-t border-border">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setContractStartDate(undefined)}
                          className="w-full"
                        >
                          <X size={14} className="mr-1" />
                          Clear date
                        </Button>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={`w-full justify-start text-left font-normal ${!contractEndDate && "text-muted-foreground"}`}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {contractEndDate ? (
                        contractEndDate.toLocaleDateString()
                      ) : (
                        "Contract End Date"
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={contractEndDate}
                      onSelect={setContractEndDate}
                      initialFocus
                    />
                    {contractEndDate && (
                      <div className="p-3 border-t border-border">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setContractEndDate(undefined)}
                          className="w-full"
                        >
                          <X size={14} className="mr-1" />
                          Clear date
                        </Button>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results panel - 1 column on mobile, 3/4 on large screens */}
        <Card className="lg:col-span-3">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Contract Report</CardTitle>
              <Badge variant="outline" className="font-normal">
                {loading ? (
                  <span className="flex items-center">
                    <RefreshCw size={12} className="mr-1 animate-spin" /> Loading...
                  </span>
                ) : reportData.length > 0 ? (
                  `${reportData.length} contracts found`
                ) : (
                  "No results"
                )}
              </Badge>
            </div>
            <CardDescription>
              {Object.keys(filters).length > 0
                ? "Showing filtered results"
                : "Apply filters to see contract data"}
            </CardDescription>
          </CardHeader>

          <CardContent>
            {error && (
              <div className="mb-4 p-3 bg-destructive/10 text-destructive rounded-md">
                <p className="text-sm">{error}</p>
              </div>
            )}

            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="font-medium">Employee ID</TableHead>
                      <TableHead className="font-medium">Name</TableHead>
                      <TableHead className="font-medium">Contract Type</TableHead>
                      <TableHead className="font-medium">Start Date</TableHead>
                      <TableHead className="font-medium">End Date</TableHead>
                      <TableHead className="font-medium">Status</TableHead>
                      <TableHead className="font-medium">Days Left</TableHead>
                      <TableHead className="font-medium">PIP</TableHead>
                      <TableHead className="font-medium">Probation End</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      // Loading skeleton
                      Array(5).fill(0).map((_, i) => (
                        <TableRow key={i}>
                          {Array(9).fill(0).map((_, j) => (
                            <TableCell key={j}>
                              <Skeleton className="h-4 w-full" />
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : reportData.length > 0 ? (
                      // Contract data
                      reportData.map((contract) => (
                        <TableRow key={contract.employee_no} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{contract.employee_no}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <User size={16} className="mr-2 text-muted-foreground" />
                              {contract.full_name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="font-normal">
                              {contract.contract_type}
                            </Badge>
                          </TableCell>
                          <TableCell>{contract.contract_start_date}</TableCell>
                          <TableCell>{contract.current_contract_end || "Permanent"}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                contract.contract_status === "active" ? "default" :
                                contract.contract_status === "expiring_soon" ? "destructive" :
                                "secondary"
                              }
                              className="font-normal"
                            >
                              {contract.contract_status === "active" && <CheckCircle size={12} className="mr-1" />}
                              {contract.contract_status === "expiring_soon" && <AlertTriangle size={12} className="mr-1" />}
                              {contract.contract_status === "expired" && <Clock size={12} className="mr-1" />}
                              {contract.contract_status.replace("_", " ")}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {contract.days_until_expiry !== null ? (
                              <span className={contract.days_until_expiry < 0 ? "text-red-600" : contract.days_until_expiry <= 30 ? "text-yellow-600" : ""}>
                                {contract.days_until_expiry} days
                              </span>
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell>
                            {contract.on_pip ? (
                              <Badge variant="destructive" className="font-normal">
                                <AlertTriangle size={12} className="mr-1" />
                                On PIP
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="font-normal">
                                No
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>{contract.end_of_probation_date || "-"}</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      // No results
                      <TableRow>
                        <TableCell colSpan={9} className="h-24 text-center">
                          <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <FileText size={24} className="mb-2" />
                            <p>No contract records found</p>
                            <p className="text-sm">Try adjusting your filters</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between border-t p-4">
            <p className="text-sm text-muted-foreground">
              {reportData.length > 0 && "Showing all available results"}
            </p>
          </CardFooter>
        </Card>
      </div>
    </Screen>
  );
}
