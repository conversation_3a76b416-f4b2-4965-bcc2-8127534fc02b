import React, { useState, useEffect } from 'react';
import { Check, X, RefreshCw } from 'lucide-react';
import { BASE_URL } from '@/config';
import { useToast } from '@/hooks/use-toast';
import { RootState } from '@/redux/store';
import { useSelector } from 'react-redux';

interface HRKpiApprovalSectionProps{
  initialCustomKpis: any[];
}

const HRKpiApprovalSection: React.FC<HRKpiApprovalSectionProps> = ({initialCustomKpis = []}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [kpiDetails, setKpiDetails] = useState<any[]>([]); // State to store KPIs, Hows, and Employee info
  const [customKpis, setCustomKpis] = useState<any[]>(initialCustomKpis || []);
  const [showConfirmation, setShowConfirmation] = useState(false); // For the confirmation dialog
  const [selectedKpi, setSelectedKpi] = useState<any>(null); // To store the selected KPI for approval/rejection
  const { token } = useSelector((state: RootState) => state.auth);
  const [actionType, setActionType] = useState<'approve' | 'reject' | null>(null);

  const fetchPendingCustomKpis = async () => {
    try {
      setRefreshing(true);
      const response = await fetch(`${BASE_URL}/hrm/kpi-whats`, {
        headers: { Authorization: `Token ${token}` },
      });
      
      if (!response.ok) throw new Error('Failed to fetch KPIs');
      
      const data = await response.json();
      // Filter for pending custom KPIs
      const pendingCustomKpis = data.filter((kpi:any) => 
        kpi.status === 'pending' && kpi.Kpi_type === 'custom'
      );
      console.log("Pending custom KPIs:", pendingCustomKpis);
      setCustomKpis(pendingCustomKpis);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to fetch pending custom KPIs',
      });
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchPendingCustomKpis();
  }, [token]);

  useEffect(() => {
    const fetchKpiDetails = async () => {
      if (customKpis.length === 0) {
        setKpiDetails([]);
        return;
      }
      
      setLoading(true);
      try {
        const kpiData = await Promise.all(customKpis.map(async (kpi) => {
          const kpiId = kpi.id;

          // Step 1: Fetch Employee ID using the KPI ID (What ID)
          const kpiEmployeeResponse = await fetch(`${BASE_URL}/hrm/kpi-to-employee?kpi=${kpiId}`, {
            headers: { Authorization: `Token ${token}` },
          });
          const employeeData = await kpiEmployeeResponse.json();
          const employee_no = employeeData && employeeData.length > 0 ? employeeData[0].employeeid : 'N/A';

          // Step 2: Fetch Employee details using employee_no
          const employeeResponse = await fetch(`${BASE_URL}/employee_details/?team=ALL&department=ALL`, {
            headers: { Authorization: `Token ${token}` },
          });
          const employeeDetailsData = await employeeResponse.json();
          const employeeDetails = employeeDetailsData.results.filter((emp: any) =>
            emp.employee_no_id === employee_no || emp.employee_no === employee_no
          );

          const employee = employeeDetails[0] || {};
          const firstName = employee.first_name || "Unknown";
          const lastName = employee.last_name || "Unknown";
          const employeeNo = employee.employee_no || "N/A";

          // Step 3: Fetch Hows for the KPI
          const howsResponse = await fetch(`${BASE_URL}/hrm/kpi-hows?whats=${kpiId}`, {
            headers: { Authorization: `Token ${token}` },
          });
          const howsData = await howsResponse.json();

          return {
            ...kpi,
            hows: howsData,  // Add Hows to the KPI data
            employee: {
              first_name: firstName,
              last_name: lastName,
              employee_no: employeeNo,
            },
          };
        }));

        setKpiDetails(kpiData); // Set the detailed data for KPIs
      } catch (err: any) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: err.message || 'Failed to fetch KPI details.',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchKpiDetails();
  }, [customKpis, token, toast]);

  const handleApproveKpi = async () => {
    setLoading(true);
    try {
      const res = await fetch(`${BASE_URL}/hrm/kpi-whats/${selectedKpi.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Token ${token}`,
        },
        body: JSON.stringify({
          status: 'approved',
        }),
      });
      if (!res.ok) throw new Error('Failed to approve KPI');
      
      toast({
        variant: 'default',
        title: 'Success',
        description: 'KPI has been approved successfully.',
      });
      
      // Refresh the KPI list after approval
      fetchPendingCustomKpis();
    } catch (err: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to approve KPI.',
      });
    } finally {
      setLoading(false);
      setShowConfirmation(false);
      setSelectedKpi(null);
    }
  };

  const handleRejectKpi = async () => {
    setLoading(true);
    try {
      const res = await fetch(`${BASE_URL}/hrm/kpi-whats/${selectedKpi.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Token ${token}`,
        },
        body: JSON.stringify({
          status: 'rejected',
        }),
      });
      if (!res.ok) throw new Error('Failed to reject KPI');
      
      toast({
        variant: 'destructive',
        title: 'Rejected',
        description: 'KPI has been rejected.',
      });
      
      // Refresh the KPI list after rejection
      fetchPendingCustomKpis();
    } catch (err: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to reject KPI.',
      });
    } finally {
      setLoading(false);
      setShowConfirmation(false);
      setSelectedKpi(null);
    }
  };

  const confirmAction = (kpi: any, action: 'approve' | 'reject') => {
    setSelectedKpi(kpi);
    setActionType(action); // Store the action type
    setShowConfirmation(true);
  };
  
  const cancelAction = () => {
    setShowConfirmation(false);
    setSelectedKpi(null);
  };

  const handleRefresh = () => {
    fetchPendingCustomKpis();
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Custom KPI Approval</h2>
        <button 
          onClick={handleRefresh} 
          className="flex items-center gap-2 px-3 py-2 text-sm bg-green-50 text-green-600 rounded-md hover:bg-green-100"
          disabled={refreshing}
        >
          <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
          Refresh
        </button>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-green-500"></div>
        </div>
      ) : (
        <div>
          {kpiDetails.length === 0 ? (
            <div className="bg-gray-50 rounded-lg p-8 text-center">
              <p className="text-gray-500">No custom KPIs awaiting approval.</p>
              <p className="text-sm text-gray-400 mt-2">
                Custom KPIs that need approval will appear here.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {kpiDetails.map((kpi) => (
                <div key={kpi.id} className="flex justify-between items-start p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <div className="space-y-2">
                    <p className="font-semibold text-gray-700">{kpi.what}</p>
                    <p className="text-sm text-gray-500">{kpi.total_marks} marks</p>
                    <div>
                      <strong className="text-sm text-gray-700">Metrics (Hows):</strong>
                      <ul className="list-disc pl-5 mt-1">
                        {kpi.hows.map((how: any, index: number) => (
                          <li key={index} className="text-sm text-gray-600">
                            {how.how || 'Unknown Metric'}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="flex items-center mt-1">
                      <strong className="text-sm text-gray-700 mr-2">Employee:</strong> 
                      <span className="text-sm">
                        {kpi.employee.first_name && kpi.employee.last_name
                          ? `${kpi.employee.first_name} ${kpi.employee.last_name}`
                          : `Employee No: ${kpi.employee.employee_no}`}
                      </span>
                    </div>
                    
                    <div className="text-xs text-green-600 bg-green-50 inline-block px-2 py-1 rounded">
                      Created: {new Date(kpi.created_at || Date.now()).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <button
                      onClick={() => confirmAction(kpi, 'approve')}
                      className="flex items-center gap-1 px-3 py-1.5 text-sm bg-green-50 text-green-600 rounded hover:bg-green-100"
                      disabled={loading}
                    >
                      <Check size={16} /> Approve
                    </button>
                    <button
                      onClick={() => confirmAction(kpi, 'reject')}
                      className="flex items-center gap-1 px-3 py-1.5 text-sm bg-red-50 text-red-600 rounded hover:bg-red-100"
                      disabled={loading}
                    >
                      <X size={16} /> Reject
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
          <div className="relative w-full max-w-lg bg-white p-6 rounded-lg shadow-xl">
            <h2 className="text-xl font-semibold text-gray-700 mb-4">Confirm {actionType === 'approve' ? 'Approval' : 'Rejection'}</h2>
            {selectedKpi && (
              <div className="mb-4 p-3 bg-gray-50 rounded-md">
                <p className="font-medium">{selectedKpi.what}</p>
                <p className="text-sm text-gray-600">For: {selectedKpi.employee.first_name} {selectedKpi.employee.last_name}</p>
              </div>
            )}
            <p className="text-sm text-gray-600 mb-6">
              {actionType === 'approve' 
                ? 'Are you sure you want to approve this KPI? This will assign it to the employee.' 
                : 'Are you sure you want to reject this KPI? This action cannot be undone.'}
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={cancelAction}
                className="text-sm bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={actionType === 'approve' ? handleApproveKpi : handleRejectKpi}
                className={`text-sm px-4 py-2 rounded-md ${
                  actionType === 'approve' 
                    ? 'bg-green-600 hover:bg-green-700 text-white' 
                    : 'bg-red-600 hover:bg-red-700 text-white'
                }`}
                disabled={loading}
              >
                {loading ? 'Processing...' : actionType === 'approve' ? 'Yes, Approve' : 'Yes, Reject'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HRKpiApprovalSection;