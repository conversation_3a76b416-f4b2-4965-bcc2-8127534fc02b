import React, { useState, useEffect } from "react";
import axios from "axios";
import { BASE_URL } from "../../config";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ClipboardList,
  RefreshCw,
  Plus,
  CircleAlert,
  Search,
  Briefcase,
  Users,
  User,
  ChevronRight,
  X,
  CheckCircle,
  Eye,
  BarChart3,
  ArrowUpRight,
  Pencil,
  ChevronLeft,
  <PERSON>lter,
  TrendingUp,
  Target,
} from "lucide-react";
import {
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  Breadcrumb,
} from "@/components/ui/breadcrumb";
import { Screen } from "@/app-components/layout/screen";
import EditKpiModal from "./EditKpiModal";
import KpiWizardModal from "@/app-components/Performance/kpi/KpiWizardModal";
import MibTargetModal from "@/app-components/Performance/kpi/MibTargetModal";
import SalesTargetModal from "@/app-components/Performance/kpi/SalesTargetModal";


interface Employee {
  id: number;
  employee_no_id: string;
  teams_code_id: number | null;
  department_id: number;
  first_name: string;
  last_name: string;
  email: string;
  job_title: string;
  is_active: number;
  // Keep these for backward compatibility if needed
  employee_no?: string;
  job_code?: string;
}

interface JobPosition {
  id: number;
  date_created: string;
  job_code: string;
  job_title: string;
  job_description: string;
  job_responsibilities: string;
  job_requirements: string;
  job_qualifications: string;
  job_experience: string;
  job_skills: string;
  job_min_salary: number;
  job_max_salary: number;
  posission_status: string;
  no_of_employees: number;
  required_proffesional_body: string;
  accademic_qualification: string;
  professional_requirements: string;
  organisation: number;
  department: number;
  created_by: string;
}

interface Department {
  id: number;
  name: string;
  description: string;
  dep_head: string;
  dep_head_assistant: string;
  department_status_active: boolean;
  organisation: number;
  parent_department: number | null;
}

export interface Kpi {
  id: number;
  what: string;
  total_marks: number;
  created_at: string;
  hows: KpiHow[];
  MIB_target?: number;
  Sales_target?: number;
  kpi_type: "default" | "custom" | "MIB Target" | "Sales Target";
}

export interface KpiHow {
  id: number;
  how: string;
}

interface ToastProps {
  open: boolean;
  message: string;
  severity: "success" | "error" | "info";
  onClose: () => void;
}
const Toast: React.FC<ToastProps> = ({ open, message, severity, onClose }) => {
  if (!open) return null;

  const bgColor =
    severity === "success"
      ? "bg-gradient-to-r from-green-500 to-emerald-600"
      : severity === "error"
        ? "bg-gradient-to-r from-red-500 to-rose-600"
        : "bg-gradient-to-r from-green-500 to-indigo-600";

  const Icon =
    severity === "success"
      ? CheckCircle
      : severity === "error"
        ? CircleAlert
        : CircleAlert;

  return (
    <div className="fixed bottom-4 right-4 z-50 animate-slide-up">
      <div
        className={`p-4 rounded-lg shadow-lg ${bgColor} text-white flex items-center gap-3 max-w-md border border-white/10`}
      >
        <div className="bg-white/20 p-2 rounded-full">
          <Icon className="h-5 w-5 flex-shrink-0" />
        </div>
        <span className="text-sm font-medium">{message}</span>
        <button
          onClick={onClose}
          className="ml-auto p-1 hover:bg-white/30 rounded-full transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export const KpiDetailModal: React.FC<{
  open: boolean;
  onClose: () => void;
  title: string;
  subtitle: string;
  kpis: Kpi[];
  loading: boolean;
  token: any;
  onKpiUpdated: () => void;
}> = ({ open, onClose, title, subtitle, kpis, loading, token, onKpiUpdated }) => {
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedKpi, setSelectedKpi] = useState<Kpi | null>(null);
  console.log("KPIs in modal:", kpis);

  const handleEditClick = (kpi: Kpi) => {
    setSelectedKpi(kpi);
    setEditModalOpen(true);
  };
  const handleEditSuccess = () => {
    setEditModalOpen(false);
    onKpiUpdated();
  }
  return (
    <>
      <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-green-600 to-indigo-700 -mx-6 -mt-6 px-6 py-4 rounded-t-lg text-white">
            <DialogTitle className="flex items-center gap-2">
              <ClipboardList className="h-5 w-5" />
              {title}
            </DialogTitle>
            <DialogDescription className="text-green-100">
              {subtitle}
            </DialogDescription>
          </DialogHeader>

          {loading ? (
            <div className="flex justify-center items-center py-16">
              <div className="w-12 h-12 border-4 border-green-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : kpis.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-100">
              <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-green-100 mb-5">
                <ClipboardList className="h-10 w-10 text-green-600" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                No KPIs Found
              </h3>
              <p className="text-gray-500 max-w-md mx-auto">
                This entity doesn't have any KPIs assigned yet. Create a new KPI
                to improve performance tracking.
              </p>
            </div>
          ) : (
            <div className="space-y-5 mt-4">
              {kpis.map((kpi) => (
                <Card
                  key={kpi.id}
                  className="overflow-hidden border-t-4 border-t-green-600 shadow-lg hover:shadow-xl transition-shadow"
                >
                  <CardHeader className="bg-gradient-to-br from-green-50 to-indigo-50 pb-3">
                    <CardTitle className="text-base flex items-start gap-2">
                      <BarChart3 className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span>{kpi.what}</span>
                    </CardTitle>
                    <Button
                      variant="ghost"
                      size='sm'
                      onClick={() => handleEditClick(kpi)}
                      className="flex items-center gap-1 text-green-600 hover:bg-green-50 hover:text-green-700">
                      <Pencil className="h-4 w-4" /> Edit KPI
                    </Button>
                    <CardDescription className="text-xs flex items-center gap-2 mt-1">
                      <div className="bg-green-600 text-white px-2 py-0.5 rounded-full text-xs font-medium">
                        Total: {kpi.total_marks} marks
                      </div>
                      <span className="text-gray-500">
                        Created: {new Date(kpi.created_at).toLocaleDateString()}
                      </span>
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <h4 className="text-sm font-medium text-green-800 mb-3 flex items-center">
                      <ArrowUpRight className="h-4 w-4 mr-1" />
                      How to Achieve (Metrics)
                    </h4>
                    <div className="space-y-3">
                      {/* Show MIB Target value and hows for MIB Target KPIs */}
                      {kpi.kpi_type === "MIB Target" && (
                        <>
                          <div className="flex items-start gap-3 pb-3 border-b border-gray-100">
                            <div className="flex-shrink-0 bg-gradient-to-r from-green-600 to-indigo-700 text-white font-medium rounded-full w-7 h-7 flex items-center justify-center text-xs">
                              T
                            </div>
                            <div className="flex-1">
                              <p className=" font-bold text-gray-700">
                                MIB Target: KSh {kpi.MIB_target?.toLocaleString() || '0'}
                              </p>
                            </div>
                          </div>
                          {kpi.hows?.map((how, idx) => (
                            <div
                              key={idx}
                              className="flex items-start gap-3 pb-3 border-b border-gray-100 last:border-0"
                            >
                              <div className="flex-shrink-0 bg-gradient-to-r from-green-600 to-indigo-700 text-white font-medium rounded-full w-7 h-7 flex items-center justify-center text-xs">
                                {idx + 1}
                              </div>
                              <div className="flex-1">
                                <p className="text-sm text-gray-700">{how.how}</p>
                              </div>
                            </div>
                          ))}
                        </>
                      )}

                      {/* Show Sales Target value and hows for Sales Target KPIs */}
                      {kpi.kpi_type === "Sales Target" && (
                        <>
                          <div className="flex items-start gap-3 pb-3 border-b border-gray-100">
                            <div className="flex-shrink-0 bg-gradient-to-r from-green-600 to-indigo-700 text-white font-medium rounded-full w-7 h-7 flex items-center justify-center text-xs">
                              T
                            </div>
                            <div className="flex-1">
                              <p className=" font-bold text-gray-700">
                                Sales Target: KSh {kpi.Sales_target?.toLocaleString() || '0'}
                              </p>
                            </div>
                          </div>
                          {kpi.hows?.map((how, idx) => (
                            <div
                              key={idx}
                              className="flex items-start gap-3 pb-3 border-b border-gray-100 last:border-0"
                            >
                              <div className="flex-shrink-0 bg-gradient-to-r from-green-600 to-indigo-700 text-white font-medium rounded-full w-7 h-7 flex items-center justify-center text-xs">
                                {idx + 1}
                              </div>
                              <div className="flex-1">
                                <p className="text-sm text-gray-700">{how.how}</p>
                              </div>
                            </div>
                          ))}
                        </>
                      )}

                      {/* Show only hows for default/other KPIs */}
                      {kpi.kpi_type !== "MIB Target" && kpi.kpi_type !== "Sales Target" && kpi.hows?.map((how, idx) => (
                        <div
                          key={idx}
                          className="flex items-start gap-3 pb-3 border-b border-gray-100 last:border-0"
                        >
                          <div className="flex-shrink-0 bg-gradient-to-r from-green-600 to-indigo-700 text-white font-medium rounded-full w-7 h-7 flex items-center justify-center text-xs">
                            {idx + 1}
                          </div>
                          <div className="flex-1">
                            <p className="text-sm text-gray-700">{how.how}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </DialogContent>
      </Dialog>
      <EditKpiModal
        open={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        kpi={selectedKpi}
        token={token}
        onSuccess={handleEditSuccess}
      />
    </>
  );
};

const DepartmentFilter: React.FC<{
  selectedDepartment: number | null;
  setSelectedDepartment: (value: number | null) => void;
  departments: Department[];
}> = ({ selectedDepartment, setSelectedDepartment, departments }) => {
  return (
    <div className="relative">
      <select
        className="w-full appearance-none bg-white border border-gray-200 rounded-lg px-4 py-2 pr-8 h-12 focus:outline-none focus:ring-2 focus:ring-green-500 shadow-sm"
        value={selectedDepartment === null ? "" : selectedDepartment}
        onChange={(e) =>
          setSelectedDepartment(e.target.value ? Number(e.target.value) : null)
        }
      >
        <option value="">All Departments</option>
        {departments.map((dept: Department) => (
          <option key={dept.id} value={dept.id}>
            {dept.name}
          </option>
        ))}
      </select>
      <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
        <ChevronRight className="h-5 w-5 text-gray-400 transform rotate-90" />
      </div>
    </div>
  );
};

const StatsCard: React.FC<{
  title: string;
  value: number;
  icon: React.ReactNode;
  bgColor: string;
}> = ({ title, value, icon, bgColor }) => {
  return (
    <div className={`${bgColor} rounded-lg shadow-md p-4 text-white`}>
      <div className="flex items-center">
        <div className="bg-white/20 p-2 rounded-full mr-3">{icon}</div>
        <div>
          <p className="text-sm font-medium text-white/90">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
        </div>
      </div>
    </div>
  );
};

const KpiManagementPage: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [jobPositions, setJobPositions] = useState<JobPosition[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("employees");
  const [kpiWizardOpen, setKpiWizardOpen] = useState<boolean>(false);
  const [openMibTargetModal, setOpenMibTargetModal] = useState<boolean>(false);
  const [openSalesTargetModal, setOpenSalesTargetModal] = useState<boolean>(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<number | null>(
    null
  );
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "error" | "info";
  }>({
    open: false,
    message: "",
    severity: "info",
  });
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "ascending" | "descending";
  } | null>(null);
  const [kpiDetailOpen, setKpiDetailOpen] = useState<boolean>(false);
  const [selectedEntityId, setSelectedEntityId] = useState<any>("");
  const [selectedEntityName, setSelectedEntityName] = useState<string>("");
  const [entityKpis, setEntityKpis] = useState<Kpi[]>([]);
  const [entityKpisLoading, setEntityKpisLoading] = useState<boolean>(false);

  // Pagination states
  const [employeesCurrentPage, setEmployeesCurrentPage] = useState<number>(1);
  const [jobsCurrentPage, setJobsCurrentPage] = useState<number>(1);
  const [itemsPerPage] = useState<number>(10);
  const [showFilters, setShowFilters] = useState<boolean>(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError("");

        const [employeesRes, jobPositionsRes, departmentsRes] =
          await Promise.all([
            axios.get(`${BASE_URL}/employee_details/`, {
              params: { team: 'ALL', department: 'ALL' },
              headers: { Authorization: `Token ${token}` },
            }),
            axios.get(`${BASE_URL}/hrm/job-positions`, {
              headers: { Authorization: `Token ${token}` },
            }),
            axios.get(`${BASE_URL}/users/departments`, {
              headers: { Authorization: `Token ${token}` },
            }),
          ]);

        // Transform the new API response to match our interface
        const transformedEmployees = employeesRes.data.results.map((emp: any) => ({
          ...emp,
          employee_no: emp.employee_no_id, // Map for backward compatibility
          job_code: emp.job_title, // Map for backward compatibility
        }));

        setEmployees(transformedEmployees);
        setJobPositions(jobPositionsRes.data);
        setDepartments(departmentsRes.data);
      } catch (err) {
        console.error("Data fetch error:", err);
        setError("Failed to load data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [token]);

  // Reset pagination when search term or department filter changes
  useEffect(() => {
    setEmployeesCurrentPage(1);
    setJobsCurrentPage(1);
  }, [searchTerm, selectedDepartment]);

  const requestSort = (key: string) => {
    let direction: "ascending" | "descending" = "ascending";
    if (
      sortConfig &&
      sortConfig.key === key &&
      sortConfig.direction === "ascending"
    ) {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  const sortedItems = (items: any[], sortKey: string) => {
    if (!sortConfig) return items;
    if (sortConfig.key !== sortKey) return items;

    return [...items].sort((a, b) => {
      let aValue = sortConfig.key
        .split(".")
        .reduce((obj, key) => obj?.[key], a);
      let bValue = sortConfig.key
        .split(".")
        .reduce((obj, key) => obj?.[key], b);

      if (aValue === null || aValue === undefined) aValue = "";
      if (bValue === null || bValue === undefined) bValue = "";

      if (typeof aValue === "string") {
        if (sortConfig.direction === "ascending") {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      } else {
        if (sortConfig.direction === "ascending") {
          return aValue - bValue;
        } else {
          return bValue - aValue;
        }
      }
    });
  };

  const getSortDirectionIcon = (key: string) => {
    if (!sortConfig || sortConfig.key !== key) {
      return <span className="text-gray-300 ml-1">↕</span>;
    }
    return sortConfig.direction === "ascending" ? (
      <span className="text-green-600 ml-1">↑</span>
    ) : (
      <span className="text-green-600 ml-1">↓</span>
    );
  };


  const getDepartmentName = (departmentId: number) => {
    const department = departments.find((dept) => dept.id === departmentId);
    return department ? department.name : `Dept #${departmentId}`;
  };

  const getJobTitle = (jobCode: string) => {
    const job = jobPositions.find((job) => job.job_code === jobCode);
    return job ? job.job_title : jobCode;
  };

  const getFilteredEmployees = () => {
    let filtered = employees;


    filtered = filtered.filter(
      (emp) =>
        (emp.first_name?.toLowerCase() || "").includes(
          searchTerm.toLowerCase()
        ) ||
        (emp.last_name?.toLowerCase() || "").includes(
          searchTerm.toLowerCase()
        ) ||
        (emp.email?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
        (emp.employee_no_id?.toLowerCase() || "").includes(
          searchTerm.toLowerCase()
        ) ||
        (emp.employee_no?.toLowerCase() || "").includes(
          searchTerm.toLowerCase()
        )
    );


    if (selectedDepartment !== null) {
      filtered = filtered.filter(
        (emp) => emp.department_id === selectedDepartment
      );
    }

    return filtered;
  };


  const getFilteredJobPositions = () => {
    let filtered = jobPositions;

    filtered = filtered.filter(
      (job) =>
        job.job_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.job_code.toLowerCase().includes(searchTerm.toLowerCase())
    );
    if (selectedDepartment !== null) {
      filtered = filtered.filter(
        (job) => job.department === selectedDepartment
      );
    }

    return filtered;
  };


  const getEmployeesForJob = (jobCode: string) => {
    return employees.filter((emp) =>
      emp.job_code === jobCode ||
      (emp.job_title && getJobTitle(jobCode) === emp.job_title)
    );
  };

  // Pagination helper functions
  const getPaginatedEmployees = () => {
    const filtered = getFilteredEmployees();
    const startIndex = (employeesCurrentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filtered.slice(startIndex, endIndex);
  };

  const getPaginatedJobPositions = () => {
    const filtered = getFilteredJobPositions();
    const startIndex = (jobsCurrentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filtered.slice(startIndex, endIndex);
  };

  const getTotalEmployeePages = () => {
    return Math.ceil(getFilteredEmployees().length / itemsPerPage);
  };

  const getTotalJobPages = () => {
    return Math.ceil(getFilteredJobPositions().length / itemsPerPage);
  };

  const handleEmployeePageChange = (page: number) => {
    setEmployeesCurrentPage(page);
  };

  const handleJobPageChange = (page: number) => {
    setJobsCurrentPage(page);
  };

  // Modern Pagination Component
  const PaginationComponent: React.FC<{
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    totalItems: number;
    itemsPerPage: number;
  }> = ({ currentPage, totalPages, onPageChange, totalItems, itemsPerPage }) => {
    if (totalPages <= 1) return null;

    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);

    const getPageNumbers = () => {
      const pages = [];
      const maxVisiblePages = 5;

      if (totalPages <= maxVisiblePages) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (currentPage <= 3) {
          for (let i = 1; i <= 4; i++) {
            pages.push(i);
          }
          pages.push('...');
          pages.push(totalPages);
        } else if (currentPage >= totalPages - 2) {
          pages.push(1);
          pages.push('...');
          for (let i = totalPages - 3; i <= totalPages; i++) {
            pages.push(i);
          }
        } else {
          pages.push(1);
          pages.push('...');
          for (let i = currentPage - 1; i <= currentPage + 1; i++) {
            pages.push(i);
          }
          pages.push('...');
          pages.push(totalPages);
        }
      }
      return pages;
    };

    return (
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200">
        <div className="text-sm text-gray-600">
          Showing <span className="font-medium text-gray-900">{startItem}</span> to{' '}
          <span className="font-medium text-gray-900">{endItem}</span> of{' '}
          <span className="font-medium text-gray-900">{totalItems}</span> results
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="flex items-center gap-1 h-9 px-3 border-gray-300 hover:bg-gray-50 disabled:opacity-50"
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>

          <div className="flex items-center gap-1">
            {getPageNumbers().map((page, index) => (
              <React.Fragment key={index}>
                {page === '...' ? (
                  <span className="px-3 py-2 text-gray-500">...</span>
                ) : (
                  <Button
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange(page as number)}
                    className={`h-9 w-9 p-0 ${currentPage === page
                      ? 'bg-gradient-to-r from-green-600 to-green-700 text-white border-green-600 hover:opacity-90'
                      : 'border-gray-300 hover:bg-gray-50'
                      }`}
                  >
                    {page}
                  </Button>
                )}
              </React.Fragment>
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="flex items-center gap-1 h-9 px-3 border-gray-300 hover:bg-gray-50 disabled:opacity-50"
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  const fetchEmployeeKpis = async (
    employeeNo: string,
    employeeName: string
  ) => {
    setSelectedEntityId(employeeNo);
    setSelectedEntityName(employeeName);
    setKpiDetailOpen(true);
    setEntityKpisLoading(true);
    setEntityKpis([]);

    try {
      console.log("Fetching KPIs for employee:", employeeNo);
      const kpiToEmployeeRes = await axios.get(
        `${BASE_URL}/hrm/kpi-to-employee`,
        {
          params: { employee_no: employeeNo },
          headers: { Authorization: `Token ${token}` },
        }
      );
      console.log("KPIs for employee:", kpiToEmployeeRes.data);
      const kpiIds = kpiToEmployeeRes.data
        .filter((item: any) => item.employeeid === employeeNo)
        .map((item: any) => item.kpi);
      console.log("Filtered KPI IDs:", kpiIds);
      if (kpiIds.length === 0) {
        setEntityKpis([]);
        setEntityKpisLoading(false);
        return;
      }

      const kpisWithDetails = await Promise.all(
        kpiIds.map(async (kpiId: number) => {
          const whatRes = await axios.get(
            `${BASE_URL}/hrm/kpi-whats/${kpiId}`,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );
          console.log("KPI What:", whatRes.data);

          const kpiType = whatRes.data.Kpi_type;
          console.log("KPI Type:", kpiType);

          const howsRes = await axios.get(`${BASE_URL}/hrm/kpi-hows`, {
            params: { whats: kpiId },
            headers: { Authorization: `Token ${token}` },
          });

          return {
            kpi_type: kpiType,
            MIB_target: whatRes.data.MIB_target || 0,
            Sales_target: whatRes.data.Sales_target || 0,
            id: whatRes.data.id,
            what: whatRes.data.what,
            total_marks: whatRes.data.total_marks,
            created_at: whatRes.data.created_at,
            hows: howsRes.data.map((h: any) => ({
              id: h.id,
              how: h.how,
            })),
          };
        })
      );

      setEntityKpis(kpisWithDetails);
    } catch (err) {
      console.error("Error fetching employee KPIs:", err);
      setSnackbar({
        open: true,
        message: "Failed to load KPIs for this employee",
        severity: "error",
      });
      setEntityKpis([]);
    } finally {
      setEntityKpisLoading(false);
    }
  };

  const fetchJobPositionKpis = async (jobId: number, jobTitle: string) => {
    setSelectedEntityId(jobId);
    setSelectedEntityName(jobTitle);
    setKpiDetailOpen(true);
    setEntityKpisLoading(true);
    setEntityKpis([]);

    try {
      const jobPosition = jobPositions.find((job) => job.id === jobId);
      if (!jobPosition) {
        console.error("Could not find job position with ID:", jobId);
        throw new Error("Job position not found");
      }

      const jobCode = jobPosition.job_code;
      console.log(
        `Looking for KPIs for job ID ${jobId} with job_code: ${jobCode}`
      );

      const kpiToJobRes = await axios.get(
        `${BASE_URL}/hrm/kpi-to-jobpositions`,
        {
          headers: { Authorization: `Token ${token}` },
        }
      );
      console.log("All job position KPIs:", kpiToJobRes.data);
      const kpiIds = kpiToJobRes.data
        .filter((item: any) => {
          if (item.job_code === jobCode) {
            console.log(`Found matching job_code: ${item.job_code}`);
            return true;
          }
          console.log(
            `Comparing item job_code: ${item.job_code} with our job_code: ${jobCode}`
          );

          return false;
        })
        .map((item: any) => item.kpi);

      console.log("Filtered KPI IDs for this job:", kpiIds);

      if (kpiIds.length === 0) {
        setEntityKpis([]);
        setEntityKpisLoading(false);
        return;
      }

      const kpisWithDetails = await Promise.all(
        kpiIds.map(async (kpiId: number) => {
          const whatRes = await axios.get(
            `${BASE_URL}/hrm/kpi-whats/${kpiId}`,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );

          const howsRes = await axios.get(`${BASE_URL}/hrm/kpi-hows`, {
            params: { whats: kpiId },
            headers: { Authorization: `Token ${token}` },
          });

          return {
            kpi_type: whatRes.data.Kpi_type || "default",
            MIB_target: whatRes.data.MIB_target || 0,
            Sales_target: whatRes.data.Sales_target || 0,
            id: whatRes.data.id,
            what: whatRes.data.what,
            total_marks: whatRes.data.total_marks,
            created_at: whatRes.data.created_at,
            hows: howsRes.data.map((h: any) => ({
              id: h.id,
              how: h.how,
            })),
          };
        })
      );

      setEntityKpis(kpisWithDetails);
    } catch (err) {
      console.error("Error fetching job position KPIs:", err);
      setSnackbar({
        open: true,
        message: "Failed to load KPIs for this job position",
        severity: "error",
      });
      setEntityKpis([]);
    } finally {
      setEntityKpisLoading(false);
    }
  };

  const showToast = (
    message: string,
    severity: "success" | "error" | "info"
  ) => {
    setSnackbar({ open: true, message, severity });
    setTimeout(() => setSnackbar((prev) => ({ ...prev, open: false })), 3000);
  };

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="/" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href="/kpi-management" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Performance
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>KPI Management Dashboard</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  return (
    <Screen>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <div className="max-w-7xl mx-auto px-4 py-6">
          {/* Breadcrumb */}
          <div className="mb-6">
            {breadcrumb}
          </div>

          {/* Modern Header */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-200 mb-8 overflow-hidden">
            <div className="bg-gradient-to-r from-green-600 via-green-700 to-emerald-600 px-8 py-12 text-white relative overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

              <div className="relative z-10">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="bg-white/20 p-3 rounded-xl backdrop-blur-sm">
                        <ClipboardList className="h-8 w-8 text-white" />
                      </div>
                      <div>
                        <h1 className="text-3xl font-bold text-white mb-2">
                          KPI Management Dashboard
                        </h1>
                        <p className="text-green-100 text-lg">
                          Track and manage performance metrics for employees and job positions
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-4 mt-6">
                      <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 flex items-center gap-2">
                        <TrendingUp className="h-5 w-5 text-green-200" />
                        <span className="text-sm font-medium text-green-100">Performance Tracking</span>
                      </div>
                      <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 flex items-center gap-2">
                        <Target className="h-5 w-5 text-green-200" />
                        <span className="text-sm font-medium text-green-100">Goal Management</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={() => setOpenMibTargetModal(true)}
                      className="bg-white/20 hover:bg-white/30 text-white border border-white/30 backdrop-blur-sm transition-all duration-200 flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" /> MIB Target
                    </Button>
                    <Button
                      onClick={() => setOpenSalesTargetModal(true)}
                      className="bg-white/20 hover:bg-white/30 text-white border border-white/30 backdrop-blur-sm transition-all duration-200 flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" /> Sales Target
                    </Button>
                    <Button
                      onClick={() => setKpiWizardOpen(true)}
                      className="bg-white text-green-700 hover:bg-gray-50 shadow-lg transition-all duration-200 flex items-center gap-2 font-medium"
                    >
                      <Plus className="h-4 w-4" /> Create KPI
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Modern Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm font-medium">Total Employees</p>
                    <p className="text-3xl font-bold text-white">{employees.length}</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-full">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium">Job Positions</p>
                    <p className="text-3xl font-bold text-white">{jobPositions.length}</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-full">
                    <Briefcase className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-emerald-500 to-emerald-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-emerald-100 text-sm font-medium">Departments</p>
                    <p className="text-3xl font-bold text-white">{departments.length}</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-full">
                    <Target className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm font-medium">Active Records</p>
                    <p className="text-3xl font-bold text-white">
                      {activeTab === "employees" ? getFilteredEmployees().length : getFilteredJobPositions().length}
                    </p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-full">
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Modern Search and Filter Section */}
          <Card className="mb-8 shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search employees, job positions, or departments..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-12 w-full h-12 px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-gray-50 hover:bg-white transition-all duration-200"
                    />
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="relative">
                    <DepartmentFilter
                      selectedDepartment={selectedDepartment}
                      setSelectedDepartment={setSelectedDepartment}
                      departments={departments}
                    />
                  </div>

                  <Button
                    onClick={() => setShowFilters(!showFilters)}
                    variant="outline"
                    className="flex items-center gap-2 h-12 px-4 border-gray-200 hover:bg-gray-50 transition-all duration-200"
                  >
                    <Filter className="h-4 w-4" />
                    Filters
                  </Button>
                </div>
              </div>

              {showFilters && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex flex-wrap gap-2">
                    <span className="text-sm text-gray-600 font-medium">Quick Filters:</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSearchTerm("")}
                      className="h-8 text-xs"
                    >
                      Clear All
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {error ? (
            <div className="p-12 bg-red-50 rounded-lg text-center border border-red-100 shadow-md">
              <CircleAlert className="h-16 w-16 mx-auto text-red-500 mb-4" />
              <p className="text-red-600 text-xl font-semibold mb-2">{error}</p>
              <p className="text-red-500 max-w-md mx-auto mb-6">
                There was a problem retrieving data from the server. Please try
                again later.
              </p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-2 bg-gradient-to-r from-red-500 to-red-600 text-white hover:opacity-90 shadow-md flex items-center gap-2 mx-auto"
              >
                <RefreshCw className="h-4 w-4" /> Retry
              </Button>
            </div>
          ) : (
            <Tabs
              defaultValue="employees"
              value={activeTab}
              onValueChange={setActiveTab}
              className="bg-white rounded-lg shadow-md border border-gray-100 overflow-hidden"
            >
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-2 border-b border-gray-200">
                <TabsList className="grid grid-cols-2 gap-4 bg-transparent">
                  <TabsTrigger
                    value="employees"
                    className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-white data-[state=active]:text-green-700 data-[state=active]:shadow-md rounded-md"
                  >
                    <Users className="h-4 w-4" /> Employees
                  </TabsTrigger>
                  <TabsTrigger
                    value="jobs"
                    className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-white data-[state=active]:text-purple-700 data-[state=active]:shadow-md rounded-md"
                  >
                    <Briefcase className="h-4 w-4" /> Job Positions
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent
                value="employees"
                className="focus-visible:outline-none focus-visible:ring-0 p-6"
              >
                {loading ? (
                  <div className="flex flex-col justify-center items-center h-64">
                    <div className="w-12 h-12 border-4 border-green-500 border-t-transparent rounded-full animate-spin mb-4"></div>
                    <p className="text-green-600 font-medium">
                      Loading employee data...
                    </p>
                  </div>
                ) : getFilteredEmployees().length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-100">
                    <Users className="h-16 w-16 text-gray-300 mb-4" />
                    {searchTerm ? (
                      <p className="text-gray-500 font-medium">
                        No employees found matching your search.
                      </p>
                    ) : (
                      <div className="text-center">
                        <p className="text-gray-600 font-medium text-lg mb-2">
                          No employees found
                        </p>
                        <p className="text-gray-500">
                          Please add employees to the system or check data
                          sources.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="rounded-lg border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader className="bg-gradient-to-r from-green-50 to-indigo-50">
                          <TableRow>
                            <TableHead
                              className="cursor-pointer hover:bg-green-100/50 transition-colors w-12"
                              onClick={() => requestSort("employee_no_id")}
                            >
                              # ID {getSortDirectionIcon("employee_no_id")}
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-green-100/50 transition-colors"
                              onClick={() => requestSort("first_name")}
                            >
                              Name {getSortDirectionIcon("first_name")}
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-green-100/50 transition-colors"
                              onClick={() => requestSort("email")}
                            >
                              Email {getSortDirectionIcon("email")}
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-green-100/50 transition-colors"
                              onClick={() => requestSort("job_code")}
                            >
                              Job Title {getSortDirectionIcon("job_code")}
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-green-100/50 transition-colors"
                              onClick={() => requestSort("department_id")}
                            >
                              Department {getSortDirectionIcon("department_id")}
                            </TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {sortedItems(getPaginatedEmployees(), "employee_no_id").map(
                            (employee) => (
                              <TableRow
                                key={employee.employee_no_id}
                                className="hover:bg-green-50/50 transition-colors cursor-pointer"
                              >
                                <TableCell className="font-medium text-green-700">
                                  {employee.employee_no_id}
                                </TableCell>
                                <TableCell className="flex items-center gap-2">
                                  <div className="bg-green-100 rounded-full p-1.5 flex-shrink-0">
                                    <User className="h-4 w-4 text-green-600" />
                                  </div>
                                  <span>
                                    {employee.first_name || "User"}{" "}
                                    {employee.last_name || "Unknown"}
                                  </span>
                                </TableCell>
                                <TableCell className="text-gray-600">
                                  {employee.email}
                                </TableCell>
                                <TableCell>
                                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-md text-xs font-medium">
                                    {employee.job_title || getJobTitle(employee.job_code || "")}
                                  </span>
                                </TableCell>
                                <TableCell>
                                  <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded-md text-xs font-medium">
                                    {getDepartmentName(employee.department_id)}
                                  </span>
                                </TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    variant="ghost"
                                    className="flex items-center gap-2 text-green-600 hover:text-green-800 hover:bg-green-50"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      fetchEmployeeKpis(
                                        employee.employee_no_id,
                                        `${employee.first_name || "User"} ${employee.last_name || "Unknown"
                                        }`
                                      );
                                    }}
                                  >
                                    <Eye className="h-4 w-4" /> View KPIs
                                  </Button>
                                </TableCell>
                              </TableRow>
                            )
                          )}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Employees Pagination */}
                    <PaginationComponent
                      currentPage={employeesCurrentPage}
                      totalPages={getTotalEmployeePages()}
                      onPageChange={handleEmployeePageChange}
                      totalItems={getFilteredEmployees().length}
                      itemsPerPage={itemsPerPage}
                    />
                  </div>
                )}
              </TabsContent>

              <TabsContent
                value="jobs"
                className="focus-visible:outline-none focus-visible:ring-0 p-6"
              >
                {loading ? (
                  <div className="flex flex-col justify-center items-center h-64">
                    <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-4"></div>
                    <p className="text-purple-600 font-medium">
                      Loading job position data...
                    </p>
                  </div>
                ) : getFilteredJobPositions().length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border border-gray-100">
                    <Briefcase className="h-16 w-16 text-gray-300 mb-4" />
                    {searchTerm ? (
                      <p className="text-gray-500 font-medium">
                        No job positions found matching your search.
                      </p>
                    ) : (
                      <div className="text-center">
                        <p className="text-gray-600 font-medium text-lg mb-2">
                          No job positions found
                        </p>
                        <p className="text-gray-500">
                          Please add job positions to the system or check data
                          sources.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="rounded-lg border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader className="bg-gradient-to-r from-purple-50 to-indigo-50">
                          <TableRow>
                            <TableHead
                              className="cursor-pointer hover:bg-purple-100/50 transition-colors w-32"
                              onClick={() => requestSort("job_code")}
                            >
                              Job Code {getSortDirectionIcon("job_code")}
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-purple-100/50 transition-colors"
                              onClick={() => requestSort("job_title")}
                            >
                              Job Title {getSortDirectionIcon("job_title")}
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-purple-100/50 transition-colors"
                              onClick={() => requestSort("department")}
                            >
                              Department {getSortDirectionIcon("department")}
                            </TableHead>
                            <TableHead>Employees</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {sortedItems(getPaginatedJobPositions(), "job_code").map(
                            (job) => (
                              <TableRow
                                key={job.id}
                                className="hover:bg-purple-50/50 transition-colors cursor-pointer"
                              >
                                <TableCell className="font-medium text-purple-700">
                                  {job.job_code}
                                </TableCell>
                                <TableCell className="flex items-center gap-2">
                                  <div className="bg-purple-100 rounded-full p-1.5 flex-shrink-0">
                                    <Briefcase className="h-4 w-4 text-purple-600" />
                                  </div>
                                  <span>{job.job_title}</span>
                                </TableCell>
                                <TableCell>
                                  <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded-md text-xs font-medium">
                                    {getDepartmentName(job.department)}
                                  </span>
                                </TableCell>
                                <TableCell>
                                  <details className="dropdown">
                                    <summary className="px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-xs font-medium cursor-pointer flex items-center gap-1">
                                      <Users className="h-3 w-3" />
                                      {
                                        getEmployeesForJob(job.job_code).length
                                      }{" "}
                                      Employees
                                      <ChevronRight className="h-3 w-3 transform rotate-90" />
                                    </summary>
                                    <div className="dropdown-content bg-white shadow-lg rounded-md p-2 border border-gray-200 mt-1 min-w-[200px] max-h-[200px] overflow-y-auto z-10">
                                      {getEmployeesForJob(job.job_code).length >
                                        0 ? (
                                        getEmployeesForJob(job.job_code).map(
                                          (emp) => (
                                            <div
                                              key={emp.employee_no_id || emp.employee_no}
                                              className="p-2 hover:bg-purple-50 rounded-md"
                                            >
                                              {emp.first_name} {emp.last_name}
                                            </div>
                                          )
                                        )
                                      ) : (
                                        <div className="p-2 text-gray-500">
                                          No employees assigned
                                        </div>
                                      )}
                                    </div>
                                  </details>
                                </TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    variant="ghost"
                                    className="flex items-center gap-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      fetchJobPositionKpis(job.id, job.job_title);
                                    }}
                                  >
                                    <Eye className="h-4 w-4" /> View KPIs
                                  </Button>
                                </TableCell>
                              </TableRow>
                            )
                          )}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Job Positions Pagination */}
                    <PaginationComponent
                      currentPage={jobsCurrentPage}
                      totalPages={getTotalJobPages()}
                      onPageChange={handleJobPageChange}
                      totalItems={getFilteredJobPositions().length}
                      itemsPerPage={itemsPerPage}
                    />
                  </div>
                )}
              </TabsContent>
            </Tabs>
          )}

          {/* KPI Wizard Modal */}
          <KpiWizardModal
            open={kpiWizardOpen}
            onClose={() => setKpiWizardOpen(false)}
            onSuccess={(message) => {
              setKpiWizardOpen(false);
              showToast(message, "success");
            }}
            token={token}
            employees={employees}
            jobPositions={jobPositions}
          />
          {/* KPI MIBTarget Modal */}
          <MibTargetModal
            open={openMibTargetModal}
            onClose={() => setOpenMibTargetModal(false)}
            onSuccess={(message) => {
              setOpenMibTargetModal(false);
              showToast(message, "success");
            }}
          />
          {/* KPI SalesTarget Modal */}
          <SalesTargetModal
            open={openSalesTargetModal}
            onClose={() => setOpenSalesTargetModal(false)}
            onSuccess={(message) => {
              setOpenSalesTargetModal(false);
              showToast(message, "success");
            }}
          />

          {/* KPI Detail Modal */}
          <KpiDetailModal
            open={kpiDetailOpen}
            onClose={() => setKpiDetailOpen(false)}
            title={
              activeTab === "employees" ? "Employee KPIs" : "Job Position KPIs"
            }
            subtitle={`Viewing KPIs for ${selectedEntityName}`}
            kpis={entityKpis}
            loading={entityKpisLoading}
            token={token}
            onKpiUpdated={() => {
              fetchEmployeeKpis(selectedEntityId, selectedEntityName);
            }}
          />

          {/* Toast Notifications */}
          <Toast
            open={snackbar.open}
            message={snackbar.message}
            severity={snackbar.severity}
            onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
          />
        </div>
      </div>
    </Screen>
  );
};

export default KpiManagementPage;
