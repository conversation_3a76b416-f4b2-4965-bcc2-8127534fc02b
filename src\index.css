@tailwind base;
@tailwind components;
@tailwind utilities;

/* -----------------------------------
   Default: Light Theme
   ----------------------------------- */
:root {
  color-scheme: light;

  /* Global font & rendering */
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Light theme tokens */
  --background: 0 0% 100%; /* White background */
  --foreground: 0 0% 10%; /* Almost black text */
  --card: 0 0% 98%;
  --card-foreground: 0 0% 10%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 10%;
  --primary: 120, 70%, 35%;
  --primary-foreground: 0 0% 10%;
  --secondary: 220 8% 78%;
  --secondary-foreground: 0 0% 10%;
  --muted: 220 8% 90%;
  --muted-foreground: 0 0% 40%;
  --accent: 204 100% 50%;
  --accent-foreground: 0 0% 100%;
  --destructive: 2 63% 51%;
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 80%;
  --input: 0 0% 98%;
  --ring: 0 0% 40%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --radius: 0.5rem;

  /* Sidebar tokens for Light */
  --sidebar-background: 143 89% 19%;
  --sidebar-foreground: 0 0% 100%; 
  --sidebar-muted: 143 88% 31%;
  --sidebar-hover: 143 89% 19%;
  --sidebar-active: 143 89% 19%;
  --sidebar-border: 142 88% 31%; 
  --sidebar-icon: 84 79% 56%; 
  --sidebar-chevron: 60 100% 50%; 
}

/* -----------------------------------
   Dark Theme Override
   ----------------------------------- */
.dark {
  color-scheme: dark;
}

.dark {
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 0 0% 83.1%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --radius: 0.5rem;

  --sidebar-background: 143 89% 19%; 
  --sidebar-foreground: 0 0% 100%; 
  --sidebar-muted: 143 88% 31%; 
  --sidebar-hover: 142 88% 31%; 
  --sidebar-active: 91 65% 46%; 
  --sidebar-border: 142 88% 31%; 
  --sidebar-icon: 84 79% 56%; 
  --sidebar-chevron: 60 100% 50%; 
}

body {
  margin: 0;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #e7e7e7;
  color: #333;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-[hsl(var(--background))] text-[hsl(var(--foreground))];
  }
}

@layer components {
  /* Main sidebar container */
  .sidebar {
    @apply bg-[hsl(var(--sidebar-background))] text-[hsl(var(--sidebar-foreground))];
  }
  
  /* Override any background colors on all menu items */
  .sidebar .sidebar-menu-button,
  .sidebar button[class*="SidebarMenuButton"],
  .sidebar [class*="SidebarMenu"] button {
    @apply bg-[hsl(var(--sidebar-background))] text-[hsl(var(--sidebar-foreground))];
  }
  
  /* Sidebar header and footer borders */
  .sidebar-header, 
  .sidebar-footer {
    @apply border-[hsl(var(--sidebar-border))];
  }
  
  /* All menu items in sidebar */
  .sidebar-menu-button {
    @apply text-[hsl(var(--sidebar-foreground))];
  }
  
  /* Hover and focus states for menu items - more specific selector */
  .sidebar .sidebar-menu-button:hover,
  .sidebar .sidebar-menu-button:focus,
  .sidebar button[class*="SidebarMenuButton"]:hover,
  .sidebar button[class*="SidebarMenuButton"]:focus {
    @apply bg-[hsl(var(--sidebar-hover))];
  }
  /* Active menu item - more specific selector */
  .sidebar .sidebar-menu-button-active,
  .sidebar button[class*="SidebarMenuButton"].active,
  .sidebar [class*="SidebarMenu"] button.active {
    @apply bg-[hsl(var(--sidebar-active))] text-[hsl(var(--sidebar-foreground))];
    @apply text-base;
  }
  
  /* Icons within sidebar */
  .sidebar-icon,
.sidebar .sidebar-menu-button svg,
.sidebar button[class*="SidebarMenuButton"] svg {
  @apply w-6 h-6;
  @apply text-white 
}
  
  /* Chevron/arrow icons */
  .sidebar-group-label {
    @apply text-sm py-4 px-5; 
  }
  /* Group labels */
  .sidebar-group-label {
    @apply text-[hsl(var(--sidebar-muted))];
  }
  
  /* Sub-menu items - ensure they also have purple backgrounds */
  .sidebar-submenu,
  .sidebar [class*="SidebarMenuSub"],
  .sidebar [class*="SidebarMenuSubItem"] {
    @apply bg-[hsl(var(--sidebar-background))];
  }

  /* Sub-menu items */
  .sidebar-submenu {
    @apply bg-[hsl(var(--sidebar-hover))];
    @apply text-base;
  }
}