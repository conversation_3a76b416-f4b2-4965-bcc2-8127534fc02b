import React from 'react';
import { Button } from '@/components/ui/button'; // Shadcn Button
import { AppraisalPeriodCard } from './AppraisalPeriodCard'; // Card component for individual periods

interface AppraisalPeriod {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  cycle_type: 'Monthly' | 'Quarterly' | 'Yearly';
  description: string;
  is_active: boolean;
  created_by: string;
  status?: 'active' | 'upcoming' | 'completed';
}

const EmptyState: React.FC<{ 
  title: string;
  description: string;
  onAction?: () => void;
}> = ({ title, description, onAction }) => (
  <div className="text-center p-6 bg-gray-50 rounded-lg shadow-md">
    <h6 className="text-lg font-semibold text-gray-800">{title}</h6>
    <p className="text-sm text-gray-600">{description}</p>
    {onAction && (
      <Button 
        variant="default" 
        onClick={onAction} 
        className="mt-4 px-6 py-3 rounded-full text-white"
      >
        Create New Period
      </Button>
    )}
  </div>
);

interface AppraisalPeriodListProps {
  periods: AppraisalPeriod[];
  onView: (id: string) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export const AppraisalPeriodList: React.FC<AppraisalPeriodListProps> = ({
  periods,
  onView,
  onEdit,
  onDelete
}) => {
  return (
    <div className="py-8">
      {periods.length === 0 ? (
        <EmptyState
          title="No Appraisal Periods Found"
          description="Create a new appraisal period to get started with performance evaluations"
          onAction={() => console.log('Create new period')}
        />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {periods.map((period) => (
            <div key={period.id} className="transition-all duration-300 ease-in-out transform hover:scale-105">
              <AppraisalPeriodCard
                period={period}
                onView={onView}
                onEdit={onEdit}
                onDelete={onDelete}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
