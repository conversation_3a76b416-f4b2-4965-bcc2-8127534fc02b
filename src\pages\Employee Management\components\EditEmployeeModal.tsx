"use client";

import React, { useEffect, useState } from "react";
import {
  X,
  MapPin,
  Mail,
  Phone,
  User,
  FileText,
  DollarSign,
  Calendar,
  Briefcase,
  Users,
  Shield,
  UserCheck,
  Gavel,
} from "lucide-react";
import { useSelector } from "react-redux";
import axios from "axios";
import { RootState } from "../../../redux/store";

// New Radix-based Dialog components
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from "@/components/ui/dialog";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Alert } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { InfoChip } from "./InfoChip";

import {
  EmployeeBioDetails,
  EmployeeContactInfo,
  EmployeeContractInfo,
  EmployeeImportantDates,
  JobInfo,
  EmployeePaymentInfo,
  EmployeeGroups,
  EmployeePermissions,
  EmployeeRole,
  WarningRecord,
} from "../../../types/employeeTypes";

import { BASE_URL } from "@/config";

interface EditEmployeeModalProps {
  open: boolean;
  onClose: () => void;
  employeeNo: string;
}

const EditEmployeeModal: React.FC<EditEmployeeModalProps> = ({
  open,
  onClose,
  employeeNo,
}) => {
  const token = useSelector((state: RootState) => state.auth.token);

  // Use string for tab value
  const [tabValue, setTabValue] = useState<string>("0");

  // Editable state for each section
  const [bio, setBio] = useState<EmployeeBioDetails | null>(null);
  const [contact, setContact] = useState<EmployeeContactInfo | null>(null);
  const [contractInfo, setContractInfo] = useState<EmployeeContractInfo | null>(null);
  const [importantDates, setImportantDates] = useState<EmployeeImportantDates | null>(null);
  const [jobInfo, setJobInfo] = useState<JobInfo | null>(null);
  const [paymentInfo, setPaymentInfo] = useState<EmployeePaymentInfo | null>(null);
  const [groups, setGroups] = useState<EmployeeGroups | null>(null);
  const [permissions, setPermissions] = useState<EmployeePermissions | null>(null);
  const [role, setRole] = useState<EmployeeRole | null>(null);
  const [warningList, setWarningList] = useState<WarningRecord[]>([]);

  // Snackbar state for messages
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    variant: "success" as "success" | "error" | "info" | "warning",
  });

  useEffect(() => {
    if (open) {
      fetchAllData();
    }
    // Cleanup when modal closes
    return () => {
      setBio(null);
      setContact(null);
      setContractInfo(null);
      setImportantDates(null);
      setJobInfo(null);
      setPaymentInfo(null);
      setGroups(null);
      setPermissions(null);
      setRole(null);
      setWarningList([]);
    };
  }, [open, employeeNo]);

  const fetchAllData = async () => {
    try {
      // Fetch Bio details
      const bioRes = await axios.get<EmployeeBioDetails[]>(
        `${BASE_URL}/users/employee-bio-details`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      if (bioRes.data.length > 0) setBio(bioRes.data[0]);

      // Fetch Contact info
      const contactRes = await axios.get<EmployeeContactInfo[]>(
        `${BASE_URL}/users/employee-contact-info-details`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      if (contactRes.data.length > 0) setContact(contactRes.data[0]);

      // Fetch Contract info
      const contractRes = await axios.get<EmployeeContractInfo[]>(
        `${BASE_URL}/users/employee-contract-info-details`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      if (contractRes.data.length > 0) setContractInfo(contractRes.data[0]);

      // Fetch Important Dates
      const datesRes = await axios.get<EmployeeImportantDates[]>(
        `${BASE_URL}/users/employee-important-dates-details`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      if (datesRes.data.length > 0) setImportantDates(datesRes.data[0]);

      // Fetch Job Info
      const jobRes = await axios.get<JobInfo[]>(
        `${BASE_URL}/users/employee-job-info-details`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      if (jobRes.data.length > 0) setJobInfo(jobRes.data[0]);

      // Fetch Payment Info
      const payRes = await axios.get<EmployeePaymentInfo[]>(
        `${BASE_URL}/users/employee-payment-info-details`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      if (payRes.data.length > 0) setPaymentInfo(payRes.data[0]);

      // Fetch Groups (read-only)
      const groupRes = await axios.get<EmployeeGroups[]>(
        `${BASE_URL}/users/employee-group-details`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      if (groupRes.data.length > 0) setGroups(groupRes.data[0]);

      // Fetch Permissions (read-only)
      const permRes = await axios.get<EmployeePermissions[]>(
        `${BASE_URL}/users/employee-permissions`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      if (permRes.data.length > 0) setPermissions(permRes.data[0]);

      // Fetch Role (read-only)
      const roleRes = await axios.get<EmployeeRole[]>(
        `${BASE_URL}/users/employee-roles`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      if (roleRes.data.length > 0) setRole(roleRes.data[0]);

      // Fetch Warnings (read-only)
      const warnRes = await axios.get<WarningRecord[]>(
        `${BASE_URL}/users/employee-warnings`,
        {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        }
      );
      setWarningList(warnRes.data);
    } catch (error) {
      console.error("Error fetching employee data for Edit:", error);
      setSnackbar({
        open: true,
        message: "Failed to fetch employee data for Edit.",
        variant: "error",
      });
    }
  };

  // Tab handler expects a string value
  const handleTabChange = (newValue: string) => {
    setTabValue(newValue);
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Update implementation using PATCH for each editable section
  const handleSave = async () => {
    try {
      const patchRequests = [];

      if (bio && bio.id) {
        patchRequests.push(
          axios.patch(
            `${BASE_URL}/users/employee-bio-details/${bio.id}`,
            bio,
            { headers: { Authorization: `Token ${token}` } }
          )
        );
      }
      if (contact && contact.id) {
        patchRequests.push(
          axios.patch(
            `${BASE_URL}/users/employee-contact-info-details/${contact.id}`,
            contact,
            { headers: { Authorization: `Token ${token}` } }
          )
        );
      }
      if (contractInfo && contractInfo.id) {
        patchRequests.push(
          axios.patch(
            `${BASE_URL}/users/employee-contract-info-details/${contractInfo.id}`,
            contractInfo,
            { headers: { Authorization: `Token ${token}` } }
          )
        );
      }
      if (importantDates && importantDates.id) {
        patchRequests.push(
          axios.patch(
            `${BASE_URL}/users/employee-important-dates-details/${importantDates.id}`,
            importantDates,
            { headers: { Authorization: `Token ${token}` } }
          )
        );
      }
      if (jobInfo && jobInfo.id) {
        patchRequests.push(
          axios.patch(
            `${BASE_URL}/users/employee-job-info-details/${jobInfo.id}`,
            jobInfo,
            { headers: { Authorization: `Token ${token}` } }
          )
        );
      }
      if (paymentInfo && paymentInfo.id) {
        patchRequests.push(
          axios.patch(
            `${BASE_URL}/users/employee-payment-info-details/${paymentInfo.id}`,
            paymentInfo,
            { headers: { Authorization: `Token ${token}` } }
          )
        );
      }

      // Wait for all PATCH requests to complete
      await Promise.all(patchRequests);

      setSnackbar({
        open: true,
        message: "Employee data updated successfully.",
        variant: "success",
      });
      onClose();
    } catch (error) {
      console.error("Error updating employee data:", error);
      setSnackbar({
        open: true,
        message: "Failed to update employee data.",
        variant: "error",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl">
        {/* HERO AREA */}
        <div className="relative bg-primary h-44" />

        {/* AVATAR & BASIC INFO */}
        <div className="flex justify-center -mt-16">
          <div className="relative">
            <Avatar className="w-24 h-24 border-4 border-white">
                          <img
                            src="https://images.unsplash.com/photo-1531727991582-cfd25ce79613"
                            alt="Employee Avatar"
                            className="w-full h-full object-cover rounded-full"
                          />
                        </Avatar>
            <div className="absolute bottom-0 right-0 bg-secondary text-white rounded-full px-2 text-xs">
              {employeeNo}
            </div>
          </div>
        </div>

        <DialogHeader className="text-center mt-2">
          <DialogTitle>
            {bio ? `${bio.first_name} ${bio.last_name}` : "Employee"}
          </DialogTitle>
          <div className="flex items-center justify-center gap-2">
            <Badge variant="secondary">Active</Badge>
            <p className="text-sm text-muted-foreground">
              {jobInfo?.job_title || "Employee Position"}
            </p>
          </div>
        </DialogHeader>

        {/* BASIC INFO CHIPS */}
        <div className="flex flex-wrap justify-center gap-2 mb-4">
          <InfoChip
            icon={<MapPin className="w-4 h-4" />}
            label={contact?.city || "No City"}
          />
          <InfoChip
            icon={<Mail className="w-4 h-4" />}
            label={contact?.company_email || "No Email"}
          />
          <InfoChip
            icon={<Phone className="w-4 h-4" />}
            label={contact?.home_phone_number || "No Phone"}
          />
        </div>

        {/* TABS */}
        <Tabs value={tabValue} onValueChange={handleTabChange} className="border-b">
          <TabsList className="w-full justify-center">
            <TabsTrigger value="0">
              <User className="w-4 h-4 mr-1" /> Bio
            </TabsTrigger>
            <TabsTrigger value="1">
              <Mail className="w-4 h-4 mr-1" /> Contact
            </TabsTrigger>
            <TabsTrigger value="2">
              <FileText className="w-4 h-4 mr-1" /> Contract
            </TabsTrigger>
            <TabsTrigger value="3">
              <DollarSign className="w-4 h-4 mr-1" /> Payment
            </TabsTrigger>
            <TabsTrigger value="4">
              <Calendar className="w-4 h-4 mr-1" /> Dates
            </TabsTrigger>
            <TabsTrigger value="5">
              <Briefcase className="w-4 h-4 mr-1" /> Job
            </TabsTrigger>
            <TabsTrigger value="6">
              <Users className="w-4 h-4 mr-1" /> Groups
            </TabsTrigger>
            <TabsTrigger value="7">
              <Shield className="w-4 h-4 mr-1" /> Permissions
            </TabsTrigger>
            <TabsTrigger value="8">
              <UserCheck className="w-4 h-4 mr-1" /> Role
            </TabsTrigger>
            <TabsTrigger value="9">
              <Gavel className="w-4 h-4 mr-1" /> Disciplinary
            </TabsTrigger>
          </TabsList>

          {/* TAB 0: Editable Bio Details */}
          <TabsContent value="0">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Bio Details</h3>
              {bio ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex flex-col gap-1">
                    <Label>First Name</Label>
                    <Input
                      value={bio.first_name}
                      onChange={(e) => setBio({ ...bio, first_name: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Middle Name</Label>
                    <Input
                      value={bio.middle_name || ""}
                      onChange={(e) => setBio({ ...bio, middle_name: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Last Name</Label>
                    <Input
                      value={bio.last_name}
                      onChange={(e) => setBio({ ...bio, last_name: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Gender</Label>
                    <Input
                      value={bio.gender || ""}
                      onChange={(e) => setBio({ ...bio, gender: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Marital Status</Label>
                    <Input
                      value={bio.marital_status || ""}
                      onChange={(e) => setBio({ ...bio, marital_status: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Date of Birth</Label>
                    <Input
                      value={bio.date_of_birth || ""}
                      onChange={(e) => setBio({ ...bio, date_of_birth: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Passport Number</Label>
                    <Input
                      value={bio.passport_number || ""}
                      onChange={(e) => setBio({ ...bio, passport_number: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>ID Number</Label>
                    <Input
                      value={bio.id_number || ""}
                      onChange={(e) =>
                        setBio({ ...bio, id_number: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Date of First Appointment</Label>
                    <Input
                      type="date"
                      value={bio.date_of_first_appointment || ""}
                      onChange={(e) =>
                        setBio({ ...bio, date_of_first_appointment: e.target.value })
                      }
                    />
                  </div>
                </div>
              ) : (
                <p>No Bio record found.</p>
              )}
            </div>
          </TabsContent>

          {/* TAB 1: Editable Contact Info */}
          <TabsContent value="1">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Contact Info</h3>
              {contact ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col gap-1">
                    <Label>Postal Address</Label>
                    <Input
                      value={contact.postal_address || ""}
                      onChange={(e) => setContact({ ...contact, postal_address: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Home Phone</Label>
                    <Input
                      value={contact.home_phone_number || ""}
                      onChange={(e) => setContact({ ...contact, home_phone_number: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Residential Address</Label>
                    <Input
                      value={contact.residential_address || ""}
                      onChange={(e) => setContact({ ...contact, residential_address: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>City</Label>
                    <Input
                      value={contact.city || ""}
                      onChange={(e) => setContact({ ...contact, city: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>County</Label>
                    <Input
                      value={contact.county || ""}
                      onChange={(e) => setContact({ ...contact, county: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Company Email</Label>
                    <Input
                      value={contact.company_email || ""}
                      onChange={(e) => setContact({ ...contact, company_email: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Country</Label>
                    <Input
                      value={contact.country || ""}
                      onChange={(e) => setContact({ ...contact, country: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Personal Email</Label>
                    <Input
                      value={contact.personal_email || ""}
                      onChange={(e) => setContact({ ...contact, personal_email: e.target.value })}
                    />
                  </div>
                </div>
              ) : (
                <p>No Contact record found.</p>
              )}
            </div>
          </TabsContent>

          {/* TAB 2: Editable Contract Info */}
          <TabsContent value="2">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Contract Info</h3>
              {contractInfo ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col gap-1">
                    <Label>Contract Type</Label>
                    <Input
                      value={contractInfo.contract_type || ""}
                      onChange={(e) =>
                        setContractInfo({ ...contractInfo, contract_type: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Contract Start Date</Label>
                    <Input
                      value={contractInfo.contract_start_date || ""}
                      onChange={(e) =>
                        setContractInfo({ ...contractInfo, contract_start_date: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Current Contract End</Label>
                    <Input
                      value={contractInfo.current_contract_end || ""}
                      onChange={(e) =>
                        setContractInfo({ ...contractInfo, current_contract_end: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>End of Probation Date</Label>
                    <Input
                      value={contractInfo.end_of_probation_date || ""}
                      onChange={(e) =>
                        setContractInfo({ ...contractInfo, end_of_probation_date: e.target.value })
                      }
                    />
                  </div>
                </div>
              ) : (
                <p>No Contract record found.</p>
              )}
            </div>
          </TabsContent>

          {/* TAB 3: Editable Payment Info */}
          <TabsContent value="3">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Payment Info</h3>
              {paymentInfo ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex flex-col gap-1">
                    <Label>Pension Scheme Join</Label>
                    <Input
                      value={paymentInfo.pension_scheme_join || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, pension_scheme_join: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Salary</Label>
                    <Input
                      value={paymentInfo.salary?.toString() || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, salary: parseFloat(e.target.value) || 0 })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Bonus</Label>
                    <Input
                      value={paymentInfo.bonus?.toString() || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, bonus: parseFloat(e.target.value) || 0 })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Bank Name</Label>
                    <Input
                      value={paymentInfo.bank_name || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, bank_name: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Account Number</Label>
                    <Input
                      value={paymentInfo.account_number || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, account_number: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Payment Frequency</Label>
                    <Input
                      value={paymentInfo.payment_frequency || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, payment_frequency: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>KRA Pin</Label>
                    <Input
                      value={paymentInfo.KRA_pin || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, KRA_pin: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>NHIF SHIF Number</Label>
                    <Input
                      value={paymentInfo.NHIF_SHIF_number || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, NHIF_SHIF_number: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>NSSF Number</Label>
                    <Input
                      value={paymentInfo.NSSF_number || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, NSSF_number: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>HELB Number</Label>
                    <Input
                      value={paymentInfo.HELB_number || ""}
                      onChange={(e) =>
                        setPaymentInfo({ ...paymentInfo, HELB_number: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Tax Status</Label>
                    <Input
                      value={paymentInfo.tax_status ? "Yes" : "No"}
                      onChange={(e) =>
                        setPaymentInfo({
                          ...paymentInfo,
                          tax_status: e.target.value.toLowerCase() === "yes",
                        })
                      }
                    />
                  </div>
                </div>
              ) : (
                <p>No Payment record found.</p>
              )}
            </div>
          </TabsContent>

          {/* TAB 4: Editable Important Dates */}
          <TabsContent value="4">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Important Dates</h3>
              {importantDates ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col gap-1">
                    <Label>Date of Current Appointment *</Label>
                    <Input
                      type="date"
                      value={importantDates.date_of_current_appointment || ""}
                      onChange={(e) =>
                        setImportantDates({
                          ...importantDates,
                          date_of_current_appointment: e.target.value,
                        })
                      }
                      required
                    />
                    <p className="text-xs text-muted-foreground">
                      The date when the employee was appointed to their current position
                    </p>
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Date of Leaving</Label>
                    <Input
                      type="date"
                      value={importantDates.date_of_leaveing || ""}
                      onChange={(e) =>
                        setImportantDates({ ...importantDates, date_of_leaveing: e.target.value || null })
                      }
                    />
                    <p className="text-xs text-muted-foreground">
                      The date when the employee left or will leave the organization (optional)
                    </p>
                  </div>
                </div>
              ) : (
                <p>No Important Dates record found.</p>
              )}
            </div>
          </TabsContent>

          {/* TAB 5: Editable Job Info */}
          <TabsContent value="5">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Job Info</h3>
              {jobInfo ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col gap-1">
                    <Label>Category</Label>
                    <Input
                      value={jobInfo.category || ""}
                      onChange={(e) =>
                        setJobInfo({ ...jobInfo, category: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Directorate</Label>
                    <Input
                      value={jobInfo.directorate || ""}
                      onChange={(e) =>
                        setJobInfo({ ...jobInfo, directorate: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Job Title Code</Label>
                    <Input
                      value={jobInfo.job_title_code || ""}
                      onChange={(e) =>
                        setJobInfo({ ...jobInfo, job_title_code: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Job Title</Label>
                    <Input
                      value={jobInfo.job_title || ""}
                      onChange={(e) =>
                        setJobInfo({ ...jobInfo, job_title: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Teams Code</Label>
                    <Input
                      value={jobInfo.teams_code || ""}
                      onChange={(e) =>
                        setJobInfo({ ...jobInfo, teams_code: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Department ID</Label>
                    <Input
                      value={jobInfo.department?.toString() || ""}
                      onChange={(e) =>
                        setJobInfo({
                          ...jobInfo,
                          department: parseInt(e.target.value, 10) || 0,
                        })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Work Location</Label>
                    <Input
                      value={jobInfo.work_location || ""}
                      onChange={(e) =>
                        setJobInfo({ ...jobInfo, work_location: e.target.value })
                      }
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Business Unit</Label>
                    <Input
                      value={jobInfo.business_unit || ""}
                      onChange={(e) =>
                        setJobInfo({ ...jobInfo, business_unit: e.target.value })
                      }
                    />
                  </div>
                </div>
              ) : (
                <p>No Job Info found.</p>
              )}
            </div>
          </TabsContent>

          {/* TAB 6: Groups (read-only) */}
          <TabsContent value="6">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Groups</h3>
              {groups ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col gap-1">
                    <Label>Group Name</Label>
                    <Input value={groups.name || ""} disabled />
                  </div>
                </div>
              ) : (
                <p>No Group record found.</p>
              )}
            </div>
          </TabsContent>

          {/* TAB 7: Permissions (read-only) */}
          <TabsContent value="7">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Permissions</h3>
              {permissions ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col gap-1">
                    <Label>Permission Name</Label>
                    <Input value={permissions.permission_name || ""} disabled />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Label>Permission Description</Label>
                    <Input value={permissions.permission_description || ""} disabled />
                  </div>
                </div>
              ) : (
                <p>No Permissions record found.</p>
              )}
            </div>
          </TabsContent>

          {/* TAB 8: Role (read-only) */}
          <TabsContent value="8">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Role</h3>
              {role ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col gap-1">
                    <Label>Role ID</Label>
                    <Input value={role.role?.toString() || ""} disabled />
                  </div>
                </div>
              ) : (
                <p>No Role record found.</p>
              )}
            </div>
          </TabsContent>

          {/* TAB 9: Disciplinary & Warnings (read-only) */}
          <TabsContent value="9">
            <div className="p-4 border rounded-md m-4">
              <h3 className="text-lg font-semibold mb-2">Disciplinary & Warnings</h3>
              <Separator className="mb-2" />
              {warningList.length > 0 ? (
                warningList.map((warn) => (
                  <div key={warn.id} className="border p-4 mb-2 rounded">
                    <p className="font-semibold">Warning #{warn.id}</p>
                    <p>Date: {warn.date}</p>
                    <p>Reason: {warn.reason}</p>
                    <Badge variant="outline" className="mt-1">
                      {warn.acknowledged ? "Acknowledged" : "Unacknowledged"}
                    </Badge>
                  </div>
                ))
              ) : (
                <p>No warnings issued.</p>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4 flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>

        {snackbar.open && (
          <Alert variant="destructive">
            {snackbar.message}
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-2"
              onClick={handleCloseSnackbar}
            >
              <X className="h-4 w-4" />
            </Button>
          </Alert>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditEmployeeModal;
