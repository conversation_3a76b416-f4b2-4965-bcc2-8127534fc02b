"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useNavigate } from "react-router-dom";
import { BASE_URL } from "@/config";
import { useDebounce } from "@/hooks/use-debounce";
import { useTheme } from "@/hooks/use-theme";

// Icons
import {
  Search,
  Calendar as CalendarIcon,
  Filter,
  X,
  Download,
  RefreshCw,
  User,
  Briefcase,
  MapPin,
  FileText
} from "lucide-react";

// Shadcn/UI components
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Enumerations
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";

interface EmployeeReport {
  employee_no: string;
  email: string;
  first_name: string;
  last_name: string;
  gender: string;
  date_of_birth: string;
  age?: string;
  marital_status: string;
  date_of_first_appointment: string;
  job_title: string;
  work_location: string;
  contract_type: string;
  current_contract_end: string;
  leaving_date: string;
}

interface EmployeeReportsFilters {
  gender?: string;
  marital_status?: string;
  contract_type?: string;
  job_title?: string;
  work_location?: string;
  date_of_first_appointment?: string;
  leaving_date?: string;
  current_contract_end?: string;
  min_age?: string;
  max_age?: string;
  search?: string;
  ordering?: string;
}
const GENDER_OPTIONS = ["m", "f", "o"];
const MARITAL_STATUS_OPTIONS = ["Single", "Married", "Divorced", "Widowed"];
const CONTRACT_TYPE_OPTIONS = [
  "Permanent","Temporary","Internship","Consultant","Contractor","Volunteer","Probation",
];
// Badge variants for filter pills
const BADGE_VARIANTS = [
  "bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100",
  "bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100",
  "bg-yellow-100 text-yellow-900 dark:bg-yellow-900 dark:text-yellow-100",
  "bg-red-100 text-red-900 dark:bg-red-900 dark:text-red-100",
  "bg-purple-100 text-purple-900 dark:bg-purple-900 dark:text-purple-100",
  "bg-pink-100 text-pink-900 dark:bg-pink-900 dark:text-pink-100",
  "bg-orange-100 text-orange-900 dark:bg-orange-900 dark:text-orange-100",
];

export default function EmployeeReports() {
  const { token } = useSelector((s: RootState) => s.auth);
  const navigate = useNavigate();
  const { theme } = useTheme();

  // State
  const [filters, setFilters] = useState<EmployeeReportsFilters>({});
  const [firstAppointmentDate, setFirstAppointmentDate] = useState<Date>();
  const [contractEndDate, setContractEndDate] = useState<Date>();
  const [leavingDate, setLeavingDate] = useState<Date>();
  const [employees, setEmployees] = useState<EmployeeReport[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);

  // Debounce the filters to prevent excessive API calls
  const debouncedFilters = useDebounce(filters, 500);

  // Build query parameters from filters
  const buildQueryParams = useCallback((params: EmployeeReportsFilters) => {
    const qp = new URLSearchParams();
    Object.entries(params).forEach(([k, v]) => v && qp.append(k, v));
    return qp.toString();
  }, []);

  // Fetch employees data
  const fetchEmployees = useCallback(async (filterParams: EmployeeReportsFilters) => {
    if (!token) return setError("Missing token. Please log in.");
    setLoading(true);
    setError(null);
    try {
      const qs = buildQueryParams(filterParams);
      const res = await fetch(
        `${BASE_URL}/reports/employees/${qs ? `?${qs}` : ""}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        }
      );
      if (!res.ok) {
        const txt = await res.text();
        throw new Error(
          txt.startsWith("<!DOCTYPE")
            ? "Server returned HTML. Check endpoint/auth."
            : `Error ${res.status}: ${txt}`
        );
      }
      setEmployees(await res.json());
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  }, [token, buildQueryParams]);

  // Auto-fetch when debounced filters change
  useEffect(() => {
    if (Object.keys(debouncedFilters).length > 0) {
      fetchEmployees(debouncedFilters);
    }
  }, [debouncedFilters, fetchEmployees]);

  // Filter management functions
  const setFilterValue = useCallback((k: keyof EmployeeReportsFilters, v?: string) => {
    setFilters((prev) => ({ ...prev, [k]: v || "" }));
  }, []);

  const removeFilter = useCallback((k: keyof EmployeeReportsFilters) => {
    setFilters((prev) => {
      const copy = { ...prev };
      delete copy[k];
      return copy;
    });
  }, []);

  // Handle date filter changes
  useEffect(() => {
    if (firstAppointmentDate) {
      setFilterValue(
        "date_of_first_appointment",
        firstAppointmentDate.toISOString().slice(0, 10)
      );
    } else {
      removeFilter("date_of_first_appointment");
    }
  }, [firstAppointmentDate, setFilterValue, removeFilter]);

  useEffect(() => {
    if (contractEndDate) {
      setFilterValue(
        "current_contract_end",
        contractEndDate.toISOString().slice(0, 10)
      );
    } else {
      removeFilter("current_contract_end");
    }
  }, [contractEndDate, setFilterValue, removeFilter]);

  useEffect(() => {
    if (leavingDate) {
      setFilterValue("leaving_date", leavingDate.toISOString().slice(0, 10));
    } else {
      removeFilter("leaving_date");
    }
  }, [leavingDate, setFilterValue, removeFilter]);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setFilters({});
    setEmployees([]);
    setError(null);
    setFirstAppointmentDate(undefined);
    setContractEndDate(undefined);
    setLeavingDate(undefined);
  }, []);

  // Export data to CSV (mock function)
  const handleExportData = useCallback(() => {
    if (employees.length === 0) return;

    setIsExporting(true);
    setTimeout(() => {
      setIsExporting(false);
      // In a real implementation, you would generate and download a CSV here
      alert("Export functionality would download a CSV file in a real implementation");
    }, 1000);
  }, [employees]);

  return (
    <Screen>
      {/* Header with breadcrumb and title */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <Breadcrumb className="mb-2">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/employees-list">Employees</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Reports</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <h1 className="text-2xl font-bold tracking-tight">Employee Reports</h1>
          <p className="text-muted-foreground">Generate and filter employee reports</p>
        </div>

        <div className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                  className="h-9"
                >
                  <RefreshCw size={16} className="mr-1" />
                  Reset
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Clear all filters</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={handleExportData}
                  disabled={employees.length === 0 || isExporting}
                  className="h-9"
                >
                  {isExporting ? (
                    <RefreshCw size={16} className="mr-1 animate-spin" />
                  ) : (
                    <Download size={16} className="mr-1" />
                  )}
                  Export
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export data to CSV</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            onClick={() => navigate("/employees-list")}
            className="h-9"
          >
            Back to Employees
          </Button>
        </div>
      </div>

      {/* Main content area */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters panel - 1 column on mobile, 1/4 on large screens */}
        <Card className="lg:col-span-1 h-fit">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Filters</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="h-8 px-2 text-muted-foreground"
              >
                <X size={16} className="mr-1" />
                Clear all
              </Button>
            </div>
            <CardDescription>
              Filters apply automatically
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-5">
            {/* Active filters */}
            {Object.entries(filters).filter(([_, v]) => !!v).length > 0 && (
              <div className="space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Active Filters</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(filters)
                    .filter(([_, v]) => !!v)
                    .map(([k, v], i) => (
                      <Badge
                        key={k}
                        className={`${BADGE_VARIANTS[i % BADGE_VARIANTS.length]} cursor-pointer transition-all hover:opacity-80`}
                        onClick={() => removeFilter(k as any)}
                      >
                        {k.replace(/_/g, ' ')}: {v}
                        <X size={12} className="ml-1" />
                      </Badge>
                    ))}
                </div>
              </div>
            )}

            {/* Search */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name, email..."
                  value={filters.search || ""}
                  onChange={(e) => setFilterValue("search", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Gender filter */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Gender</Label>
              <div className="flex flex-wrap gap-2">
                {GENDER_OPTIONS.map((gender) => (
                  <Badge
                    key={gender}
                    variant={filters.gender === gender ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => setFilterValue("gender", filters.gender === gender ? undefined : gender)}
                  >
                    {gender === "m" ? "Male" : gender === "f" ? "Female" : "Other"}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Marital Status */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Marital Status</Label>
              <Select
                value={filters.marital_status || "all"}
                onValueChange={(value) => setFilterValue("marital_status", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  {MARITAL_STATUS_OPTIONS.map((status) => (
                    <SelectItem key={status} value={status}>{status}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Contract Type */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Contract Type</Label>
              <Select
                value={filters.contract_type || "all"}
                onValueChange={(value) => setFilterValue("contract_type", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All contract types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All contract types</SelectItem>
                  {CONTRACT_TYPE_OPTIONS.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Job & Location */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Job Title</Label>
              <div className="relative">
                <Briefcase className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Filter by job title"
                  value={filters.job_title || ""}
                  onChange={(e) => setFilterValue("job_title", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Work Location</Label>
              <div className="relative">
                <MapPin className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Filter by location"
                  value={filters.work_location || ""}
                  onChange={(e) => setFilterValue("work_location", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Date filters */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Date Filters</Label>

              <div className="grid grid-cols-1 gap-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={`w-full justify-start text-left font-normal ${!firstAppointmentDate && "text-muted-foreground"}`}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {firstAppointmentDate ? (
                        firstAppointmentDate.toLocaleDateString()
                      ) : (
                        "First Appointment Date"
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={firstAppointmentDate}
                      onSelect={setFirstAppointmentDate}
                      initialFocus
                    />
                    {firstAppointmentDate && (
                      <div className="p-3 border-t border-border">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setFirstAppointmentDate(undefined)}
                          className="w-full"
                        >
                          <X size={14} className="mr-1" />
                          Clear date
                        </Button>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={`w-full justify-start text-left font-normal ${!contractEndDate && "text-muted-foreground"}`}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {contractEndDate ? (
                        contractEndDate.toLocaleDateString()
                      ) : (
                        "Contract End Date"
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={contractEndDate}
                      onSelect={setContractEndDate}
                      initialFocus
                    />
                    {contractEndDate && (
                      <div className="p-3 border-t border-border">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setContractEndDate(undefined)}
                          className="w-full"
                        >
                          <X size={14} className="mr-1" />
                          Clear date
                        </Button>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={`w-full justify-start text-left font-normal ${!leavingDate && "text-muted-foreground"}`}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {leavingDate ? (
                        leavingDate.toLocaleDateString()
                      ) : (
                        "Leaving Date"
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={leavingDate}
                      onSelect={setLeavingDate}
                      initialFocus
                    />
                    {leavingDate && (
                      <div className="p-3 border-t border-border">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setLeavingDate(undefined)}
                          className="w-full"
                        >
                          <X size={14} className="mr-1" />
                          Clear date
                        </Button>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results panel - 1 column on mobile, 3/4 on large screens */}
        <Card className="lg:col-span-3">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Employee Report</CardTitle>
              <Badge variant="outline" className="font-normal">
                {loading ? (
                  <span className="flex items-center">
                    <RefreshCw size={12} className="mr-1 animate-spin" /> Loading...
                  </span>
                ) : employees.length > 0 ? (
                  `${employees.length} employees found`
                ) : (
                  "No results"
                )}
              </Badge>
            </div>
            <CardDescription>
              {Object.keys(filters).length > 0
                ? "Showing filtered results"
                : "Apply filters to see employee data"}
            </CardDescription>
          </CardHeader>

          <CardContent>
            {error && (
              <div className="mb-4 p-3 bg-destructive/10 text-destructive rounded-md">
                <p className="text-sm">{error}</p>
              </div>
            )}

            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="font-medium">ID</TableHead>
                      <TableHead className="font-medium">Name</TableHead>
                      <TableHead className="font-medium">Email</TableHead>
                      <TableHead className="font-medium">Gender</TableHead>
                      <TableHead className="font-medium">Age</TableHead>
                      <TableHead className="font-medium">Marital</TableHead>
                      <TableHead className="font-medium">Job Title</TableHead>
                      <TableHead className="font-medium">Location</TableHead>
                      <TableHead className="font-medium">Contract</TableHead>
                      <TableHead className="font-medium">End Date</TableHead>
                      <TableHead className="font-medium">Leaving</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      // Loading skeleton
                      Array(5).fill(0).map((_, i) => (
                        <TableRow key={i}>
                          {Array(11).fill(0).map((_, j) => (
                            <TableCell key={j}>
                              <Skeleton className="h-4 w-full" />
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : employees.length > 0 ? (
                      // Employee data
                      employees.map((emp) => (
                        <TableRow key={emp.employee_no} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{emp.employee_no}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <User size={16} className="mr-2 text-muted-foreground" />
                              {emp.first_name} {emp.last_name}
                            </div>
                          </TableCell>
                          <TableCell>{emp.email}</TableCell>
                          <TableCell>
                            {emp.gender === 'm' ? 'Male' : emp.gender === 'f' ? 'Female' : 'Other'}
                          </TableCell>
                          <TableCell>{emp.age ?? "-"}</TableCell>
                          <TableCell>{emp.marital_status}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Briefcase size={14} className="mr-1 text-muted-foreground" />
                              {emp.job_title}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <MapPin size={14} className="mr-1 text-muted-foreground" />
                              {emp.work_location}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="font-normal">
                              {emp.contract_type}
                            </Badge>
                          </TableCell>
                          <TableCell>{emp.current_contract_end || "-"}</TableCell>
                          <TableCell>{emp.leaving_date || "-"}</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      // No results
                      <TableRow>
                        <TableCell colSpan={11} className="h-24 text-center">
                          <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <FileText size={24} className="mb-2" />
                            <p>No employee records found</p>
                            <p className="text-sm">Try adjusting your filters</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between border-t p-4">
            <p className="text-sm text-muted-foreground">
              {employees.length > 0 && "Showing all available results"}
            </p>

           
          </CardFooter>
        </Card>
      </div>
    </Screen>
  );
}
