"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>ooter, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>lider } from "@/components/ui/slider";

import { VacancyApplication, InterviewQuestion } from "../types";

interface RateCandidateDialogProps {
  open: boolean;
  onClose: () => void;
  candidateApp: VacancyApplication | null;
  questions: InterviewQuestion[];
  existingScores: { [questionId: number]: number };
  onSave: (updatedScores: { [questionId: number]: number }) => void;
}

const RateCandidateDialog: React.FC<RateCandidateDialogProps> = ({
  open,
  onClose,
  candidateApp,
  questions,
  existingScores,
  onSave,
}) => {
  const [localScores, setLocalScores] = useState<{ [qid: number]: number }>({});

  useEffect(() => {
    setLocalScores(existingScores || {});
  }, [existingScores, open]);

  if (!candidateApp) return null;

  const handleSliderChange = (questionId: number, value: number) => {
    setLocalScores((prev) => ({ ...prev, [questionId]: value }));
  };

  const handleConfirm = () => {
    onSave(localScores);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle>Rate Candidate</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {questions.map((q) => {
            const val = localScores[q.id] ?? 1;
            return (
              <div key={q.id} className="border p-2 rounded">
                <p className="font-medium">{q.question}</p>
                <Slider
                  className="mt-2"
                  value={[val]}
                  min={1}
                  max={10}
                  step={1}
                  onValueChange={(arr) => handleSliderChange(q.id, arr[0])}
                />
                <p className="text-sm mt-1">Rating: {val}</p>
              </div>
            );
          })}
        </div>
        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleConfirm}>Save Ratings</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RateCandidateDialog;
