"use client"

import { Screen } from "@/app-components/layout/screen"
import ResourceSourcing from "../../app-components/training/ResourceSourcing"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { BASE_URL } from "@/config"
import { useSelector } from "react-redux"
import { RootState } from "@/redux/store"

export default function SourcingPage() {
  const handleSource = async (resources: { name: string; location: string }[]) => {
    try {
      for (const resource of resources) {
        const response = await fetch(`${BASE_URL}/hrm/training-content-details`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          }, 
          body: JSON.stringify({
            title: resource.name,
            description: resource.location,
            // status: "pending" or "active",
            // category: some ID if needed
          }),
        })
        if (!response.ok) {
          throw new Error(`Failed to save resource: ${resource.name}`)
        }
      }
      console.log("Resources sourced successfully")
    } catch (error) {
      console.error("Error sourcing resources:", error)
    }
  }

  const token = useSelector((state: RootState) => state.auth.token);

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Resource Sourcing
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  )

  return (
    <Screen headerContent={breadcrumb}>
      <div>
        <ResourceSourcing onSource={handleSource} />
      </div>
    </Screen>
  )
}
