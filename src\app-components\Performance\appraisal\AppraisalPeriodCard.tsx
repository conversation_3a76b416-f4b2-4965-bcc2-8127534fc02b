import React, {useState} from 'react';
import { Button } from '@/components/ui/button'; // Shadcn Button
import { Card, CardContent, CardFooter } from '@/components/ui/card'; // Shadcn Card components
import { CheckCircle, Calendar, Eye } from 'lucide-react'; // Lucide icons
import { useNavigate } from 'react-router-dom';
// Types and backend interface
interface AppraisalPeriod {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  cycle_type: 'Monthly' | 'Quarterly' | 'Yearly';
  description: string;
  is_active: boolean;
  created_by: string;
  status?: 'active' | 'upcoming' | 'completed';
  progress?: number;
}

interface AppraisalPeriodCardProps {
  period: AppraisalPeriod;
  onView: (id: string) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

// Date formatter utility
const formatDate = (dateString: string): string => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

export const AppraisalPeriodCard: React.FC<AppraisalPeriodCardProps> = ({
  period
}) => {
  const navigate = useNavigate();

  const calculateProgress = () => {
    const start = new Date(period.start_date).getTime();
    const end = new Date(period.end_date).getTime();
    const now = Date.now();

    if (now < start) return 0;
    if (now > end) return 100;

    return ((now - start) / (end - start)) * 100;
  };

  const handleViewDetails = () => {
    navigate(`/performance-dashboard?period=${period.id}`);
  }
  return (
    <Card className="relative p-6 bg-white rounded-lg shadow-lg transition-all duration-300 ease-in-out transform hover:scale-105">
      {/* Card Content */}
      <CardContent>
        {/* Status Chips */}
        <div className="flex gap-2 mb-4">
          <Button
            variant="outline"
            className="text-primary capitalize px-3 py-1 rounded-full text-xs font-medium"
          >
            {period.cycle_type}
          </Button>
          {period.is_active && (
            <Button variant="outline" className="bg-green-200 text-green-600 flex items-center text-xs font-semibold py-1 px-3 rounded-full">
              <CheckCircle size={14} className="mr-1" />
              Active
            </Button>
          )}
        </div>

        {/* Main Content */}
        <div className="text-lg font-semibold mb-2 text-gray-800">{period.name}</div>

        <div className="flex items-center gap-1 mb-3 text-sm text-gray-500">
          <Calendar size={16} className="text-gray-600" />
          <span>{formatDate(period.start_date)} - {formatDate(period.end_date)}</span>
        </div>

        <p className="text-sm text-gray-600 mb-4">{period.description}</p>

        {/* Progress Bar */}
        <div className="w-full h-2 bg-gray-300 rounded-full overflow-hidden mb-4">
          <div
            className="h-full bg-green-500"
            style={{ width: `${calculateProgress()}%` }}
          />
        </div>

        <div className="text-sm text-gray-500 mt-1">Progress: {Math.round(calculateProgress())}%</div>
      </CardContent>
      
      {/* View Details Button */}
      <CardFooter className="pt-4">
        <Button 
          onClick={handleViewDetails} 
          className="w-full flex items-center justify-center bg-primary text-white"
          variant="outline"
        >
          <Eye size={16} className="mr-2" /> View Appraisal Details
        </Button>
      </CardFooter>
    </Card>
  );
};
