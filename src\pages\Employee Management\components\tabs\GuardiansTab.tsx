import React, { useEffect, useState } from "react";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { EmployeeParentGuardian } from "../../types/EmployeeTypes";
import { TabLayout, FormGrid, FormField } from "../TabLayout";
import { 
  Shield, 
  UserPlus, 
  Phone, 
  MapPin, 
  Calendar, 
  Briefcase, 
  Edit, 
  Trash2, 
  Save,
  X,
  Heart,
  HeartOff
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

import { BASE_URL } from "@/config";

interface GuardiansTabProps {
  employeeNo: string;
  onSaveSuccess?: () => void;
}

const relationshipOptions = [
  { value: 'Father', label: 'Father' },
  { value: 'Mother', label: 'Mother' },
  { value: 'Guardian', label: 'Guardian' },
  { value: 'Step-Father', label: 'Step-Father' },
  { value: 'Step-Mother', label: 'Step-Mother' },
];

export default function GuardiansTab({ employeeNo, onSaveSuccess }: GuardiansTabProps) {
  const token = useSelector((state: RootState) => state.auth.token);
  const [guardians, setGuardians] = useState<EmployeeParentGuardian[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [newGuardian, setNewGuardian] = useState<Omit<EmployeeParentGuardian, 'id'>>({
    full_name: '',
    relationship: 'Father',
    date_of_birth: null,
    occupation: null,
    phone_number_or_email: null,
    address: null,
    is_alive: true,
    employee_no: employeeNo,
  });

  // Fetch existing guardians
  useEffect(() => {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }
    fetchGuardians();
  }, [employeeNo, token]);

  const fetchGuardians = async () => {
    setLoading(true);
    try {
      const headers = { Authorization: `Token ${token}` };
      const params = { employee_no: employeeNo };
      const res = await axios.get(`${BASE_URL}/users/employee-guardians`, { headers, params });
      
      if (Array.isArray(res.data)) {
        const employeeGuardians = res.data.filter((guardian: EmployeeParentGuardian) => 
          guardian.employee_no === employeeNo
        );
        setGuardians(employeeGuardians);
      } else {
        setGuardians([]);
      }
    } catch (err) {
      console.error("Error fetching guardians:", err);
      toast.error("Failed to fetch guardian data");
      setGuardians([]);
    } finally {
      setLoading(false);
    }
  };

  // Create new guardian
  const handleCreate = async () => {
    if (!newGuardian.full_name.trim()) {
      toast.error("Full name is required");
      return;
    }

    try {
      const headers = { Authorization: `Token ${token}` };
      const payload = { ...newGuardian, employee_no: employeeNo };
      
      const res = await axios.post(`${BASE_URL}/users/employee-guardians`, payload, { headers });
      
      setGuardians(prev => [...prev, res.data]);
      setNewGuardian({
        full_name: '',
        relationship: 'Father',
        date_of_birth: null,
        occupation: null,
        phone_number_or_email: null,
        address: null,
        is_alive: true,
        employee_no: employeeNo,
      });
      setIsCreating(false);
      toast.success("Guardian added successfully");
      
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (err) {
      console.error("Error creating guardian:", err);
      toast.error("Failed to add guardian");
    }
  };

  // Update guardian
  const handleUpdate = async (id: number, updatedData: Partial<EmployeeParentGuardian>) => {
    try {
      const headers = { Authorization: `Token ${token}` };
      const res = await axios.patch(`${BASE_URL}/users/employee-guardians/${id}`, updatedData, { headers });
      
      setGuardians(prev => prev.map(guardian => 
        guardian.id === id ? res.data : guardian
      ));
      setEditingId(null);
      toast.success("Guardian updated successfully");
      
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (err) {
      console.error("Error updating guardian:", err);
      toast.error("Failed to update guardian");
    }
  };

  // Delete guardian
  const handleDelete = async (id: number) => {
    if (!window.confirm("Are you sure you want to delete this guardian?")) {
      return;
    }

    try {
      const headers = { Authorization: `Token ${token}` };
      await axios.delete(`${BASE_URL}/users/employee-guardians/${id}`, { headers });
      
      setGuardians(prev => prev.filter(guardian => guardian.id !== id));
      toast.success("Guardian deleted successfully");
      
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (err) {
      console.error("Error deleting guardian:", err);
      toast.error("Failed to delete guardian");
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  const getRelationshipColor = (relationship: string) => {
    switch (relationship) {
      case 'Father': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'Mother': return 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300';
      case 'Guardian': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'Step-Father': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300';
      case 'Step-Mother': return 'bg-rose-100 text-rose-800 dark:bg-rose-900/30 dark:text-rose-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  return (
    <TabLayout
      title="Parent & Guardian Information"
      description="Manage parent and guardian details for the employee"
      employeeNo={employeeNo}
      loading={loading}
      actions={
        <Button
          onClick={() => setIsCreating(true)}
          className="flex items-center gap-2"
          disabled={isCreating}
        >
          <UserPlus className="h-4 w-4" />
          Add Guardian
        </Button>
      }
    >
      {/* Create New Guardian Form */}
      {isCreating && (
        <Card className="mb-6 border-primary/20 bg-primary/5">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <UserPlus className="h-5 w-5" />
              Add New Guardian
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FormGrid columns={2}>
              <FormField>
                <Label>Full Name *</Label>
                <Input
                  value={newGuardian.full_name}
                  onChange={(e) => setNewGuardian(prev => ({ ...prev, full_name: e.target.value }))}
                  placeholder="Enter full name"
                />
              </FormField>
              <FormField>
                <Label>Relationship *</Label>
                <Select
                  value={newGuardian.relationship}
                  onValueChange={(value) => setNewGuardian(prev => ({ 
                    ...prev, 
                    relationship: value as EmployeeParentGuardian['relationship']
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {relationshipOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormField>
              <FormField>
                <Label>Date of Birth</Label>
                <Input
                  type="date"
                  value={newGuardian.date_of_birth || ''}
                  onChange={(e) => setNewGuardian(prev => ({ 
                    ...prev, 
                    date_of_birth: e.target.value || null 
                  }))}
                />
              </FormField>
              <FormField>
                <Label>Occupation</Label>
                <Input
                  value={newGuardian.occupation || ''}
                  onChange={(e) => setNewGuardian(prev => ({ 
                    ...prev, 
                    occupation: e.target.value || null 
                  }))}
                  placeholder="Enter occupation"
                />
              </FormField>
              <FormField>
                <Label>Phone Number or Email</Label>
                <Input
                  value={newGuardian.phone_number_or_email || ''}
                  onChange={(e) => setNewGuardian(prev => ({ 
                    ...prev, 
                    phone_number_or_email: e.target.value || null 
                  }))}
                  placeholder="Enter phone or email"
                />
              </FormField>
              <FormField>
                <Label className="flex items-center gap-2">
                  Is Alive
                  <div className="flex items-center gap-2">
                    {newGuardian.is_alive ? (
                      <Heart className="h-4 w-4 text-green-600" />
                    ) : (
                      <HeartOff className="h-4 w-4 text-gray-400" />
                    )}
                    <Switch
                      checked={newGuardian.is_alive}
                      onCheckedChange={(checked) => setNewGuardian(prev => ({ 
                        ...prev, 
                        is_alive: checked 
                      }))}
                    />
                  </div>
                </Label>
              </FormField>
            </FormGrid>
            <FormField className="mt-4">
              <Label>Address</Label>
              <Textarea
                value={newGuardian.address || ''}
                onChange={(e) => setNewGuardian(prev => ({ 
                  ...prev, 
                  address: e.target.value || null 
                }))}
                placeholder="Enter address"
                rows={3}
              />
            </FormField>
            <div className="flex gap-2 mt-6">
              <Button onClick={handleCreate} className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                Save Guardian
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsCreating(false);
                  setNewGuardian({
                    full_name: '',
                    relationship: 'Father',
                    date_of_birth: null,
                    occupation: null,
                    phone_number_or_email: null,
                    address: null,
                    is_alive: true,
                    employee_no: employeeNo,
                  });
                }}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Guardians List */}
      <div className="space-y-4">
        {guardians.length === 0 ? (
          <div className="text-center p-8 border border-dashed rounded-lg bg-muted/20">
            <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">No guardians added yet</p>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => setIsCreating(true)}
              className="flex items-center gap-2"
            >
              <UserPlus className="h-4 w-4" />
              Add First Guardian
            </Button>
          </div>
        ) : (
          guardians.map((guardian) => (
            <GuardianCard
              key={guardian.id}
              guardian={guardian}
              isEditing={editingId === guardian.id}
              onEdit={() => guardian.id ? setEditingId(guardian.id) : null}
              onSave={(updatedData) => handleUpdate(guardian.id!, updatedData)}
              onCancel={() => setEditingId(null)}
              onDelete={() => handleDelete(guardian.id!)}
              getRelationshipColor={getRelationshipColor}
              formatDate={formatDate}
            />
          ))
        )}
      </div>
    </TabLayout>
  );
}

// Guardian Card Component
interface GuardianCardProps {
  guardian: EmployeeParentGuardian;
  isEditing: boolean;
  onEdit: () => void;
  onSave: (data: Partial<EmployeeParentGuardian>) => void;
  onCancel: () => void;
  onDelete: () => void;
  getRelationshipColor: (relationship: string) => string;
  formatDate: (date: string | null) => string;
}

function GuardianCard({ 
  guardian, 
  isEditing, 
  onEdit, 
  onSave, 
  onCancel, 
  onDelete,
  getRelationshipColor,
  formatDate
}: GuardianCardProps) {
  const [editData, setEditData] = useState<EmployeeParentGuardian>(guardian);

  useEffect(() => {
    setEditData(guardian);
  }, [guardian, isEditing]);

  const handleSave = () => {
    if (!editData.full_name.trim()) {
      toast.error("Full name is required");
      return;
    }
    onSave(editData);
  };

  if (isEditing) {
    return (
      <Card className="border-primary/20 bg-primary/5">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Edit className="h-5 w-5" />
            Edit Guardian
          </CardTitle>
        </CardHeader>
        <CardContent>
          <FormGrid columns={2}>
            <FormField>
              <Label>Full Name *</Label>
              <Input
                value={editData.full_name}
                onChange={(e) => setEditData(prev => ({ ...prev, full_name: e.target.value }))}
                placeholder="Enter full name"
              />
            </FormField>
            <FormField>
              <Label>Relationship *</Label>
              <Select
                value={editData.relationship}
                onValueChange={(value) => setEditData(prev => ({ 
                  ...prev, 
                  relationship: value as EmployeeParentGuardian['relationship']
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {relationshipOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>
            <FormField>
              <Label>Date of Birth</Label>
              <Input
                type="date"
                value={editData.date_of_birth || ''}
                onChange={(e) => setEditData(prev => ({ 
                  ...prev, 
                  date_of_birth: e.target.value || null 
                }))}
              />
            </FormField>
            <FormField>
              <Label>Occupation</Label>
              <Input
                value={editData.occupation || ''}
                onChange={(e) => setEditData(prev => ({ 
                  ...prev, 
                  occupation: e.target.value || null 
                }))}
                placeholder="Enter occupation"
              />
            </FormField>
            <FormField>
              <Label>Phone Number or Email</Label>
              <Input
                value={editData.phone_number_or_email || ''}
                onChange={(e) => setEditData(prev => ({ 
                  ...prev, 
                  phone_number_or_email: e.target.value || null 
                }))}
                placeholder="Enter phone or email"
              />
            </FormField>
            <FormField>
              <Label className="flex items-center gap-2">
                Is Alive
                <div className="flex items-center gap-2">
                  {editData.is_alive ? (
                    <Heart className="h-4 w-4 text-green-600" />
                  ) : (
                    <HeartOff className="h-4 w-4 text-gray-400" />
                  )}
                  <Switch
                    checked={editData.is_alive}
                    onCheckedChange={(checked) => setEditData(prev => ({ 
                      ...prev, 
                      is_alive: checked 
                    }))}
                  />
                </div>
              </Label>
            </FormField>
          </FormGrid>
          <FormField className="mt-4">
            <Label>Address</Label>
            <Textarea
              value={editData.address || ''}
              onChange={(e) => setEditData(prev => ({ 
                ...prev, 
                address: e.target.value || null 
              }))}
              placeholder="Enter address"
              rows={3}
            />
          </FormField>
          <div className="flex gap-2 mt-6">
            <Button onClick={handleSave} className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              Save Changes
            </Button>
            <Button variant="outline" onClick={onCancel} className="flex items-center gap-2">
              <X className="h-4 w-4" />
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all hover:shadow-md">
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
              <Shield className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-lg">{guardian.full_name}</h3>
              <Badge className={getRelationshipColor(guardian.relationship)}>
                {guardian.relationship}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {guardian.is_alive ? (
              <Heart className="h-5 w-5 text-green-600" />
            ) : (
              <HeartOff className="h-5 w-5 text-gray-400" />
            )}
            <Button size="sm" variant="ghost" onClick={onEdit}>
              <Edit className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="ghost" onClick={onDelete} className="text-red-500 hover:text-red-700">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-xs text-muted-foreground">Date of Birth</p>
              <p className="font-medium">{formatDate(guardian.date_of_birth ?? null)}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Briefcase className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-xs text-muted-foreground">Occupation</p>
              <p className="font-medium">{guardian.occupation || '-'}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-xs text-muted-foreground">Contact</p>
              <p className="font-medium">{guardian.phone_number_or_email || '-'}</p>
            </div>
          </div>
        </div>

        {guardian.address && (
          <>
            <Separator className="my-4" />
            <div className="flex items-start gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
              <div>
                <p className="text-xs text-muted-foreground">Address</p>
                <p className="font-medium">{guardian.address}</p>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
