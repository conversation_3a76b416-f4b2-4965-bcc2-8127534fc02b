"use client";

import { useState, useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";

import { BASE_URL } from "@/config";
import { RootState } from "@/redux/store";
import { Screen } from "@/app-components/layout/screen";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandList,
  CommandInput,
  CommandItem,
  CommandEmpty,
  CommandGroup,
} from "@/components/ui/command";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import {
  Users,
  Calendar,
  Plus,
  Minus,
  CheckCircle,
  AlertCircle,
  Loader2,
  ChevronDown,
  Search,
  Clock,
  TrendingUp,
  TrendingDown,
} from "lucide-react";

// ---------------------- Interfaces ----------------------

interface EmployeeBio {
  id: number;
  employee_no: string;
  first_name: string;
  middle_name: string;
  last_name: string;
}

interface LeaveTypeItem {
  id: number;
  leave_type_name: string;
}

interface LeaveBalanceItem {
  id: number;
  LeaveBalance: number;
  employee_no: string;
  LeaveType_id: number;
  // ... any other fields
}

// ---------------------- Zod Schema ----------------------

const leaveClawbackSchema = z.object({
  employee: z.string().nonempty("Select an employee."),
  leaveType: z.string().nonempty("Select a leave type."),
  operation: z.enum(["add", "subtract"], {
    errorMap: () => ({ message: "Operation must be 'add' or 'subtract'." }),
  }),
  days: z.preprocess(
    (val) => (val === "" ? undefined : Number(val)),
    z
      .number({
        invalid_type_error: "Enter number of days.",
        required_error: "Enter number of days.",
      })
      .positive("Days must be a positive number.")
  ),
});

type LeaveClawbackFormData = z.infer<typeof leaveClawbackSchema>;

// ---------------------- Component ----------------------

export default function LeaveClawbackPage() {
  const navigate = useNavigate();
  const { token } = useSelector((state: RootState) => state.auth);

  const [employees, setEmployees] = useState<EmployeeBio[]>([]);
  const [leaveTypes, setLeaveTypes] = useState<LeaveTypeItem[]>([]);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalanceItem[]>([]);

  const [employeeOpen, setEmployeeOpen] = useState(false);
  const [employeeSearch, setEmployeeSearch] = useState("");
  const [employeeDisplay, setEmployeeDisplay] = useState("");

  const [leaveTypeOpen, setLeaveTypeOpen] = useState(false);
  const [leaveTypeSearch, setLeaveTypeSearch] = useState("");
  const [leaveTypeDisplay, setLeaveTypeDisplay] = useState("");

  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogTitle, setDialogTitle] = useState("");
  const [dialogDescription, setDialogDescription] = useState("");
  const [dialogAction, setDialogAction] = useState<() => void>(() => {});

  const [submitting, setSubmitting] = useState(false);

  const showAlertDialog = (
    title: string,
    description: string,
    action?: () => void
  ) => {
    setDialogTitle(title);
    setDialogDescription(description);
    setDialogAction(() => (action ? action : () => {}));
    setDialogOpen(true);
  };

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<LeaveClawbackFormData>({
    resolver: zodResolver(leaveClawbackSchema),
    defaultValues: {
      employee: "",
      leaveType: "",
      operation: "add",
      days: "",
    },
  });

  const watchEmployee = watch("employee");
  const watchLeaveType = watch("leaveType");
  const watchOperation = watch("operation");

  // ---------------------- Data Fetching ----------------------

  useEffect(() => {
    if (!token) return;
    fetchEmployees();
    fetchLeaveTypes();
    fetchAllLeaveBalances();
  }, [token]);

  async function fetchEmployees() {
    try {
      const res = await fetch(`${BASE_URL}/users/employee-bio-details`, {
        headers: { Authorization: `Token ${token}` },
      });
      if (!res.ok) throw new Error("Failed to fetch employees");
      const data = (await res.json()) as EmployeeBio[];
      setEmployees(data);
    } catch (error) {
      console.error("Error fetching employees:", error);
    }
  }

  async function fetchLeaveTypes() {
    try {
      const res = await fetch(`${BASE_URL}/hrm/leave-type-details`, {
        headers: { Authorization: `Token ${token}` },
      });
      if (!res.ok) throw new Error("Failed to fetch leave types");
      const data = (await res.json()) as LeaveTypeItem[];
      setLeaveTypes(data);
    } catch (error) {
      console.error("Error fetching leave types:", error);
    }
  }

  async function fetchAllLeaveBalances() {
    try {
      const res = await fetch(`${BASE_URL}/hrm/leave-balance`, {
        headers: { Authorization: `Token ${token}` },
      });
      if (!res.ok) throw new Error("Failed to fetch all leave balances");
      const data = (await res.json()) as LeaveBalanceItem[];
      setLeaveBalances(data);
    } catch (error) {
      console.error("Error fetching all leave balances:", error);
    }
  }

  // ---------------------- Helpers ----------------------

  const filteredEmployees = useMemo(() => {
    const term = employeeSearch.toLowerCase();
    return employees.filter((emp) => {
      const fullName =
        `${emp.first_name} ${emp.middle_name} ${emp.last_name}`.toLowerCase();
      return (
        fullName.includes(term) || emp.employee_no.toLowerCase().includes(term)
      );
    });
  }, [employees, employeeSearch]);

  const filteredLeaveTypes = useMemo(() => {
    const term = leaveTypeSearch.toLowerCase();
    return leaveTypes.filter((lt) =>
      lt.leave_type_name.toLowerCase().includes(term)
    );
  }, [leaveTypes, leaveTypeSearch]);

  // Find the existing leave balance for the selected employee + leave type
  const selectedBalance = useMemo(() => {
    if (!watchEmployee || !watchLeaveType) return null;

    const eNo = watchEmployee; // employee_no
    const typeName = watchLeaveType;

    const typeObj = leaveTypes.find((lt) => lt.leave_type_name === typeName);
    if (!typeObj) return null;

    const found = leaveBalances.find(
      (b) => b.employee_no === eNo && b.LeaveType_id === typeObj.id
    );
    return found || null;
  }, [watchEmployee, watchLeaveType, leaveBalances, leaveTypes]);

  // If we have a selectedBalance, let's show the current # of days
  const currentDays = selectedBalance?.LeaveBalance ?? null;
  const leaveBalanceId = selectedBalance?.id;

  // ---------------------- Submit Logic ----------------------

  async function onSubmit(data: LeaveClawbackFormData) {
    if (!leaveBalanceId) {
      showAlertDialog(
        "Error",
        "Could not find a matching leave balance for the chosen employee and leave type."
      );
      return;
    }

    setSubmitting(true);

    const floatDays = parseFloat(data.days);
    if (isNaN(floatDays) || floatDays <= 0) {
      showAlertDialog("Error", "Number of days must be a positive number.");
      setSubmitting(false);
      return;
    }

    // Prepare request
    const requestBody = {
      id: leaveBalanceId,
      days: floatDays,
      operation: data.operation,
    };

    try {
      const res = await fetch(
        `${BASE_URL}/hrm/leave-balance/days-manual-update/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        const errorMsg =
          errorData?.error ||
          errorData?.detail ||
          "Failed to manually update the leave balance";
        throw new Error(errorMsg);
      }

      const updated = await res.json();
      // Re-fetch all balances or update in place
      await fetchAllLeaveBalances();

      showAlertDialog("Success", "Leave balance updated successfully!", () => {
        // optional: navigate or just keep user on the same page
        navigate("/leave-clawback");
        // Or you can stay on the same page and reset:
        // reset()
        // setEmployeeDisplay("")
        // setLeaveTypeDisplay("")
      });
    } catch (error: any) {
      showAlertDialog(
        "Error",
        error.message || "Failed to update leave balance."
      );
    } finally {
      setSubmitting(false);
    }
  }

  // ---------------------- Rendering ----------------------

  return (
    <>
      <Screen headerContent>
        <div className="container mx-auto p-6 space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Form Card */}
            <div className="lg:col-span-2">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Leave Clawback
                  </h1>
                  <p className="text-sm text-muted-foreground">
                    Manually adjust employee leave balances
                  </p>
                </div>
              </div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-gradient-to-br from-green-500 to-emerald-600">
                        <Users className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-semibold">
                          Manual Leave Days Update
                        </CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                          Add or subtract leave days from employee balances
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <form
                      onSubmit={handleSubmit(onSubmit)}
                      className="space-y-6"
                    >
                      {/* Employee Selection */}
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 }}
                        className="space-y-3"
                      >
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-blue-600" />
                          <Label
                            htmlFor="employeeSelect"
                            className="text-sm font-medium"
                          >
                            Select Employee
                          </Label>
                        </div>
                        <Popover
                          open={employeeOpen}
                          onOpenChange={setEmployeeOpen}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={employeeOpen}
                              className="w-full justify-between h-12 px-4 bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-blue-300 transition-all duration-200"
                            >
                              <div className="flex items-center gap-2">
                                {employeeDisplay ? (
                                  <>
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span className="text-gray-900">
                                      {employeeDisplay}
                                    </span>
                                  </>
                                ) : (
                                  <>
                                    <Search className="h-4 w-4 text-gray-400" />
                                    <span className="text-gray-500">
                                      Search and select an employee...
                                    </span>
                                  </>
                                )}
                              </div>
                              <ChevronDown className="h-4 w-4 text-gray-400" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0 shadow-xl border-0">
                            <Command className="rounded-lg">
                              <CommandInput
                                placeholder="Search employees..."
                                value={employeeSearch}
                                onValueChange={setEmployeeSearch}
                                className="border-0 focus:ring-0"
                              />
                              <CommandList className="max-h-64">
                                <CommandEmpty className="py-6 text-center text-gray-500">
                                  <Users className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                                  No employees found.
                                </CommandEmpty>
                                <CommandGroup>
                                  {filteredEmployees.map((emp) => {
                                    const fullName = `${emp.first_name} ${emp.middle_name} ${emp.last_name}`;
                                    return (
                                      <CommandItem
                                        key={emp.id}
                                        onSelect={() => {
                                          setEmployeeDisplay(
                                            `${fullName} (${emp.employee_no})`
                                          );
                                          setEmployeeOpen(false);
                                          setValue("employee", emp.employee_no);
                                        }}
                                        className="flex items-center gap-3 p-3 hover:bg-blue-50 cursor-pointer"
                                      >
                                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                          {emp.first_name?.charAt(0) ?? ""}
                                          {emp.middle_name?.charAt(0) ?? ""}
                                          {emp.last_name?.charAt(0) ?? ""}
                                        </div>
                                        <div>
                                          <div className="font-medium text-gray-900">
                                            {fullName}
                                          </div>
                                          <div className="text-sm text-gray-500">
                                            ID: {emp.employee_no}
                                          </div>
                                        </div>
                                      </CommandItem>
                                    );
                                  })}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        {errors.employee?.message && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 text-red-600 text-sm"
                          >
                            <AlertCircle className="h-4 w-4" />
                            {String(errors.employee.message)}
                          </motion.div>
                        )}
                      </motion.div>

                      {/* Leave Type Selection */}
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.2 }}
                        className="space-y-3"
                      >
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-green-600" />
                          <Label
                            htmlFor="leaveTypeSelect"
                            className="text-sm font-medium"
                          >
                            Leave Type
                          </Label>
                        </div>
                        <Popover
                          open={leaveTypeOpen}
                          onOpenChange={setLeaveTypeOpen}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={leaveTypeOpen}
                              className="w-full justify-between h-12 px-4 bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-green-300 transition-all duration-200"
                            >
                              <div className="flex items-center gap-2">
                                {leaveTypeDisplay ? (
                                  <>
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span className="text-gray-900">
                                      {leaveTypeDisplay}
                                    </span>
                                  </>
                                ) : (
                                  <>
                                    <Calendar className="h-4 w-4 text-gray-400" />
                                    <span className="text-gray-500">
                                      Select leave type...
                                    </span>
                                  </>
                                )}
                              </div>
                              <ChevronDown className="h-4 w-4 text-gray-400" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0 shadow-xl border-0">
                            <Command className="rounded-lg">
                              <CommandInput
                                placeholder="Search leave types..."
                                value={leaveTypeSearch}
                                onValueChange={setLeaveTypeSearch}
                                className="border-0 focus:ring-0"
                              />
                              <CommandList className="max-h-64">
                                <CommandEmpty className="py-6 text-center text-gray-500">
                                  <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                                  No leave types found.
                                </CommandEmpty>
                                <CommandGroup>
                                  {filteredLeaveTypes.map((lt) => (
                                    <CommandItem
                                      key={lt.id}
                                      onSelect={() => {
                                        setLeaveTypeDisplay(lt.leave_type_name);
                                        setLeaveTypeOpen(false);
                                        setValue(
                                          "leaveType",
                                          lt.leave_type_name
                                        );
                                      }}
                                      className="flex items-center gap-3 p-3 hover:bg-green-50 cursor-pointer"
                                    >
                                      <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                        {lt.leave_type_name.charAt(0)}
                                      </div>
                                      <span className="font-medium text-gray-900">
                                        {lt.leave_type_name}
                                      </span>
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        {errors.leaveType?.message && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 text-red-600 text-sm"
                          >
                            <AlertCircle className="h-4 w-4" />
                            {String(errors.leaveType.message)}
                          </motion.div>
                        )}
                      </motion.div>

                      {/* Operation Selection */}
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                        className="space-y-3"
                      >
                        <div className="flex items-center gap-2">
                          {watchOperation === "add" ? (
                            <Plus className="h-4 w-4 text-green-600" />
                          ) : (
                            <Minus className="h-4 w-4 text-red-600" />
                          )}
                          <Label
                            htmlFor="operationSelect"
                            className="text-sm font-medium"
                          >
                            Operation
                          </Label>
                        </div>
                        <Select
                          value={watchOperation}
                          onValueChange={(value) =>
                            setValue("operation", value as "add" | "subtract")
                          }
                        >
                          <SelectTrigger className="h-12 bg-white border-2 border-gray-200 hover:border-purple-300 transition-all duration-200">
                            <SelectValue placeholder="Select operation" />
                          </SelectTrigger>
                          <SelectContent className="shadow-xl border-0">
                            <SelectItem
                              value="add"
                              className="hover:bg-green-50"
                            >
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                                  <Plus className="h-4 w-4 text-white" />
                                </div>
                                <div>
                                  <div className="font-medium">Add Days</div>
                                  <div className="text-sm text-gray-500">
                                    Increase leave balance
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                            <SelectItem
                              value="subtract"
                              className="hover:bg-red-50"
                            >
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center">
                                  <Minus className="h-4 w-4 text-white" />
                                </div>
                                <div>
                                  <div className="font-medium">
                                    Subtract Days
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    Decrease leave balance
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.operation?.message && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 text-red-600 text-sm"
                          >
                            <AlertCircle className="h-4 w-4" />
                            {String(errors.operation.message)}
                          </motion.div>
                        )}
                      </motion.div>

                      {/* Number of Days */}
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                        className="space-y-3"
                      >
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-purple-600" />
                          <Label htmlFor="days" className="text-sm font-medium">
                            Number of Days
                          </Label>
                        </div>
                        <Input
                          type="number"
                          id="days"
                          placeholder="Enter number of days"
                          step={0.01} 
                          min={0.01}
                          className="h-12 bg-white border-2 border-gray-200 hover:border-purple-300 focus:border-purple-500 transition-all duration-200"
                          {...register("days")}
                        />
                        {errors.days?.message && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 text-red-600 text-sm"
                          >
                            <AlertCircle className="h-4 w-4" />
                            {String(errors.days.message)}
                          </motion.div>
                        )}
                      </motion.div>

                      {/* Current Balance Display */}
                      <AnimatePresence>
                        {selectedBalance && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.95 }}
                            transition={{ delay: 0.5 }}
                            className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl"
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                                <CheckCircle className="h-5 w-5 text-white" />
                              </div>
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  Current Balance
                                </div>
                                <div className="text-lg font-bold text-blue-600">
                                  {currentDays} days
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>

                      {/* Submit Button */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6 }}
                        className="pt-4"
                      >
                        <Button
                          type="submit"
                          disabled={submitting}
                          className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
                        >
                          {submitting ? (
                            <div className="flex items-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Updating Balance...
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              {watchOperation === "add" ? (
                                <TrendingUp className="h-4 w-4" />
                              ) : (
                                <TrendingDown className="h-4 w-4" />
                              )}
                              Update Balance
                            </div>
                          )}
                        </Button>
                      </motion.div>
                    </form>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Info Sidebar */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="space-y-6"
              >
                {/* Quick Stats */}
                {/* <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-semibold flex items-center gap-2">
                      <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600">
                        <TrendingUp className="h-4 w-4 text-white" />
                      </div>
                      Quick Info
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="text-sm font-medium text-blue-900">Total Employees</div>
                      <div className="text-2xl font-bold text-blue-600">{employees.length}</div>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="text-sm font-medium text-green-900">Leave Types</div>
                      <div className="text-2xl font-bold text-green-600">{leaveTypes.length}</div>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                      <div className="text-sm font-medium text-purple-900">Total Balances</div>
                      <div className="text-2xl font-bold text-purple-600">{leaveBalances.length}</div>
                    </div>
                  </CardContent>
                </Card> */}

                {/* Instructions */}
                <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-semibold flex items-center gap-2">
                      <div className="p-2 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600">
                        <AlertCircle className="h-4 w-4 text-white" />
                      </div>
                      Instructions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-start gap-3">
                      <Badge variant="outline" className="mt-0.5">
                        1
                      </Badge>
                      <p className="text-sm text-gray-600">
                        Select an employee from the dropdown
                      </p>
                    </div>
                    <div className="flex items-start gap-3">
                      <Badge variant="outline" className="mt-0.5">
                        2
                      </Badge>
                      <p className="text-sm text-gray-600">
                        Choose the leave type to modify
                      </p>
                    </div>
                    <div className="flex items-start gap-3">
                      <Badge variant="outline" className="mt-0.5">
                        3
                      </Badge>
                      <p className="text-sm text-gray-600">
                        Select add or subtract operation
                      </p>
                    </div>
                    <div className="flex items-start gap-3">
                      <Badge variant="outline" className="mt-0.5">
                        4
                      </Badge>
                      <p className="text-sm text-gray-600">
                        Enter the number of days to adjust
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </div>
      </Screen>

      {/* Enhanced Alert Dialog */}
      <AlertDialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader className="text-center">
            <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              {dialogTitle === "Success" ? (
                <CheckCircle className="h-6 w-6 text-white" />
              ) : (
                <AlertCircle className="h-6 w-6 text-white" />
              )}
            </div>
            <AlertDialogTitle className="text-xl font-semibold">
              {dialogTitle}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600">
              {dialogDescription}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex gap-2 sm:gap-2">
            <AlertDialogCancel className="flex-1">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setDialogOpen(false);
                dialogAction();
              }}
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              OK
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
