"use client";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { RootState } from "../../redux/store";
import { motion, AnimatePresence } from "framer-motion";
import JobList from "@/app-components/Performance/jobs/JobList";
import CreateJobModal from "@/app-components/Performance/jobs/CreateJobModal";
import JobDetails from "@/app-components/Performance/jobs/JobDetails";
import useSessionStorage from "@/app-components/Performance/hooks/useSessionStorage";

// Shadcn UI Button component (ensure you have a button component styled with Tailwind)
import { Button } from "@/components/ui/button";

// Lucide icons (using valid exports)
import { Plus, Briefcase } from "lucide-react";

// Import Screen component to wrap the page content
import { Screen } from "@/app-components/layout/screen";

// A simple spinner component for loading states
const Spinner = () => (
  <svg
    className="animate-spin h-6 w-6 text-green-500 mx-auto"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
    />
  </svg>
);

// Container for the whole management page
const ManagementContainer: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => (
  <div className="p-8 bg-gray-100 min-h-screen">{children}</div>
);

// Header card with a gradient background and decorative element
const HeaderCard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="relative p-8 mb-8 rounded-2xl bg-gradient-to-br from-purple-600 to-green-500 text-white shadow-lg overflow-hidden">
    {/* Decorative circle */}
    <div className="absolute -top-12 -right-12 w-24 h-24 bg-white opacity-10 rounded-full"></div>
    {children}
  </div>
);

// Content card used to wrap inner sections
const ContentCard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="p-6 rounded-2xl shadow-sm bg-white transition-all">
    {children}
  </div>
);

const JobManagement: React.FC = () => {
  const navigate = useNavigate();
  const { token } = useSelector((state: RootState) => state.auth);
  const [activeTab, setActiveTab] = useSessionStorage("jobsActiveTab", 0);
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [filters, setFilters] = useState<any>({});
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"list" | "details">("list");

  useEffect(() => {
    if (!token) {
      navigate("/login");
    }
  }, [token, navigate]);

  const handleJobSelect = (job: any) => {
    setSelectedJob(job);
    setViewMode("details");
  };

  const handleFilterChange = (newFilters: any) => setFilters(newFilters);

  const handleCreateSuccess = (newJob: any) => {
    setSelectedJob(newJob);
    setViewMode("details");
  };

  if (!token) {
    return (
      <div className="flex justify-center mt-8">
        <Spinner />
      </div>
    );
  }


  return (
    <Screen>
      <ManagementContainer>
        {/* Header Section */}
        <HeaderCard>
          <div className="grid grid-cols-1 md:grid-cols-[auto,1fr,auto] gap-4 items-center">
            <div>
              <Briefcase className="h-10 w-10" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Job Position Management</h1>
              <p className="text-lg">
                Central hub for creating, managing, and analyzing organizational
                positions
              </p>
            </div>
            <div>
              <Button
                onClick={() => setCreateModalOpen(true)}
                className="bg-white/20 hover:bg-white/30 text-white"
              >
                <Plus className="mr-2 h-4 w-4" /> New Position
              </Button>
            </div>
          </div>
        </HeaderCard>

        {/* Main Content */}
        <div className="mt-8">
          <AnimatePresence mode="wait">
            {activeTab === 0 && (
              <motion.div
                key="dashboard"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <div className="grid grid-cols-1 gap-6">
                  <ContentCard>
                    <h2 className="text-xl font-semibold mb-4">
                      Recent Job Positions
                    </h2>
                    <JobList
                      onJobSelect={handleJobSelect}
                      filters={filters}
                      compact
                    />
                  </ContentCard>
                </div>
              </motion.div>
            )}

            {activeTab === 1 && (
              <motion.div
                key="positions"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <div className="grid grid-cols-1 gap-6">
                  <div>
                    <ContentCard>
                      {viewMode === "list" ? (
                        <JobList
                          onJobSelect={handleJobSelect}
                          filters={filters}
                        />
                      ) : (
                        <JobDetails
                          job={selectedJob}
                          onBack={() => setViewMode("list")}
                          onEdit={() => {
                            setCreateModalOpen(true);
                            setViewMode("list");
                          }}
                        />
                      )}
                    </ContentCard>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Create / Edit Job Modal */}
        <CreateJobModal
          open={createModalOpen}
          onClose={() => setCreateModalOpen(false)}
          onSuccess={handleCreateSuccess}
          existingJob={viewMode === "details" ? selectedJob : undefined}
        />
      </ManagementContainer>
    </Screen>
  );
};

export default JobManagement;
