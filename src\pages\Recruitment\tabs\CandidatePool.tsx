"use client";

import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";

import CandidateDetailsDialog from "../dialogs/CandidateDetailsDialog";
import {
  VacancyApplication,
  JobVacancy,
  ExternalUserBioData,
} from "../types";
import { BASE_URL } from "@/config";

const CandidatePool: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const [applications, setApplications] = useState<VacancyApplication[]>([]);
  const [vacancies, setVacancies] = useState<JobVacancy[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [candidateNameMap, setCandidateNameMap] = useState<Record<string, string>>({});
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<VacancyApplication | null>(null);

  useEffect(() => {
    const fetchAllData = async () => {
      setLoading(true);
      setError(null);
      try {
        const [vacRes, appRes, bioRes] = await Promise.all([
          fetch(`${BASE_URL}/hrm/vacancies`, {
            headers: { "Content-Type": "application/json" },
          }),
          fetch(`${BASE_URL}/recruitment/vacancy-application`, {
            headers: { "Content-Type": "application/json" },
          }),
          fetch(`${BASE_URL}/recruitment/external-user-bio-data`, {
            headers: { "Content-Type": "application/json" },
          }),
        ]);

        if (!vacRes.ok) {
          throw new Error(`Failed to fetch vacancies: ${vacRes.statusText}`);
        }
        if (!appRes.ok) {
          throw new Error(`Failed to fetch applications: ${appRes.statusText}`);
        }
        if (!bioRes.ok) {
          throw new Error(`Failed to fetch candidate bios: ${bioRes.statusText}`);
        }

        const vacData: JobVacancy[] = await vacRes.json();
        const appData: VacancyApplication[] = await appRes.json();
        const bioData: ExternalUserBioData[] = await bioRes.json();

        setVacancies(vacData);
        setApplications(appData);

        const nameMap: Record<string, string> = {};
        bioData.forEach((b) => {
          const fullName = `${b.first_name} ${b.middle_name ?? ""} ${b.last_name}`.trim();
          nameMap[b.external_user_no] = fullName;
        });
        setCandidateNameMap(nameMap);
      } catch (err: any) {
        setError(err.message || "Error fetching data");
      } finally {
        setLoading(false);
      }
    };
    fetchAllData();
  }, [token]);

  const getGroupedByVacancy = () => {
    return vacancies.map((vac) => {
      const appsForVac = applications.filter((a) => a.vacancy === vac.id);
      const underReview = appsForVac.filter(
        (a) =>
          a.application_status === "under review" ||
          a.application_status === "Submitted" ||
          a.application_status === "Open"
      );
      const shortlisted = appsForVac.filter(
        (a) =>
          a.application_status === "shortlisted" ||
          a.application_status === "Accepted"
      );
      const rejected = appsForVac.filter((a) => a.application_status === "Rejected");
      return {
        vacancy: vac,
        underReview,
        shortlisted,
        rejected,
      };
    });
  };

  const grouped = getGroupedByVacancy();
  const totalApplications = applications.length;

  const handleViewDetails = (app: VacancyApplication) => {
    setSelectedApplication(app);
    setDetailsOpen(true);
  };

  const handleCloseDetails = () => {
    setSelectedApplication(null);
    setDetailsOpen(false);
  };

  const handleUpdateStatus = async (newStatus: string) => {
    if (!selectedApplication) return;
    try {
      setLoading(true);
      setError(null);
      const res = await fetch(
        `${BASE_URL}/recruitment/vacancy-application/${selectedApplication.id}`,
        {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ application_status: newStatus }),
        }
      );
      if (!res.ok) {
        const errText = await res.text();
        throw new Error(`Failed to update status: ${errText}`);
      }
      setApplications((prev) =>
        prev.map((app) =>
          app.id === selectedApplication.id
            ? { ...app, application_status: newStatus }
            : app
        )
      );
      handleCloseDetails();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded shadow space-y-4">
      <div className="flex justify-between items-center mb-2">
        <h2 className="font-semibold text-sm">
          Candidate Pool <Badge>{totalApplications} applications</Badge>
        </h2>
      </div>
      {loading && <p>Loading...</p>}
      {error && <p className="text-red-500">{error}</p>}
      {!loading && !error && totalApplications === 0 && <p>No applications found.</p>}
      <Accordion type="single" collapsible className="space-y-2">
        {grouped.map(({ vacancy, underReview, shortlisted, rejected }) => {
          if (underReview.length === 0 && shortlisted.length === 0 && rejected.length === 0) {
            return null;
          }
          const vacancyTotal = underReview.length + shortlisted.length + rejected.length;
          return (
            <AccordionItem value={`vacancy-${vacancy.id}`} key={vacancy.id}>
              <AccordionTrigger className="flex justify-between items-center font-semibold">
                <span>
                  {vacancy.job_details.job_title} — {vacancy.job_details.job_code}
                </span>
                <Badge>{vacancyTotal}</Badge>
              </AccordionTrigger>
              <AccordionContent>
                <Accordion type="multiple" className="space-y-2">
                  {underReview.length > 0 && (
                    <AccordionItem value="underReview">
                      <AccordionTrigger className="flex justify-between items-center">
                        <span>Under Review</span>
                        <Badge>{underReview.length}</Badge>
                      </AccordionTrigger>
                      <AccordionContent>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Candidate</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {underReview.map((app) => {
                              const candidateName =
                                candidateNameMap[app.external_user_no] || app.external_user_no;
                              return (
                                <TableRow key={app.id}>
                                  <TableCell>{candidateName}</TableCell>
                                  <TableCell>
                                    <Badge variant="outline">{app.application_status}</Badge>
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <Button size="sm" onClick={() => handleViewDetails(app)}>
                                      View Details
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </AccordionContent>
                    </AccordionItem>
                  )}
                  {shortlisted.length > 0 && (
                    <AccordionItem value="shortlisted">
                      <AccordionTrigger className="flex justify-between items-center">
                        <span>Shortlisted</span>
                        <Badge>{shortlisted.length}</Badge>
                      </AccordionTrigger>
                      <AccordionContent>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Candidate</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {shortlisted.map((app) => {
                              const candidateName =
                                candidateNameMap[app.external_user_no] || app.external_user_no;
                              return (
                                <TableRow key={app.id}>
                                  <TableCell>{candidateName}</TableCell>
                                  <TableCell>
                                    <Badge variant="outline">{app.application_status}</Badge>
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <Button size="sm" onClick={() => handleViewDetails(app)}>
                                      View Details
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </AccordionContent>
                    </AccordionItem>
                  )}
                  {rejected.length > 0 && (
                    <AccordionItem value="rejected">
                      <AccordionTrigger className="flex justify-between items-center">
                        <span>Rejected</span>
                        <Badge>{rejected.length}</Badge>
                      </AccordionTrigger>
                      <AccordionContent>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Candidate</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {rejected.map((app) => {
                              const candidateName =
                                candidateNameMap[app.external_user_no] || app.external_user_no;
                              return (
                                <TableRow key={app.id}>
                                  <TableCell>{candidateName}</TableCell>
                                  <TableCell>
                                    <Badge variant="outline">{app.application_status}</Badge>
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <Button size="sm" onClick={() => handleViewDetails(app)}>
                                      View Details
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </AccordionContent>
                    </AccordionItem>
                  )}
                </Accordion>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
      <CandidateDetailsDialog
        open={detailsOpen}
        application={selectedApplication}
        onClose={handleCloseDetails}
        onStatusChange={handleUpdateStatus}
      />
    </div>
  );
};

export default CandidatePool;
