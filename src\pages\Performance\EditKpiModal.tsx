import React, { useState } from "react";
import axios from "axios";
import { BASE_URL } from "../../config";
import { Pencil, Save, X, Plus, Trash2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";


interface Kpi {
  id: number;
  what: string;
  total_marks: number;
  created_at: string;
  hows: KpiHow[];
  MIB_target?: number;
  Sales_target?: number;
  kpi_type: "default" | "custom" | "MIB Target" | "Sales Target";
}

interface KpiHow {
  id: number;
  how: string;
}

interface EditKpiModalProps {
  open: boolean;
  onClose: () => void;
  kpi: Kpi | null;
  token: string;
  onSuccess: () => void;
}

const EditKpiModal: React.FC<EditKpiModalProps> = ({
  open,
  onClose,
  kpi,
  token,
  onSuccess,
}) => {
  const [editedKpi, setEditedKpi] = useState<Kpi | null>(kpi);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [hows, setHows] = useState<KpiHow[]>(kpi?.hows || []);
  const [newHow, setNewHow] = useState({ how: "" });
  const [totalMarks, setTotalMarks] = useState(kpi?.total_marks || 0);

  React.useEffect(() => {
    if (kpi) {
      setEditedKpi(kpi);
      setHows(kpi.hows || []);
      setTotalMarks(kpi.total_marks);
    }
  }, [kpi]);

  if (!editedKpi) return null;

  const handleWhatChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditedKpi({ ...editedKpi, what: e.target.value });
  };


  const handleTotalMarksChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    setTotalMarks(value);
  };

  const handleHowChange = (index: number, value: string) => {
    const updatedHows = [...hows];
    updatedHows[index] = { ...updatedHows[index], how: value };
    setHows(updatedHows);
  };

  const handleAddHow = () => {
    if (!newHow.how.trim()) {
      setError("How description cannot be empty");
      return;
    }

    const updatedHows = [...hows, { ...newHow, id: 0 }];
    setHows(updatedHows);
    setNewHow({ how: "" });
  };

  const handleDeleteHow = (index: number) => {
    const updatedHows = hows.filter((_, i) => i !== index);
    setHows(updatedHows);
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError("");


      if (!editedKpi.what.trim()) {
        setError("KPI description is required");
        setLoading(false);
        return;
      }

      if (hows.length === 0 && editedKpi.kpi_type !== "MIB Target") {
        setError("At least one How is required");
        setLoading(false);
        return;
      }

      if (hows.length === 0 && editedKpi.kpi_type !== "Sales Target") {
        setError("At least one How is required");
        setLoading(false);
        return;
      }

      console.log("Updating KPI:", editedKpi, totalMarks, hows);
      await axios.patch(
        `${BASE_URL}/hrm/kpi-whats/${editedKpi.id}`,
        {
          what: editedKpi.what,
          total_marks: totalMarks,
          MIB_target: editedKpi.MIB_target || null,
          Sales_target: editedKpi.Sales_target || null,
          Kpi_type: editedKpi.kpi_type
        },
        {
          headers: { Authorization: `Token ${token}` },
        }
      );


      if (editedKpi.kpi_type !== "MIB Target" && editedKpi.kpi_type !== "Sales Target") {
        for (const how of hows) {
          console.log("Saving how:", how);
          if (how.id > 0) {
            await axios.patch(
              `${BASE_URL}/hrm/kpi-hows/${how.id}`,
              {
                how: how.how,
                whats: editedKpi.id,
              },
              {
                headers: { Authorization: `Token ${token}` },
              }
            );
          } else {
            await axios.post(
              `${BASE_URL}/hrm/kpi-hows`,
              {
                how: how.how,
                whats: editedKpi.id,
              },
              {
                headers: { Authorization: `Token ${token}` },
              }
            );
          }
        }

        const currentHowIds = hows.map(h => h.id).filter(id => id > 0);
        const originalHowIds = kpi?.hows?.map(h => h.id) || [];

        for (const originalId of originalHowIds) {
          if (!currentHowIds.includes(originalId)) {
            await axios.delete(`${BASE_URL}/hrm/kpi-hows/${originalId}/`, {
              headers: { Authorization: `Token ${token}` },
            });
          }
        }
      }

      onSuccess();
    } catch (err) {
      console.error("Error updating KPI:", err);
      setError("Failed to update KPI. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="bg-gradient-to-r from-green-600 to-indigo-700 -mx-6 -mt-6 px-6 py-4 rounded-t-lg text-white">
          <DialogTitle className="flex items-center gap-2">
            <Pencil className="h-5 w-5" />
            Edit KPI
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {/* KPI What */}
          <div className="space-y-2">
            <Label htmlFor="kpi-what" className="text-gray-700 font-medium">
              KPI Description (What)
            </Label>
            <Input
              id="kpi-what"
              value={editedKpi.what}
              onChange={handleWhatChange}
              className="w-full"
            />
          </div>

          {/* Total Marks */}
          <div className="space-y-2">
            <Label htmlFor="total-marks" className="text-gray-700 font-medium">
              Total Marks
            </Label>
            <Input
              id="total-marks"
              type="number"
              value={totalMarks}
              onChange={handleTotalMarksChange}
              className="w-full"
              min="0"
              max="100"
            />
          </div>

          {/* KPI Type */}
          <div className="p-4 bg-green-50 rounded-md border border-green-100">
            <div className="flex items-center gap-2 text-green-800 mb-2 font-medium">
              <AlertCircle className="h-5 w-5" />
              KPI Type: {editedKpi.kpi_type || "Default"}
            </div>

            {editedKpi.kpi_type === "MIB Target" && (
              <div className="space-y-2 mt-3">
                <Label htmlFor="mib-target" className="text-gray-700 font-medium">
                  MIB Target Value
                </Label>
                <Input
                  id="mib-target"
                  type="number"
                  value={editedKpi.MIB_target || 0}
                  onChange={(e) => setEditedKpi({ ...editedKpi, MIB_target: Number(e.target.value) })}
                  className="w-full"
                />
              </div>
            )}
            {editedKpi.kpi_type === "Sales Target" && (
              <div className="space-y-2 mt-3">
                <Label htmlFor="sales-target" className="text-gray-700 font-medium">
                  Sales Target Value
                </Label>
                <Input
                  id="sales-target"
                  type="number"
                  value={editedKpi.Sales_target || 0}
                  onChange={(e) => setEditedKpi({ ...editedKpi, Sales_target: Number(e.target.value) })}
                  className="w-full"
                />
              </div>
            )}
          </div>

          {/* KPI Hows - only show if not MIB Target */}
          {editedKpi.kpi_type !== "MIB Target" && editedKpi.kpi_type !== "Sales Target" && (
            <div className="space-y-4">
              <Label className="text-gray-700 font-medium">
                How to Achieve (Metrics)
              </Label>

              {/* Existing Hows */}
              <div className="space-y-3">
                {hows.map((how, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-3 border border-gray-200 rounded-md hover:bg-gray-50"
                  >
                    <div className="flex-shrink-0 bg-gradient-to-r from-green-600 to-indigo-700 text-white font-medium rounded-full w-7 h-7 flex items-center justify-center text-xs">
                      {index + 1}
                    </div>
                    <div className="flex-1 space-y-2">
                      <Textarea
                        value={how.how}
                        onChange={(e) => handleHowChange(index, e.target.value)}
                        className="w-full resize-none"
                        placeholder="How to achieve this KPI"
                      />
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteHow(index)}
                      className="text-red-500 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-5 w-5" />
                    </Button>
                  </div>
                ))}
              </div>

              {/* Add New How */}
              <div className="flex items-end gap-3 p-3 border border-dashed border-green-300 rounded-md bg-green-50">
                <div className="flex-1 space-y-2">
                  <Label htmlFor="new-how" className="text-green-700 text-xs">
                    Add New "How" Description
                  </Label>
                  <Textarea
                    id="new-how"
                    value={newHow.how}
                    onChange={(e) => setNewHow({ how: e.target.value })}
                    className="w-full resize-none"
                    placeholder="Enter how to achieve this KPI"
                  />
                </div>
                <Button
                  onClick={handleAddHow}
                  variant="outline"
                  className="flex items-center gap-1 bg-green-600 text-white hover:bg-green-700 border-none"
                >
                  <Plus className="h-4 w-4" /> Add
                </Button>
              </div>
            </div>
          )}
        </div>
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-center gap-2 mb-4">
            <AlertCircle className="h-5 w-5 text-red-500" />
            {error}
          </div>
        )}

        <DialogFooter className="flex justify-between gap-2 mt-6">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" /> Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="flex items-center gap-2 bg-gradient-to-r from-green-600 to-indigo-700 text-white"
            disabled={loading}
          >
            {loading ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditKpiModal;