import React, { useState, useEffect } from "react";
import axios from "axios";
import toast, { Toaster } from "react-hot-toast";
import { Screen } from "@/app-components/layout/screen";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import {
  B<PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DataTable } from "@/components/ui/DataTable";
import { ColumnDef } from "@tanstack/react-table";
import EmployeeDetails from "./EmployeeDetails";

import { BASE_URL } from "@/config";

interface Employee {
  employee_no: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  email: string;
  department_id?: number;
  gender?: string;
  job_title?: string;
  is_active?: boolean | string;
}

interface EmployeeBioDetails {
  id?: number;
  employee_no: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  gender?: string;
  marital_status?: string;
  date_of_birth?: string;
  passport_number?: string;
  id_number?: string;
  date_of_first_appointment?: string;
  year_in_employment?: string;
  created_at?: string;
  update_at?: string;
}

interface JobInfo {
  id?: number;
  employee_no: string;
  category?: string;
  directorate?: string;
  job_title_code?: string;
  job_title?: string;
  work_location?: string;
  business_unit?: string;
  department?: number;
  teams_code?: number;
}

interface Department {
  id: number;
  name: string;
  description?: string;
  dep_head?: string;
  dep_head_assistant?: string;
  dep_hr?: string;
  department_status_active?: boolean;
  organisation?: number;
  parent_department?: number;
}

function formatName(str: string): string {
  if (!str) return "";
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

function getFullName(employeeNo: string, bioDetails: Record<string, EmployeeBioDetails>, maxLength: number = 30): string {
  const bio = bioDetails[employeeNo];
  if (!bio) return "";

  const firstName = formatName(bio.first_name || "");
  const middleName = formatName(bio.middle_name || "");
  const lastName = formatName(bio.last_name || "");

  const fullName = [firstName, middleName, lastName].filter(Boolean).join(" ");

  if (fullName.length <= maxLength) {
    return fullName;
  }

  return fullName.substring(0, maxLength - 3) + "...";
}

export default function EmployeesList() {
  const token = useSelector((state: RootState) => state.auth.token);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [employeeBioDetails, setEmployeeBioDetails] = useState<Record<string, EmployeeBioDetails>>({});
  const [employeeJobInfo, setEmployeeJobInfo] = useState<Record<string, JobInfo>>({});
  const [departments, setDepartments] = useState<Record<number, string>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedEmployeeNo, setSelectedEmployeeNo] = useState<string | null>(null);
  const [tab, setTab] = useState<"all" | "active" | "inactive">("all");

  useEffect(() => {
    if (!token) {
      setError("Authentication token is missing");
      return;
    }

    setLoading(true);
    const headers = { Authorization: `Token ${token}` };

    const fetchEmployees = axios
      .get<Employee[]>(`${BASE_URL}/users/all-employees`, { headers })
      .then((res) => setEmployees(res.data))
      .catch((err) => {
        console.error(err);
        setError("Error fetching employees");
        toast.error("Error fetching employees");
      });

    const fetchEmployeeBioDetails = axios
      .get<EmployeeBioDetails[]>(`${BASE_URL}/users/employee-bio-details`, { headers })
      .then((res) => {
        const bioMap: Record<string, EmployeeBioDetails> = {};
        res.data.forEach((bio) => {
          bioMap[bio.employee_no] = bio;
        });
        setEmployeeBioDetails(bioMap);
      })
      .catch((err) => {
        console.error(err);
        toast.error("Error fetching employee bio details");
      });

    const fetchEmployeeJobInfo = axios
      .get<JobInfo[]>(`${BASE_URL}/users/employee-job-info-details`, { headers })
      .then((res) => {
        const jobMap: Record<string, JobInfo> = {};
        res.data.forEach((job) => {
          jobMap[job.employee_no] = job;
        });
        setEmployeeJobInfo(jobMap);
      })
      .catch((err) => {
        console.error(err);
        toast.error("Error fetching employee job info");
      });

    const fetchDepartments = axios
      .get<Department[]>(`${BASE_URL}/users/departments`, { headers })
      .then((res) => {
        const deptMap: Record<number, string> = {};
        res.data.forEach((dept) => {
          deptMap[dept.id] = dept.name;
        });
        setDepartments(deptMap);
      })
      .catch((err) => {
        console.error(err);
        toast.error("Error fetching departments");
      });

    Promise.all([fetchEmployees, fetchEmployeeBioDetails, fetchEmployeeJobInfo, fetchDepartments]).finally(() => {
      setLoading(false);
    });
  }, [token]);

  // Helper to normalize active status to boolean
  const isActive = (status?: boolean | string): boolean => {
    return (
      status === true ||
      status === "true" ||
      status === "1" ||
      (typeof status === "string" && status.toLowerCase() === "true")
    );
  };

  const renderGender = (gender?: string) => {
    switch ((gender || "").toUpperCase()) {
      case "M":
        return "Male";
      case "F":
        return "Female";
      case "O":
        return "Other";
      default:
        return "";
    }
  };

  const renderActiveStatus = (status?: boolean | string) => {
    const active = isActive(status);
    const text = active ? "Active" : "Inactive";
    const badgeClass = active
      ? "bg-green-200 text-green-800"
      : "bg-red-200 text-red-800";
    return <span className={`px-2 py-1 rounded-full ${badgeClass}`}>{text}</span>;
  };

  const columns: ColumnDef<Employee>[] = [
    { accessorKey: "employee_no", header: "Employee No" },
    {
      id: "name",
      header: "Name",
      accessorFn: (row) => getFullName(row.employee_no, employeeBioDetails),
      cell: ({ row }) => {
        const fullName = getFullName(row.original.employee_no, employeeBioDetails);
        return fullName || `${formatName(row.original.first_name)} ${formatName(row.original.last_name)}`.trim();
      },
    },
    { accessorKey: "email", header: "Email" },
    {
      id: "department",
      header: "Department",
      cell: ({ row }) => {
        const jobInfo = employeeJobInfo[row.original.employee_no];
        // Use department ID from job info to get department name
        if (jobInfo?.department) {
          return formatName(departments[jobInfo.department] || "");
        }
        // Fallback to original department logic
        const id = row.original.department_id;
        return id ? formatName(departments[id] || "") : "";
      },
    },
    {
      id: "job_title",
      header: "Job Title",
      cell: ({ row }) => {
        const jobInfo = employeeJobInfo[row.original.employee_no];
        return jobInfo?.job_title ? formatName(jobInfo.job_title) : formatName(row.original.job_title || "");
      },
    },
    {
      id: "gender",
      header: "Gender",
      cell: ({ row }) => {
        const bio = employeeBioDetails[row.original.employee_no];
        const gender = bio?.gender || row.original.gender;
        return renderGender(gender);
      },
    },
    {
      accessorKey: "is_active",
      header: "Status",
      cell: (info) => renderActiveStatus(info.getValue() as boolean | string),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <button
          onClick={() => setSelectedEmployeeNo(row.original.employee_no)}
          className="!p-0 !px-3 border border-slate-700"
        >
          View
        </button>
      ),
    },
  ];

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Employee List
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  // apply tab-based filtering and exclude SYS/ADMIN/0001
  const filteredEmployees = employees.filter((emp) => {
    // Hide SYS/ADMIN/0001 from the list
    if (emp.employee_no === "SYS/ADMIN/0001") return false;

    if (tab === "active") return isActive(emp.is_active);
    if (tab === "inactive") return !isActive(emp.is_active);
    return true; // "all"
  });

  return (
    <Screen headerContent={breadcrumb}>
      <Toaster />
      {loading ? (
        <div className="p-6 text-center">Loading employees...</div>
      ) : error ? (
        <div className="p-6 text-center text-red-600">{error}</div>
      ) : selectedEmployeeNo && token ? (
        <EmployeeDetails
          employeeNo={selectedEmployeeNo}
          setView={(employeeNo) => {
            setSelectedEmployeeNo(employeeNo);
            // Refresh employee list when returning from details
            if (!employeeNo) {
              setLoading(true);
              const headers = { Authorization: `Token ${token}` };

              const refreshEmployees = axios
                .get<Employee[]>(`${BASE_URL}/users/all-employees`, { headers })
                .then((res) => setEmployees(res.data))
                .catch((err) => {
                  console.error(err);
                  setError("Error refreshing employees");
                });

              const refreshBioDetails = axios
                .get<EmployeeBioDetails[]>(`${BASE_URL}/users/employee-bio-details`, { headers })
                .then((res) => {
                  const bioMap: Record<string, EmployeeBioDetails> = {};
                  res.data.forEach((bio) => {
                    bioMap[bio.employee_no] = bio;
                  });
                  setEmployeeBioDetails(bioMap);
                })
                .catch((err) => {
                  console.error(err);
                  toast.error("Error refreshing employee bio details");
                });

              const refreshJobInfo = axios
                .get<JobInfo[]>(`${BASE_URL}/users/employee-job-info-details`, { headers })
                .then((res) => {
                  const jobMap: Record<string, JobInfo> = {};
                  res.data.forEach((job) => {
                    jobMap[job.employee_no] = job;
                  });
                  setEmployeeJobInfo(jobMap);
                })
                .catch((err) => {
                  console.error(err);
                  toast.error("Error refreshing employee job info");
                });

              const refreshDepartments = axios
                .get<Department[]>(`${BASE_URL}/users/departments`, { headers })
                .then((res) => {
                  const deptMap: Record<number, string> = {};
                  res.data.forEach((dept) => {
                    deptMap[dept.id] = dept.name;
                  });
                  setDepartments(deptMap);
                })
                .catch((err) => {
                  console.error(err);
                  toast.error("Error refreshing departments");
                });

              Promise.all([refreshEmployees, refreshBioDetails, refreshJobInfo, refreshDepartments]).finally(() => {
                setLoading(false);
              });
            }
          }}
          token={token}
        />
      ) : (
        <div className="p-6">
          {/* Tabs */}
          <div className="flex space-x-4 mb-4">
            <button
              onClick={() => setTab("all")}
              className={`px-4 py-2 rounded-md ${
                tab === "all" ? "bg-slate-700 text-white" : "bg-slate-200 text-slate-800"
              }`}
            >
              All
            </button>
            <button
              onClick={() => setTab("active")}
              className={`px-4 py-2 rounded-md ${
                tab === "active" ? "bg-slate-700 text-white" : "bg-slate-200 text-slate-800"
              }`}
            >
              Active
            </button>
            <button
              onClick={() => setTab("inactive")}
              className={`px-4 py-2 rounded-md ${
                tab === "inactive"
                  ? "bg-slate-700 text-white"
                  : "bg-slate-200 text-slate-800"
              }`}
            >
              Inactive
            </button>
          </div>

          {/* DataTable */}
          <div className="h-[80vh]">
            <DataTable<Employee>
              data={filteredEmployees}
              columns={columns}
              enableToolbar
              enableColumnFilters
              enableSorting
              enablePagination
              enableExportToExcel
              enablePrintPdf
              enableFullScreenToggle
              enableColumnControl
              containerClassName="h-full"
            />
          </div>
        </div>
      )}
    </Screen>
  );
}
