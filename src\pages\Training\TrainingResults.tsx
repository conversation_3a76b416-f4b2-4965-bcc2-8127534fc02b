"use client"

import { useEffect, useState } from "react"
import { Screen } from "@/app-components/layout/screen"
import { TrainingResults } from "../../app-components/training/TrainingResults"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { BASE_URL } from "@/config"
import { useSelector } from "react-redux"
import { RootState } from "@/redux/store"

interface EmployeeTrainingItem {
  id: number
  employee: number
  progress_percentage: number
  // ...other fields
}

export default function ResultsPage() {
  const [results, setResults] = useState<{ employee: string; score: number }[]>([])
  const token = useSelector((state: RootState) => state.auth.token);

  useEffect(() => {
    const fetchResults = async () => {
      try {
        const response = await fetch(`${BASE_URL}/hrm/employee-training-details`)
        if (!response.ok) {
          throw new Error("Failed to fetch training details")
        }
        const data: EmployeeTrainingItem[] = await response.json()
        // Convert from your model to { employee, score }
        const mapped = data.map((item) => ({
          employee: String(item.employee), // or fetch the employee name separately
          score: item.progress_percentage || 0,
        }))
        setResults(mapped)
      } catch (error) {
        console.error(error)
      }
    }

    fetchResults()
  }, [])

  // Sort descending by score
  const sorted = [...results].sort((a, b) => b.score - a.score)

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Results
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  )

  return (
    <Screen headerContent={breadcrumb}>
      <div>
        <TrainingResults results={sorted} />
      </div>
    </Screen>
  )
}
