import React, { useState, useEffect, FormEvent, ChangeEvent } from 'react';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Screen } from '@/app-components/layout/screen';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from '@/components/ui/tabs';
import {
  RefreshCw,
  Edit2,
  Trash2,
  X as CancelIcon,
  History,
} from 'lucide-react';
import { BASE_URL } from '@/config';

type Announcement = {
  id: number;
  title: string;
  content: string;
  start_date: string;
  end_date: string;
  expired: boolean;
};

export default function Announcements() {
  const token = useSelector((state: RootState) => state.auth.token);
  const isMobile = useIsMobile();
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('active');
  const [form, setForm] = useState({
    title: '',
    content: '',
    start_date: '',
    end_date: '',
  });
  const [editingId, setEditingId] = useState<number | null>(null);

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block" />
        <BreadcrumbItem>
          <BreadcrumbPage>Announcements</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const res = await axios.get<Announcement[]>(
        `${BASE_URL}/hrm/announcements`,
        { headers: { Authorization: `Token ${token}` } }
      );
      setAnnouncements(res.data);
      setError('');
    } catch (err) {
      console.error(err);
      setError('Failed to load announcements');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnnouncements();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    try {
      if (editingId) {
        // PATCH existing
        await axios.patch(
          `${BASE_URL}/hrm/announcements/${editingId}`,
          form,
          { headers: { Authorization: `Token ${token}` } }
        );
        setEditingId(null);
      } else {
        // POST new
        await axios.post(
          `${BASE_URL}/hrm/announcements`,
          form,
          { headers: { Authorization: `Token ${token}` } }
        );
      }
      setForm({ title: '', content: '', start_date: '', end_date: '' });
      fetchAnnouncements();
    } catch (err) {
      console.error(err);
      setError(editingId ? 'Failed to update announcement' : 'Failed to create announcement');
    }
  };

  const handleEdit = (item: Announcement) => {
    setEditingId(item.id);
    setForm({
      title: item.title,
      content: item.content,
      start_date: item.start_date,
      end_date: item.end_date,
    });
    setError('');
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setForm({ title: '', content: '', start_date: '', end_date: '' });
    setError('');
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this announcement?')) return;
    try {
      await axios.delete(`${BASE_URL}/hrm/announcements/${id}`, {
        headers: { Authorization: `Token ${token}` },
      });
      fetchAnnouncements();
    } catch (err) {
      console.error(err);
      setError('Failed to delete announcement');
    }
  };

  // Helper function to check if announcement is expired based on end date
  const isAnnouncementExpired = (endDate: string): boolean => {
    const today = new Date();
    const announcementEndDate = new Date(endDate);
    // Set time to end of day for the announcement end date to be inclusive
    announcementEndDate.setHours(23, 59, 59, 999);
    return announcementEndDate < today;
  };

  // Filter announcements based on both expired field and actual end date
  const activeAnnouncements = announcements.filter(item => {
    // Check both the API expired field and actual date comparison
    const isExpiredByDate = isAnnouncementExpired(item.end_date);
    return !item.expired && !isExpiredByDate;
  });

  const expiredAnnouncements = announcements.filter(item => {
    // Check both the API expired field and actual date comparison
    const isExpiredByDate = isAnnouncementExpired(item.end_date);
    return item.expired || isExpiredByDate;
  });

  // Component for rendering announcement list
  const AnnouncementList = ({ items, showActions = true }: { items: Announcement[], showActions?: boolean }) => (
    <div className="space-y-4">
      {items.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">No announcements found.</p>
        </div>
      )}
      {items.map(item => {
        const isExpiredByDate = isAnnouncementExpired(item.end_date);
        const isExpired = item.expired || isExpiredByDate;

        return (
          <div
            key={item.id}
            className="p-4 sm:p-6 rounded-lg bg-gray-50 dark:bg-gray-800 relative transition-all hover:shadow-md"
          >
            {showActions && (
              <div className={`absolute top-2 right-2 sm:top-4 sm:right-4 flex ${isMobile ? 'flex-col' : 'flex-row'} gap-1 sm:gap-2`}>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleEdit(item)}
                  className={`${isMobile ? 'h-8 w-8 p-1' : 'h-auto w-auto px-3 py-2'}`}
                >
                  <Edit2 className="w-4 h-4" />
                  {!isMobile && <span className="ml-1">Edit</span>}
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleDelete(item.id)}
                  className={`${isMobile ? 'h-8 w-8 p-1' : 'h-auto w-auto px-3 py-2'}`}
                >
                  <Trash2 className="w-4 h-4" />
                  {!isMobile && <span className="ml-1">Delete</span>}
                </Button>
              </div>
            )}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3 pr-16 sm:pr-24">
              <h4 className="font-semibold text-lg sm:text-xl">{item.title}</h4>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                  {item.start_date} – {item.end_date}
                </span>
                <Badge
                  className={isExpired
                    ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                    : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                  }
                >
                  {isExpired ? 'Expired' : 'Active'}
                </Badge>
              </div>
            </div>
            <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300 leading-relaxed">
              {item.content}
            </p>
          </div>
        );
      })}
    </div>
  );

  return (
    <Screen headerContent={breadcrumb}>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-7xl">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-1 sm:grid-cols-2 h-auto sm:h-10">
            <TabsTrigger
              value="active"
              className="flex items-center justify-center gap-1 sm:gap-2 p-2 sm:p-3 text-sm sm:text-base"
            >
              <span className="hidden sm:inline">Active Announcements</span>
              <span className="sm:hidden">Active</span>
              {activeAnnouncements.length > 0 && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {activeAnnouncements.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="history"
              className="flex items-center justify-center gap-1 sm:gap-2 p-2 sm:p-3 text-sm sm:text-base"
            >
              <History className="w-4 h-4" />
              <span className="hidden sm:inline">Announcement History</span>
              <span className="sm:hidden">History</span>
              {expiredAnnouncements.length > 0 && (
                <Badge variant="outline" className="ml-1 text-xs">
                  {expiredAnnouncements.length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="space-y-4 sm:space-y-6">
            {/* Create / Edit Form - Only shown in active tab */}
            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl sm:text-2xl">
                  {editingId ? 'Edit Announcement' : 'Create Announcement'}
                </CardTitle>
                <CardDescription className="text-sm sm:text-base">
                  {editingId
                    ? 'Modify your HR announcement'
                    : 'Publish new HR announcement'}
                </CardDescription>
              </CardHeader>
              <CardContent className="p-4 sm:p-6">
                {error && (
                  <div className="text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md mb-4 text-sm">
                    {error}
                  </div>
                )}
                <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium">Title</label>
                    <Input
                      name="title"
                      value={form.title}
                      onChange={handleChange}
                      placeholder="Enter announcement title"
                      required
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium">Content</label>
                    <Textarea
                      name="content"
                      value={form.content}
                      onChange={handleChange}
                      placeholder="Enter announcement content"
                      rows={4}
                      required
                      className="w-full resize-none"
                    />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">
                        Start Date
                      </label>
                      <Input
                        name="start_date"
                        type="date"
                        value={form.start_date}
                        onChange={handleChange}
                        required
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium">
                        End Date
                      </label>
                      <Input
                        name="end_date"
                        type="date"
                        value={form.end_date}
                        onChange={handleChange}
                        required
                        className="w-full"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 pt-2">
                    <Button type="submit" className="w-full sm:w-auto">
                      {editingId ? 'Update' : 'Publish'}
                    </Button>
                    {editingId && (
                      <Button
                        variant="secondary"
                        onClick={handleCancelEdit}
                        type="button"
                        className="w-full sm:w-auto"
                      >
                        <CancelIcon className="w-4 h-4 mr-1" />
                        Cancel
                      </Button>
                    )}
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Active Announcements List */}
            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <div>
                    <CardTitle className="text-xl sm:text-2xl">Active Announcements</CardTitle>
                    <CardDescription className="text-sm sm:text-base">
                      Current announcements that are still active
                    </CardDescription>
                  </div>
                  {activeAnnouncements.length > 0 && (
                    <Badge variant="secondary" className="self-start sm:self-center">
                      {activeAnnouncements.length} active
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-4 sm:p-6">
                {loading ? (
                  <div className="flex justify-center py-8 sm:py-12">
                    <RefreshCw className="animate-spin w-6 h-6 text-gray-500" />
                  </div>
                ) : (
                  <AnnouncementList items={activeAnnouncements} showActions={true} />
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4 sm:space-y-6">
            {/* Announcement History */}
            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <div>
                    <CardTitle className="text-xl sm:text-2xl flex items-center gap-2">
                      <History className="w-5 h-5 sm:w-6 sm:h-6" />
                      Announcement History
                    </CardTitle>
                    <CardDescription className="text-sm sm:text-base">
                      Previous announcements that have expired
                    </CardDescription>
                  </div>
                  {expiredAnnouncements.length > 0 && (
                    <Badge variant="outline" className="self-start sm:self-center">
                      {expiredAnnouncements.length} expired
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-4 sm:p-6">
                {loading ? (
                  <div className="flex justify-center py-8 sm:py-12">
                    <RefreshCw className="animate-spin w-6 h-6 text-gray-500" />
                  </div>
                ) : (
                  <AnnouncementList items={expiredAnnouncements} showActions={false} />
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Screen>
  );
}
