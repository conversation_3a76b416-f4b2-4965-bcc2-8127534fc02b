"use client";

import React, { useState,  use<PERSON>emo } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useNavigate } from "react-router-dom";

import { BASE_URL } from "@/config";

// Shadcn/UI components
import { Screen } from "@/app-components/layout/screen";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";

// Recharts
import {
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>hart,
  Area,
} from "recharts";

/** ------------------ TYPES (Based on your endpoints' responses) ------------------ */
// Absenteeism trends
interface AbsenteeismTrends {
  leave_distribution: {
    leave_type_name: string;
    total_days: number;
    frequency: number;
  }[];
  monthly_trends: {
    month: string;     // e.g. "2025-03"
    total_days: number;
    leave_count: number;
  }[];
  total_leave_days: number;
  total_leave_requests: number;
}

// Leave Audit Trail
interface AuditTrailEntry {
  action_date: string;
  action_type: string; // e.g. "submission", "approval"
  action_by: string;
  employee_no: string;
  leave_type_name: string;
  comments?: string;
}
interface LeaveAuditTrail {
  audit_trail: AuditTrailEntry[];
}

// Leave Compliance
interface LeaveCompliance {
  total_applications: number;
  late_submissions: number;
  missing_documents: number;
  avg_manager_response_time: string; // e.g. "2 days"
  compliance_by_department: {
    department_name: string;
    total: number;
    compliant: number;
  }[];
}

// Leave Dashboard
interface LeaveDashboard {
  today_leaves: number;
  pending_approvals: number;
  upcoming_leaves: {
    employee_name: string;
    start_date: string;
    end_date: string;
    leave_type: string;
  }[];
  department_impact: {
    department_name: string;
    count: number;
    total_days: number;
  }[];
  leave_types_distribution: {
    leave_type: string;
    count: number;
  }[];
}

// Leave Forecast
interface LeaveForecast {
  forecast: {
    forecast_date: string; // e.g. "2025-05-01"
    leave_type: string;    // e.g. "Annual"
    predicted_usage: number;
    confidence_level: number; // e.g. 0.9
  }[];
  historical_averages: {
    month: string; // e.g. "2024-10"
    leave_type_name: string;
    avg_days: number;
    count: number;
  }[];
}

// Leave Liability
interface LeaveLiability {
  financial_impact: {
    total_cost: number;
    total_days: number;
  };
  department_breakdown: {
    department_name: string;
    total_cost: number;
    total_days: number;
    employee_count: number;
  }[];
  leave_type_breakdown: {
    leave_type_name: string;
    total_cost: number;
    total_days: number;
    employee_count: number;
  }[];
  total_approved_leaves: number;
  total_employees: number;
}

const LeaveAnalyticsDashboard: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();

  // ------------------ FILTER STATES ------------------
  const [departmentId, setDepartmentId] = useState<string>("");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");

  // For leave_dashboard
  const [daysAhead, setDaysAhead] = useState<string>("7");

  // For leave_forecast
  const [forecastMonths, setForecastMonths] = useState<string>("3");
  const [confidenceThreshold, setConfidenceThreshold] = useState<string>("0.8");

  // --------------- STATES TO STORE DATA ---------------
  const [absenteeismTrends, setAbsenteeismTrends] = useState<AbsenteeismTrends | null>(null);
  const [auditTrail, setAuditTrail] = useState<LeaveAuditTrail | null>(null);
  const [leaveCompliance, setLeaveCompliance] = useState<LeaveCompliance | null>(null);
  const [leaveDashboard, setLeaveDashboard] = useState<LeaveDashboard | null>(null);
  const [leaveForecast, setLeaveForecast] = useState<LeaveForecast | null>(null);
  const [leaveLiability, setLeaveLiability] = useState<LeaveLiability | null>(null);

  // Loading & Error
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Build query param helper
  const buildQueryParams = (params: Record<string, string>): string => {
    const urlParams = new URLSearchParams();
    Object.entries(params).forEach(([key, val]) => {
      if (val) urlParams.append(key, val);
    });
    return urlParams.toString();
  };

  // ------------------ FETCH ALL ENDPOINTS (in parallel) ------------------
  const fetchAllLeaveAnalytics = async () => {
    if (!token) {
      setError("No token found. Please log in.");
      return;
    }
    setLoading(true);
    setError(null);

    try {
      // Common query params for department, start_date, end_date
      const commonParams = {
        department_id: departmentId,
        start_date: startDate,
        end_date: endDate,
      };
      // Endpoints
      const absenteeismUrl = `${BASE_URL}/analytics/absenteeism_trends/?${buildQueryParams(
        commonParams
      )}`;
      const auditTrailUrl = `${BASE_URL}/analytics/leave_audit_trail/?${buildQueryParams(
        commonParams
      )}`;
      const complianceUrl = `${BASE_URL}/analytics/leave_compliance/?${buildQueryParams(
        commonParams
      )}`;
      const dashboardUrl = `${BASE_URL}/analytics/leave_dashboard/?${buildQueryParams({
        ...commonParams,
        days_ahead: daysAhead,
      })}`;
      const forecastUrl = `${BASE_URL}/analytics/leave_forecast/?${buildQueryParams({
        ...commonParams,
        forecast_months: forecastMonths,
        confidence_threshold: confidenceThreshold,
      })}`;
      const liabilityUrl = `${BASE_URL}/analytics/leave_liability/?${buildQueryParams(
        commonParams
      )}`;

      // Parallel fetch
      const [
        absenteeismRes,
        auditRes,
        complianceRes,
        dashboardRes,
        forecastRes,
        liabilityRes,
      ] = await Promise.all([
        fetch(absenteeismUrl, { headers: { Authorization: `Token ${token}` } }),
        fetch(auditTrailUrl, { headers: { Authorization: `Token ${token}` } }),
        fetch(complianceUrl, { headers: { Authorization: `Token ${token}` } }),
        fetch(dashboardUrl, { headers: { Authorization: `Token ${token}` } }),
        fetch(forecastUrl, { headers: { Authorization: `Token ${token}` } }),
        fetch(liabilityUrl, { headers: { Authorization: `Token ${token}` } }),
      ]);

      // Check for errors in any response
      const allResponses = [
        absenteeismRes,
        auditRes,
        complianceRes,
        dashboardRes,
        forecastRes,
        liabilityRes,
      ];
      for (const response of allResponses) {
        if (!response.ok) {
          const text = await response.text();
          throw new Error(`Request failed [${response.url}]: ${response.status} ${text}`);
        }
      }

      // Parse JSON
      const [absenteeismJson, auditJson, complianceJson, dashboardJson, forecastJson, liabilityJson] =
        await Promise.all([
          absenteeismRes.json(),
          auditRes.json(),
          complianceRes.json(),
          dashboardRes.json(),
          forecastRes.json(),
          liabilityRes.json(),
        ]);

      setAbsenteeismTrends(absenteeismJson);
      setAuditTrail(auditJson);
      setLeaveCompliance(complianceJson);
      setLeaveDashboard(dashboardJson);
      setLeaveForecast(forecastJson);
      setLeaveLiability(liabilityJson);
    } catch (err: any) {
      setError(err.message || "Failed to fetch leave analytics data.");
    } finally {
      setLoading(false);
    }
  };

  // OPTIONAL: Auto-fetch on mount or when filters change
  // useEffect(() => {
  //   fetchAllLeaveAnalytics();
  // }, [token, departmentId, startDate, endDate, daysAhead, forecastMonths, confidenceThreshold]);

  // ------------- Some sample computed data or chart transforms -------------
  // For absenteeismTrends.monthly_trends => line chart or area chart data
  const absenteeismMonthlyData = useMemo(() => {
    // If absenteeismTrends is null or monthly_trends is undefined, return empty array
    if (!absenteeismTrends?.monthly_trends) return [];
    return absenteeismTrends.monthly_trends.map((item) => ({
      month: item.month,
      totalDays: item.total_days,
      leaveCount: item.leave_count,
    }));
  }, [absenteeismTrends]);

  // For leave_dashboard.leave_types_distribution => pie chart data
  const leaveTypesPieData = useMemo(() => {
    if (!leaveDashboard?.leave_types_distribution) return [];
    return leaveDashboard.leave_types_distribution.map((item) => ({
      name: item.leave_type,
      value: item.count,
    }));
  }, [leaveDashboard]);

  return (
    <Screen>
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Leave Analytics</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="p-4 space-y-6">
        {/* Filter Card */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
            <CardDescription>Set parameters to refine your analytics</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Basic filter fields: department_id, start_date, end_date, etc. */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              {/* Department */}
              <div className="flex flex-col space-y-1">
                <Label htmlFor="departmentId">Department ID</Label>
                <Input
                  id="departmentId"
                  placeholder="Department ID"
                  value={departmentId}
                  onChange={(e) => setDepartmentId(e.target.value)}
                />
              </div>
              {/* Start Date */}
              <div className="flex flex-col space-y-1">
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  type="date"
                  id="startDate"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              {/* End Date */}
              <div className="flex flex-col space-y-1">
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  type="date"
                  id="endDate"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>

            {/* Additional fields for forecast, dashboard, etc. */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              {/* Days Ahead (for leave_dashboard) */}
              <div className="flex flex-col space-y-1">
                <Label htmlFor="daysAhead">Days Ahead (Dashboard)</Label>
                <Input
                  type="number"
                  id="daysAhead"
                  min={1}
                  max={30}
                  value={daysAhead}
                  onChange={(e) => setDaysAhead(e.target.value)}
                />
              </div>
              {/* Forecast Months (for leave_forecast) */}
              <div className="flex flex-col space-y-1">
                <Label htmlFor="forecastMonths">Forecast Months (1-12)</Label>
                <Input
                  type="number"
                  id="forecastMonths"
                  min={1}
                  max={12}
                  value={forecastMonths}
                  onChange={(e) => setForecastMonths(e.target.value)}
                />
              </div>
              {/* Confidence Threshold (for leave_forecast) */}
              <div className="flex flex-col space-y-1">
                <Label htmlFor="confidenceThreshold">
                  Confidence Threshold (0-1)
                </Label>
                <Input
                  type="number"
                  step="0.01"
                  id="confidenceThreshold"
                  value={confidenceThreshold}
                  onChange={(e) => setConfidenceThreshold(e.target.value)}
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2 mt-4">
              <Button onClick={fetchAllLeaveAnalytics} disabled={loading}>
                {loading ? "Loading..." : "Load Data"}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setDepartmentId("");
                  setStartDate("");
                  setEndDate("");
                  setDaysAhead("7");
                  setForecastMonths("3");
                  setConfidenceThreshold("0.8");
                  // Clear previous data
                  setAbsenteeismTrends(null);
                  setAuditTrail(null);
                  setLeaveCompliance(null);
                  setLeaveDashboard(null);
                  setLeaveForecast(null);
                  setLeaveLiability(null);
                }}
              >
                Clear
              </Button>
            </div>
          </CardContent>
        </Card>

        {error && <p className="text-red-500">{error}</p>}
        {loading && <p>Loading data from all endpoints...</p>}

        {/* ---------------------- ABSENTEEISM TRENDS ---------------------- */}
        {absenteeismTrends && (
          <Card>
            <CardHeader>
              <CardTitle>Absenteeism Trends</CardTitle>
              <CardDescription>
                Total Requests: {absenteeismTrends.total_leave_requests} | Total Days:{" "}
                {absenteeismTrends.total_leave_days}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 1) Leave Distribution (Bar Chart) */}
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Leave Distribution
                </h3>
                {absenteeismTrends.leave_distribution?.length === 0 ? (
                  <p>No distribution data.</p>
                ) : absenteeismTrends.leave_distribution ? (
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={absenteeismTrends.leave_distribution}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="leave_type_name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="total_days" fill="#8884d8" name="Total Days" />
                      <Bar dataKey="frequency" fill="#82ca9d" name="Frequency" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <p>No distribution data.</p>
                )}
              </div>

              {/* 2) Monthly Trends (Area Chart) */}
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Monthly Trends
                </h3>
                {absenteeismMonthlyData.length === 0 ? (
                  <p>No monthly trend data.</p>
                ) : (
                  <ResponsiveContainer width="100%" height={250}>
                    <AreaChart data={absenteeismMonthlyData}>
                      <defs>
                        <linearGradient id="areaColor" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopOpacity={0.8} />
                          <stop offset="95%" stopOpacity={0.1} />
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="totalDays"
                        name="Total Days"
                        stroke="#8884d8"
                        fill="url(#areaColor)"
                      />
                      <Area
                        type="monotone"
                        dataKey="leaveCount"
                        name="Leave Count"
                        stroke="#82ca9d"
                        fillOpacity={0.4}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* ---------------------- LEAVE AUDIT TRAIL ---------------------- */}
        {auditTrail && (
          <Card>
            <CardHeader>
              <CardTitle>Leave Audit Trail</CardTitle>
              <CardDescription>Actions taken on leave requests</CardDescription>
            </CardHeader>
            <CardContent>
              {auditTrail.audit_trail?.length === 0 ? (
                <p>No audit entries found.</p>
              ) : auditTrail.audit_trail ? (
                <div className="overflow-auto max-h-[300px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Action Date</TableHead>
                        <TableHead>Action Type</TableHead>
                        <TableHead>Action By</TableHead>
                        <TableHead>Employee No</TableHead>
                        <TableHead>Leave Type</TableHead>
                        <TableHead>Comments</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {auditTrail.audit_trail.map((item, idx) => (
                        <TableRow key={idx}>
                          <TableCell>{item.action_date}</TableCell>
                          <TableCell>{item.action_type}</TableCell>
                          <TableCell>{item.action_by}</TableCell>
                          <TableCell>{item.employee_no}</TableCell>
                          <TableCell>{item.leave_type_name}</TableCell>
                          <TableCell>{item.comments || ""}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <p>No audit data.</p>
              )}
            </CardContent>
          </Card>
        )}

        {/* ---------------------- LEAVE COMPLIANCE ---------------------- */}
        {leaveCompliance && (
          <Card>
            <CardHeader>
              <CardTitle>Leave Compliance</CardTitle>
              <CardDescription>
                Late: {leaveCompliance.late_submissions}, Missing Docs:{" "}
                {leaveCompliance.missing_documents}, Avg Resp:{" "}
                {leaveCompliance.avg_manager_response_time}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <Card className="flex-1">
                  <CardHeader>
                    <CardTitle>Total Applications</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl">{leaveCompliance.total_applications}</p>
                  </CardContent>
                </Card>
                <Card className="flex-1">
                  <CardHeader>
                    <CardTitle>Late Submissions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl">{leaveCompliance.late_submissions}</p>
                  </CardContent>
                </Card>
                <Card className="flex-1">
                  <CardHeader>
                    <CardTitle>Missing Docs</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl">{leaveCompliance.missing_documents}</p>
                  </CardContent>
                </Card>
              </div>

              {/* Department compliance (bar chart) */}
              <h3 className="font-semibold">Compliance by Department</h3>
              {leaveCompliance.compliance_by_department?.length === 0 ? (
                <p>No department data.</p>
              ) : leaveCompliance.compliance_by_department ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={leaveCompliance.compliance_by_department}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="department_name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="total" fill="#8884d8" name="Total" />
                    <Bar dataKey="compliant" fill="#82ca9d" name="Compliant" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <p>No department data.</p>
              )}
            </CardContent>
          </Card>
        )}

        {/* ---------------------- LEAVE DASHBOARD ---------------------- */}
        {leaveDashboard && (
          <Card>
            <CardHeader>
              <CardTitle>Leave Dashboard</CardTitle>
              <CardDescription>
                Today’s Leaves: {leaveDashboard.today_leaves}, Pending Approvals:{" "}
                {leaveDashboard.pending_approvals}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 1) Upcoming Leaves Table */}
              <h3 className="font-semibold">Upcoming Leaves</h3>
              {leaveDashboard.upcoming_leaves?.length === 0 ? (
                <p>No upcoming leaves.</p>
              ) : leaveDashboard.upcoming_leaves ? (
                <div className="overflow-auto max-h-[200px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Employee Name</TableHead>
                        <TableHead>Start</TableHead>
                        <TableHead>End</TableHead>
                        <TableHead>Type</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {leaveDashboard.upcoming_leaves.map((item, idx) => (
                        <TableRow key={idx}>
                          <TableCell>{item.employee_name}</TableCell>
                          <TableCell>{item.start_date}</TableCell>
                          <TableCell>{item.end_date}</TableCell>
                          <TableCell>{item.leave_type}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <p>No upcoming leaves data.</p>
              )}

              {/* 2) Department impact (bar chart) */}
              <h3 className="font-semibold">Department Impact</h3>
              {leaveDashboard.department_impact?.length === 0 ? (
                <p>No department impact.</p>
              ) : leaveDashboard.department_impact ? (
                <ResponsiveContainer width="100%" height={200}>
                  <BarChart data={leaveDashboard.department_impact}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="department_name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8" name="Employees on Leave" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <p>No department impact data.</p>
              )}

              {/* 3) Leave Types Distribution (Pie) */}
              <h3 className="font-semibold">Leave Types Distribution</h3>
              {leaveTypesPieData.length === 0 ? (
                <p>No leave type distribution data.</p>
              ) : (
                <div className="flex justify-center">
                  <ResponsiveContainer width={300} height={300}>
                    <PieChart>
                      <Tooltip />
                      <Pie
                        data={leaveTypesPieData}
                        dataKey="value"
                        nameKey="name"
                        innerRadius={60}
                        outerRadius={100}
                        strokeWidth={2}
                        label
                      >
                        {leaveTypesPieData.map((entry, idx) => (
                          <Cell
                            key={`cell-${idx}`}
                            fill={`hsl(${(idx * 60) % 360}, 70%, 50%)`}
                          />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* ---------------------- LEAVE FORECAST ---------------------- */}
        {leaveForecast && (
          <Card>
            <CardHeader>
              <CardTitle>Leave Forecast</CardTitle>
              <CardDescription>
                Forecast for next {forecastMonths} months (Confidence ≥ {confidenceThreshold})
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 1) Forecast Table */}
              <h3 className="font-semibold">Forecast Predictions</h3>
              {leaveForecast.forecast?.length === 0 ? (
                <p>No forecast data.</p>
              ) : leaveForecast.forecast ? (
                <div className="overflow-auto max-h-[250px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Predicted Usage</TableHead>
                        <TableHead>Confidence</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {leaveForecast.forecast.map((f, idx) => (
                        <TableRow key={idx}>
                          <TableCell>{f.forecast_date}</TableCell>
                          <TableCell>{f.leave_type}</TableCell>
                          <TableCell>{f.predicted_usage}</TableCell>
                          <TableCell>{(f.confidence_level * 100).toFixed(1)}%</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <p>No forecast data.</p>
              )}

              {/* 2) Historical Averages (Bar or Radar) */}
              <h3 className="font-semibold">Historical Averages</h3>
              {leaveForecast.historical_averages?.length === 0 ? (
                <p>No historical data.</p>
              ) : leaveForecast.historical_averages ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={leaveForecast.historical_averages}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="avg_days" fill="#8884d8" name="Avg Days" />
                    <Bar dataKey="count" fill="#82ca9d" name="Count" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <p>No historical data.</p>
              )}
            </CardContent>
          </Card>
        )}

        {/* ---------------------- LEAVE LIABILITY ---------------------- */}
        {leaveLiability && (
          <Card>
            <CardHeader>
              <CardTitle>Leave Liability</CardTitle>
              <CardDescription>
                Financial Impact: {leaveLiability.financial_impact?.total_cost} (covering{" "}
                {leaveLiability.financial_impact?.total_days} days)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Approved Leaves</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl">
                      {leaveLiability.total_approved_leaves ?? 0}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Total Employees</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl">
                      {leaveLiability.total_employees ?? 0}
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Department Breakdown */}
              <h3 className="font-semibold">Department Breakdown</h3>
              {leaveLiability.department_breakdown?.length === 0 ? (
                <p>No department breakdown data.</p>
              ) : leaveLiability.department_breakdown ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={leaveLiability.department_breakdown}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="department_name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="total_cost" fill="#8884d8" name="Total Cost" />
                    <Bar dataKey="total_days" fill="#82ca9d" name="Total Days" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <p>No department breakdown data.</p>
              )}

              {/* Leave Type Breakdown */}
              <h3 className="font-semibold">Leave Type Breakdown</h3>
              {leaveLiability.leave_type_breakdown?.length === 0 ? (
                <p>No leave type breakdown data.</p>
              ) : leaveLiability.leave_type_breakdown ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={leaveLiability.leave_type_breakdown}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="leave_type_name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="total_cost" fill="#8884d8" name="Total Cost" />
                    <Bar dataKey="total_days" fill="#82ca9d" name="Total Days" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <p>No leave type breakdown data.</p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Navigation Button (optional) */}
        <div className="text-right">
          <Button onClick={() => navigate("/leave-management")}>Go to Leave Management</Button>
        </div>
      </div>
    </Screen>
  );
};

export default LeaveAnalyticsDashboard;
