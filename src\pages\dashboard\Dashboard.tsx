import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";
import { BASE_URL } from "../../config";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/redux/store";
import { logout } from "@/redux/features/auth/authSlice";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Users,
  BookOpen,
  Calendar,
  Umbrella,
  UserPlus,
  TrendingUp,
  Bell,
  ChevronRight,
  Clock,
  Target,
  AlertCircle,
  CheckCircle2,
  Building,
  RefreshCw,
} from "lucide-react";

const performanceData = [
  { month: "Jan", performance: 65 },
  { month: "Feb", performance: 75 },
  { month: "Mar", performance: 85 },
  { month: "Apr", performance: 82 },
  { month: "May", performance: 90 },
  { month: "Jun", performance: 88 },
];

const quickLinks = [
  {
    name: "Employee Management",
    route: "/employees-list",
    icon: Users,
    color: "bg-blue-100 dark:bg-blue-800",
  },
  {
    name: "Training",
    route: "/create-training-need",
    icon: BookOpen,
    color: "bg-green-100 dark:bg-green-800",
  },
  {
    name: "Calendar",
    route: "/calender-management",
    icon: Calendar,
    color: "bg-purple-100 dark:bg-purple-800",
  },
  {
    name: "Leave",
    route: "/leave-management",
    icon: Umbrella,
    color: "bg-yellow-100 dark:bg-yellow-800",
  },
  {
    name: "Recruitment",
    route: "/recruitment-dash",
    icon: UserPlus,
    color: "bg-pink-100 dark:bg-pink-800",
  },
  {
    name: "Performance",
    route: "/appraisal-periods",
    icon: TrendingUp,
    color: "bg-indigo-100 dark:bg-indigo-800",
  },
  {
    name: "Announcements",
    route: "/announcements",
    icon: Bell,
    color: "bg-red-100 dark:bg-red-800",
  },
];

const notifications = [
  {
    id: 1,
    title: "Leave Request",
    message: "New leave request from John Doe",
    time: "5m ago",
    icon: Umbrella,
  },
  {
    id: 2,
    title: "Training Complete",
    message: "Sarah completed compliance training",
    time: "1h ago",
    icon: CheckCircle2,
  },
  {
    id: 3,
    title: "Performance Review",
    message: "Upcoming review deadline",
    time: "2h ago",
    icon: Target,
  },
];

const upcomingEvents = [
  { id: 1, title: "Team Meeting", time: "10:00 AM", date: "Today" },
  { id: 2, title: "Performance Reviews", time: "2:00 PM", date: "Tomorrow" },
  { id: 3, title: "Training Workshop", time: "11:00 AM", date: "Next Week" },
];

export default function Dashboard() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const handleLogout = () => {
    dispatch(logout());
    navigate("/login");
  };
  const { user, token } = useSelector((state: RootState) => state.auth);
  const userName = `${user?.UserBio?.first_name ?? ""} ${
    user?.UserBio?.last_name ?? ""
  }`.trim();
  const [language, setLanguage] = useState("en");
  const [showNotifications, setShowNotifications] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // State for the actual data
  const [activeEmployees, setActiveEmployees] = useState(0);
  const [inactiveEmployees, setInactiveEmployees] = useState(0);
  const [departments, setDepartments] = useState([]);
  const [leaveRequests, setLeaveRequests] = useState(0); // This would come from a real API
  const [trainingProgress, setTrainingProgress] = useState(0); // This would come from a real API

  // Helper to normalize active status to boolean (similar to EmployeesList.tsx)
  const isActive = (status?: boolean | string): boolean => {
    return (
      status === true ||
      status === "true" ||
      status === "1" ||
      (typeof status === "string" && status.toLowerCase() === "true")
    );
  };

  useEffect(() => {
    const intervalId = setInterval(() => {
      setLanguage((prev) => (prev === "en" ? "sw" : "en"));
    }, 5000);
    return () => clearInterval(intervalId);
  }, []);

  // Fetch data from APIs
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch employees
        const employeesRes = await axios.get(
          `${BASE_URL}/employee_details/`,
          {
            params: { team: 'ALL', department: 'ALL' },
            headers: { Authorization: `Token ${token}` },
          }
        );

        // Fetch departments
        const departmentsRes = await axios.get(
          `${BASE_URL}/users/departments`,
          {
            headers: { Authorization: `Token ${token}` },
          }
        );

        // Set the data
        setDepartments(departmentsRes.data);

        // Calculate active and inactive employees
        const employeesList = employeesRes.data.results || [];
        const activeCount = employeesList.filter((emp: any) =>
          isActive(emp.is_active)
        ).length;
        const inactiveCount = employeesList.length - activeCount;
        setActiveEmployees(activeCount);
        setInactiveEmployees(inactiveCount);

        // In a real app, you would also fetch leave requests and training progress
        // For now, let's set some mock data
        setLeaveRequests(18);
        setTrainingProgress(89);

        setLoading(false);
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError("Failed to load dashboard data. Please try again.");
        setLoading(false);
      }
    };

    fetchData();
  }, [token]);

  const currentHour = new Date().getHours();
  let greetingEnglish, greetingSwahili;
  if (currentHour < 12) {
    greetingEnglish = "Good morning";
    greetingSwahili = "Habari ya asubuhi";
  } else if (currentHour < 18) {
    greetingEnglish = "Good afternoon";
    greetingSwahili = "Habari ya mchana";
  } else {
    greetingEnglish = "Good evening";
    greetingSwahili = "Habari ya jioni";
  }
  const greeting = language === "en" ? greetingEnglish : greetingSwahili;

  const greetingVariants = {
    initial: { opacity: 0, y: -10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 10 },
  };

  // Handle card clicks
  const handleCardClick = (route: string) => {
    navigate(route);
  };

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block" />
        <BreadcrumbItem>
          <BreadcrumbPage>Dashboard</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  // Loading state
  if (loading) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">
              Loading dashboard data...
            </p>
          </div>
        </div>
      </Screen>
    );
  }

  // Error state
  if (error) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="bg-red-100 dark:bg-red-900/30 p-6 rounded-lg max-w-md text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-red-700 dark:text-red-400 mb-2">
              Error Loading Dashboard
            </h2>
            <p className="text-gray-700 dark:text-gray-300">{error}</p>
            <button
              onClick={handleLogout}
              className="mt-4 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
            >
              <RefreshCw className="w-4 h-4 inline-block mr-2" />
              Retry
            </button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen headerContent={breadcrumb}>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex justify-between items-center">
          <AnimatePresence mode="wait">
            <motion.h1
              key={language}
              variants={greetingVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={{ duration: 0.5 }}
              className="text-3xl font-bold text-gray-900 dark:text-white"
            >
              {greeting}, {userName || "User"}
            </motion.h1>
          </AnimatePresence>

          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
            >
              <Bell className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Employee Card */}
          <Card
            className="bg-gradient-to-br from-blue-500 to-blue-600 text-white cursor-pointer transition-transform hover:scale-105"
            onClick={() => handleCardClick("/employees-list")}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Active Employees
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{activeEmployees}</div>
              <p className="text-sm opacity-80">
                {inactiveEmployees} inactive employees
              </p>
            </CardContent>
          </Card>

          {/* Departments Card */}
          <Card
            className="bg-gradient-to-br from-green-500 to-green-600 text-white cursor-pointer transition-transform hover:scale-105"
            onClick={() => handleCardClick("/departments")}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Building className="w-5 h-5 mr-2" />
                Departments
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{departments.length}</div>
              <p className="text-sm opacity-80">Manage departments</p>
            </CardContent>
          </Card>

          {/* Leave Requests Card */}
          <Card
            className="bg-gradient-to-br from-purple-500 to-purple-600 text-white cursor-pointer transition-transform hover:scale-105"
            onClick={() => handleCardClick("/leave-management")}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Umbrella className="w-5 h-5 mr-2" />
                Leave Requests
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{leaveRequests}</div>
              <p className="text-sm opacity-80">5 pending approval</p>
            </CardContent>
          </Card>

          {/* Training Progress Card */}
          <Card
            className="bg-gradient-to-br from-orange-500 to-orange-600 text-white cursor-pointer transition-transform hover:scale-105"
            onClick={() => handleCardClick("/create-training-need")}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <BookOpen className="w-5 h-5 mr-2" />
                Training Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{trainingProgress}%</div>
              <p className="text-sm opacity-80">+5% from last month</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Links */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Frequently used HR functions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {quickLinks.map((link) => {
                const Icon = link.icon;
                return (
                  <Link
                    key={link.name}
                    to={link.route}
                    className={`flex flex-col items-center justify-center p-4 rounded-xl transition-all hover:scale-105 ${link.color}`}
                  >
                    <Icon className="w-8 h-8 mb-2" />
                    <span className="text-sm font-medium text-center">
                      {link.name}
                    </span>
                  </Link>
                );
              })}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Performance Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
              <CardDescription>Monthly performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="performance"
                      stroke="#8884d8"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activities</CardTitle>
              <CardDescription>
                Latest HR updates and notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {notifications.map((notification) => {
                  const Icon = notification.icon;
                  return (
                    <div
                      key={notification.id}
                      className="flex items-start space-x-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-800"
                    >
                      <div className="p-2 rounded-full bg-green-100 dark:bg-green-900">
                        <Icon className="w-4 h-4 text-green-600 dark:text-green-300" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium">
                          {notification.title}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {notification.message}
                        </p>
                        <span className="text-xs text-gray-400">
                          {notification.time}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Events and Tasks */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Events</CardTitle>
              <CardDescription>Schedule for the next few days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div
                    key={event.id}
                    className="flex items-center space-x-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-800"
                  >
                    <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900">
                      <Clock className="w-4 h-4 text-purple-600 dark:text-purple-300" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">{event.title}</h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {event.time} - {event.date}
                      </p>
                    </div>
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>HR Insights</CardTitle>
              <CardDescription>Key metrics and trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Employee Satisfaction
                    </p>
                    <h4 className="text-2xl font-bold">4.5/5</h4>
                    <span className="text-xs text-green-500">
                      ↑ 0.3 from last month
                    </span>
                  </div>
                  <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Retention Rate
                    </p>
                    <h4 className="text-2xl font-bold">95%</h4>
                    <span className="text-xs text-green-500">
                      ↑ 2% from last quarter
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
}

export { Dashboard };
