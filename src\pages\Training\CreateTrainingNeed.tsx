"use client"

import { useState } from "react"
import { Screen } from "@/app-components/layout/screen"
import { TrainingNeedForm } from "../../app-components/training/TrainingNeedForm"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { BASE_URL } from "@/config"
import { useSelector } from "react-redux"
import { RootState } from "@/redux/store"

export default function CreateTrainingNeed() {
  const [loading, setLoading] = useState(false)
  const token = useSelector((state: RootState) => state.auth.token);

  const handleCreate = async (data: {
    trainingName: string
    description: string
    evaluationLink: string
    evaluationFile: File | null
  }) => {
    setLoading(true)
    try {
      console.log("Token in Redux store:", token);

      const response = await fetch(`${BASE_URL}/hrm/training-content-details`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },        
        body: JSON.stringify({
          title: data.trainingName,
          description: data.description,
        }),
      })
      if (!response.ok) {
        throw new Error("Failed to create training content")
      }
      const created = await response.json()
      console.log("Training Need Created:", created)

      // If you need to handle file uploads, do that separately
      // or switch to multipart/form-data logic.
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Create Training Need
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  )

  return (
    <Screen headerContent={breadcrumb}>
      <TrainingNeedForm onSubmit={handleCreate} />
      {loading && <p className="mt-2 text-sm text-gray-600">Creating...</p>}
    </Screen>
  )
}
