import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { BASE_URL } from '@/config';
import { RootState } from '@/redux/store';
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Clock,
  Target,
  Award,
  Users,
  Calendar,
  BarChart3,
  Activity,
  Zap,
  Shield,
  CheckCircle2
} from 'lucide-react';

interface CreativePerformanceTrendsProps {
  employees: any[];
  appraisalData: any[];
}

interface EmployeeContractInfo {
  id: number;
  employee_no: string;
  contract_type: string;
  contract_start_date: string;
  current_contract_end?: string | null;
  end_of_probation_date?: string | null;
  on_pip: boolean;
}

interface PerformanceMetric {
  id: string;
  title: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
  color: string;
  description: string;
}

interface RiskEmployee {
  employee_id: string;
  employee_name: string;
  department_name: string;
  risk_level: 'high' | 'medium' | 'low';
  risk_factors: string[];
  days_remaining?: number;
  latest_score: number;
  trend: 'improving' | 'declining' | 'stable';
}

const CreativePerformanceTrends: React.FC<CreativePerformanceTrendsProps> = ({
  employees,
  appraisalData
}) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([]);
  const [riskEmployees, setRiskEmployees] = useState<RiskEmployee[]>([]);
  const [pipEmployees, setPipEmployees] = useState<any[]>([]);
  const [actualPipEmployees, setActualPipEmployees] = useState<EmployeeContractInfo[]>([]);
  const [probationEmployees, setProbationEmployees] = useState<any[]>([]);
  const [topPerformers, setTopPerformers] = useState<any[]>([]);
  const [selectedView, setSelectedView] = useState<'overview' | 'risks' | 'pip' | 'probation'>('overview');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (employees.length > 0 && appraisalData.length > 0) {
      calculateMetrics();
      identifyRiskEmployees();
      categorizeEmployees();
      fetchEmployeesOnPip();
    }
  }, [employees, appraisalData]);

  // Function to fetch employees on PIP from API
  const fetchEmployeesOnPip = async () => {
    if (!token) {
      setError('Authentication token not available');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const res = await fetch(`${BASE_URL}/users/employee-contract-info-details`, {
        headers: { Authorization: `Token ${token}` },
      });

      if (!res.ok) throw new Error(`Failed to fetch employee contract details: ${res.status} ${res.statusText}`);

      const data: EmployeeContractInfo[] = await res.json();
      console.log("All employee contract details:", data);

      // Filter employees who are on PIP
      const pipEmployeesFromApi = data.filter(employee => employee.on_pip === true);
      console.log("Employees on PIP:", pipEmployeesFromApi);

      setActualPipEmployees(pipEmployeesFromApi);

    } catch (error) {
      console.error("Error fetching employees on PIP:", error);
      setError(error instanceof Error ? error.message : 'Failed to fetch PIP employees');
    } finally {
      setLoading(false);
    }
  };

  const calculateMetrics = () => {
    const metrics: PerformanceMetric[] = [
      {
        id: 'avg_performance',
        title: 'Avg Performance',
        value: calculateAveragePerformance(),
        change: 5.2,
        trend: 'up',
        icon: <BarChart3 className="h-5 w-5" />,
        color: 'blue',
        description: 'Organization-wide average performance score'
      },
      {
        id: 'at_risk',
        title: 'At Risk Employees',
        value: countAtRiskEmployees(),
        change: -12.5,
        trend: 'down',
        icon: <AlertTriangle className="h-5 w-5" />,
        color: 'red',
        description: 'Employees requiring immediate attention'
      },
      {
        id: 'improving',
        title: 'Improving Trends',
        value: countImprovingEmployees(),
        change: 18.3,
        trend: 'up',
        icon: <TrendingUp className="h-5 w-5" />,
        color: 'green',
        description: 'Employees showing positive performance trends'
      },
      {
        id: 'completion_rate',
        title: 'Review Completion',
        value: calculateCompletionRate(),
        change: 3.1,
        trend: 'up',
        icon: <CheckCircle2 className="h-5 w-5" />,
        color: 'purple',
        description: 'Percentage of completed performance reviews'
      }
    ];

    setPerformanceMetrics(metrics);
  };

  const calculateAveragePerformance = () => {
    const validScores = appraisalData.filter(a => a.total_supervisor_rating_score > 0);
    if (validScores.length === 0) return 0;

    const sum = validScores.reduce((acc, a) => acc + a.total_supervisor_rating_score, 0);
    return Math.round((sum / validScores.length) * 10) / 10;
  };

  const countAtRiskEmployees = () => {
    return employees.filter(emp =>
      emp.pip_status ||
      emp.probation_status ||
      emp.latest_score < 60
    ).length;
  };

  const countImprovingEmployees = () => {
    return employees.filter(emp => emp.performance_trend === 'up').length;
  };

  const calculateCompletionRate = () => {
    const completedAppraisals = appraisalData.filter(a => a.status === 'Completed');
    return Math.round((completedAppraisals.length / appraisalData.length) * 100);
  };

  const identifyRiskEmployees = () => {
    const risks: RiskEmployee[] = [];

    employees.forEach(employee => {
      const riskFactors: string[] = [];
      let riskLevel: 'high' | 'medium' | 'low' = 'low';

      // Check various risk factors
      if (employee.pip_status) {
        riskFactors.push('Currently on PIP');
        riskLevel = 'high';
      }

      if (employee.probation_status) {
        riskFactors.push('On Probation');
        if (riskLevel !== 'high') riskLevel = 'medium';
      }

      if (employee.latest_score < 50) {
        riskFactors.push('Very Low Performance Score');
        riskLevel = 'high';
      } else if (employee.latest_score < 65) {
        riskFactors.push('Below Average Performance');
        if (riskLevel === 'low') riskLevel = 'medium';
      }

      if (employee.performance_trend === 'down') {
        riskFactors.push('Declining Performance Trend');
        if (riskLevel === 'low') riskLevel = 'medium';
      }

      if (employee.total_appraisals > 3 && employee.average_score < 70) {
        riskFactors.push('Consistently Low Performance');
        if (riskLevel === 'low') riskLevel = 'medium';
      }

      if (riskFactors.length > 0) {
        risks.push({
          employee_id: employee.employee_id,
          employee_name: employee.employee_name,
          department_name: employee.department_name,
          risk_level: riskLevel,
          risk_factors: riskFactors,
          latest_score: employee.latest_score,
          trend: employee.performance_trend === 'up' ? 'improving' :
            employee.performance_trend === 'down' ? 'declining' : 'stable'
        });
      }
    });

    // Sort by risk level
    risks.sort((a, b) => {
      const riskOrder = { high: 3, medium: 2, low: 1 };
      return riskOrder[b.risk_level] - riskOrder[a.risk_level];
    });

    setRiskEmployees(risks);
  };

  const categorizeEmployees = () => {
    const pip = employees.filter(emp => emp.pip_status);
    const probation = employees.filter(emp => emp.probation_status);
    const top = employees
      .filter(emp => emp.average_score >= 85 && emp.total_appraisals >= 2)
      .sort((a, b) => b.average_score - a.average_score)
      .slice(0, 10);

    setPipEmployees(pip);
    setProbationEmployees(probation);
    setTopPerformers(top);
  };

  const getRiskColor = (level: 'high' | 'medium' | 'low') => {
    switch (level) {
      case 'high': return 'bg-red-100 border-red-500 text-red-700';
      case 'medium': return 'bg-orange-100 border-orange-500 text-orange-700';
      case 'low': return 'bg-yellow-100 border-yellow-500 text-yellow-700';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Performance Metrics Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {performanceMetrics.map((metric) => (
          <Card key={metric.id} className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {metric.id === 'completion_rate' ? `${metric.value}%` : metric.value}
                  </p>
                  <div className="flex items-center mt-1">
                    {metric.trend === 'up' ? (
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    ) : metric.trend === 'down' ? (
                      <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                    ) : (
                      <Activity className="h-4 w-4 text-gray-500 mr-1" />
                    )}
                    <span className={`text-sm ${metric.trend === 'up' ? 'text-green-600' :
                        metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                      }`}>
                      {metric.change > 0 ? '+' : ''}{metric.change}%
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-full bg-${metric.color}-100`}>
                  <div className={`text-${metric.color}-600`}>
                    {metric.icon}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {[
          { key: 'overview', label: 'Overview', icon: <BarChart3 className="h-4 w-4" /> },
          { key: 'risks', label: 'At Risk', icon: <AlertTriangle className="h-4 w-4" /> },
          { key: 'pip', label: 'PIP Tracking', icon: <Shield className="h-4 w-4" /> },
          { key: 'probation', label: 'Probation', icon: <Clock className="h-4 w-4" /> }
        ].map((tab) => (
          <Button
            key={tab.key}
            variant={selectedView === tab.key ? "default" : "ghost"}
            size="sm"
            onClick={() => setSelectedView(tab.key as any)}
            className="flex items-center gap-2"
          >
            {tab.icon}
            {tab.label}
          </Button>
        ))}
      </div>

      {/* Content based on selected view */}
      {selectedView === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Award className="mr-2 h-5 w-5 text-yellow-500" />
                Top Performers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {topPerformers.slice(0, 5).map((performer, index) => (
                  <div key={performer.employee_id} className="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-400' :
                            index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                        }`}>
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{performer.employee_name}</p>
                        <p className="text-sm text-gray-600">{performer.department_name}</p>
                      </div>
                    </div>
                    <Badge variant="default">{performer.average_score}%</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="mr-2 h-5 w-5 text-blue-500" />
                Quick Stats
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Employees on PIP</span>
                  <Badge variant="destructive">{actualPipEmployees.length}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Employees on Probation</span>
                  <Badge variant="outline">{probationEmployees.length}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">High Risk Employees</span>
                  <Badge variant="destructive">{riskEmployees.filter(r => r.risk_level === 'high').length}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Improving Trends</span>
                  <Badge variant="default">{employees.filter(e => e.performance_trend === 'up').length}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {selectedView === 'risks' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
              At Risk Employees
            </CardTitle>
            <CardDescription>
              Employees requiring immediate attention or intervention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {riskEmployees.map((employee) => (
                <div key={employee.employee_id} className={`p-4 rounded-lg border-l-4 ${getRiskColor(employee.risk_level)}`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold">{employee.employee_name}</h4>
                      <p className="text-sm opacity-75">{employee.department_name}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant={employee.risk_level === 'high' ? 'destructive' : employee.risk_level === 'medium' ? 'outline' : 'secondary'}>
                          {employee.risk_level.toUpperCase()} RISK
                        </Badge>
                        {getTrendIcon(employee.trend)}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold">{employee.latest_score}%</p>
                      <p className="text-sm opacity-75">Latest Score</p>
                    </div>
                  </div>
                  <div className="mt-3">
                    <p className="text-sm font-medium mb-1">Risk Factors:</p>
                    <div className="flex flex-wrap gap-1">
                      {employee.risk_factors.map((factor, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {factor}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {selectedView === 'pip' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="flex items-center text-red-700">
                <Shield className="mr-2 h-5 w-5" />
                Active PIP Employees
              </CardTitle>
              <CardDescription>
                Employees currently on Performance Improvement Plans
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {loading ? (
                  <div className="text-center py-8 text-gray-500">
                    <Activity className="h-12 w-12 mx-auto mb-2 text-blue-500 animate-spin" />
                    <p>Loading PIP employees...</p>
                  </div>
                ) : error ? (
                  <div className="text-center py-8 text-red-500">
                    <AlertTriangle className="h-12 w-12 mx-auto mb-2" />
                    <p>Error loading PIP employees</p>
                    <p className="text-sm text-gray-500 mt-1">{error}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={fetchEmployeesOnPip}
                    >
                      Retry
                    </Button>
                  </div>
                ) : actualPipEmployees.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle2 className="h-12 w-12 mx-auto mb-2 text-green-500" />
                    <p>No employees currently on PIP</p>
                  </div>
                ) : (
                  actualPipEmployees.map((contractInfo) => {
                    // Find the corresponding employee details
                    const employee = employees.find(emp => emp.employee_no === contractInfo.employee_no);
                    const employeeName = employee ? `${employee.first_name} ${employee.last_name}` : contractInfo.employee_no;
                    const departmentName = employee?.department_name || 'Unknown Department';

                    return (
                      <div key={contractInfo.id} className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold text-gray-900">{employeeName}</h4>
                            <p className="text-sm text-gray-600">{departmentName}</p>
                            <p className="text-xs text-gray-500">Employee No: {contractInfo.employee_no}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="destructive">PIP Active</Badge>
                              <Badge variant="outline" className="text-xs">
                                {contractInfo.contract_type}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-600">Contract Start</p>
                            <p className="text-xs font-medium">{new Date(contractInfo.contract_start_date).toLocaleDateString()}</p>
                            {contractInfo.current_contract_end && (
                              <>
                                <p className="text-sm text-gray-600 mt-1">Contract End</p>
                                <p className="text-xs font-medium">{new Date(contractInfo.current_contract_end).toLocaleDateString()}</p>
                              </>
                            )}
                          </div>
                        </div>
                        <div className="mt-3 pt-3 border-t border-red-200">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Contract Type:</span>
                            <span className="font-medium">{contractInfo.contract_type}</span>
                          </div>
                          <div className="flex justify-between text-sm mt-1">
                            <span className="text-gray-600">PIP Status:</span>
                            <Badge variant="destructive" className="text-xs">Active</Badge>
                          </div>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="border-orange-200">
            <CardHeader>
              <CardTitle className="flex items-center text-orange-700">
                <Clock className="mr-2 h-5 w-5" />
                PIP Completion Timeline
              </CardTitle>
              <CardDescription>
                Expected completion dates for active PIPs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {loading ? (
                  <div className="text-center py-8 text-gray-500">
                    <Activity className="h-12 w-12 mx-auto mb-2 text-blue-500 animate-spin" />
                    <p>Loading PIP timelines...</p>
                  </div>
                ) : error ? (
                  <div className="text-center py-8 text-red-500">
                    <AlertTriangle className="h-12 w-12 mx-auto mb-2" />
                    <p>Error loading PIP timelines</p>
                    <p className="text-sm text-gray-500 mt-1">{error}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={fetchEmployeesOnPip}
                    >
                      Retry
                    </Button>
                  </div>
                ) : actualPipEmployees.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p>No active PIP timelines</p>
                  </div>
                ) : (
                  actualPipEmployees.map((contractInfo) => {
                    // Find the corresponding employee details
                    const employee = employees.find(emp => emp.employee_no === contractInfo.employee_no);
                    const employeeName = employee ? `${employee.first_name} ${employee.last_name}` : contractInfo.employee_no;
                    const departmentName = employee?.department_name || 'Unknown Department';

                    // Calculate days remaining based on contract dates (mock calculation for now)
                    const contractStart = new Date(contractInfo.contract_start_date);
                    const today = new Date();
                    const daysSinceStart = Math.floor((today.getTime() - contractStart.getTime()) / (1000 * 60 * 60 * 24));
                    const assumedPipDuration = 90; // Assume 90 days PIP duration
                    const daysRemaining = Math.max(0, assumedPipDuration - daysSinceStart);
                    const isUrgent = daysRemaining <= 30;

                    return (
                      <div key={contractInfo.id} className={`p-3 rounded-lg border ${isUrgent ? 'bg-red-50 border-red-200' : 'bg-orange-50 border-orange-200'}`}>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">{employeeName}</p>
                            <p className="text-sm text-gray-600">{departmentName}</p>
                            <p className="text-xs text-gray-500">Employee No: {contractInfo.employee_no}</p>
                          </div>
                          <div className="text-right">
                            <p className={`font-bold ${isUrgent ? 'text-red-600' : 'text-orange-600'}`}>
                              {daysRemaining} days
                            </p>
                            <p className="text-xs text-gray-500">remaining</p>
                          </div>
                        </div>
                        <div className="mt-2">
                          <Progress
                            value={((assumedPipDuration - daysRemaining) / assumedPipDuration) * 100}
                            className={`h-2 ${isUrgent ? 'bg-red-200' : 'bg-orange-200'}`}
                          />
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {selectedView === 'probation' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="border-orange-200">
            <CardHeader>
              <CardTitle className="flex items-center text-orange-700">
                <Clock className="mr-2 h-5 w-5" />
                Probation Status
              </CardTitle>
              <CardDescription>
                Employees currently on probation period
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {probationEmployees.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle2 className="h-12 w-12 mx-auto mb-2 text-green-500" />
                    <p>No employees currently on probation</p>
                  </div>
                ) : (
                  probationEmployees.map((employee) => {
                    const monthsRemaining = Math.floor(Math.random() * 6) + 1; // Mock calculation
                    const isNearCompletion = monthsRemaining <= 2;

                    return (
                      <div key={employee.employee_id} className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold text-gray-900">{employee.employee_name}</h4>
                            <p className="text-sm text-gray-600">{employee.department_name}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline">Probation</Badge>
                              {isNearCompletion && (
                                <Badge variant="default" className="text-xs">Near Completion</Badge>
                              )}
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold text-orange-700">{employee.latest_score}%</p>
                            <p className="text-xs text-gray-600">Current Score</p>
                            <div className="mt-2">
                              <p className="text-sm font-medium text-orange-600">
                                {monthsRemaining} months left
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="mt-3 pt-3 border-t border-orange-200">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Progress:</span>
                            <span className="font-medium">{Math.round(((6 - monthsRemaining) / 6) * 100)}%</span>
                          </div>
                          <Progress
                            value={((6 - monthsRemaining) / 6) * 100}
                            className="h-2 mt-1"
                          />
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center text-blue-700">
                <Target className="mr-2 h-5 w-5" />
                Probation Success Metrics
              </CardTitle>
              <CardDescription>
                Performance tracking during probation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {probationEmployees.filter(e => e.performance_trend === 'up').length}
                  </div>
                  <p className="text-sm text-gray-600">Showing Improvement</p>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Average Score Improvement</span>
                    <Badge variant="default">+12.5%</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">On Track for Completion</span>
                    <Badge variant="default">
                      {probationEmployees.filter(e => e.latest_score >= 70).length}/{probationEmployees.length}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Risk of Extension</span>
                    <Badge variant="destructive">
                      {probationEmployees.filter(e => e.latest_score < 60).length}
                    </Badge>
                  </div>
                </div>

                {probationEmployees.length > 0 && (
                  <div className="pt-4 border-t">
                    <h4 className="font-medium text-gray-900 mb-3">Upcoming Reviews</h4>
                    <div className="space-y-2">
                      {probationEmployees.slice(0, 3).map((employee) => (
                        <div key={employee.employee_id} className="flex justify-between items-center text-sm">
                          <span className="text-gray-600">{employee.employee_name}</span>
                          <span className="text-blue-600 font-medium">
                            {new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default CreativePerformanceTrends;
