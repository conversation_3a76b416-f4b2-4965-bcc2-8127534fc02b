"use client";

import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
  TableCaption,
} from "@/components/ui/table";

import VacancyDetailsDialog from "../dialogs/VacancyDetailsDialog";
import { JobVacancy } from "../types";
import { BASE_URL } from "@/config";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

const OpenRequisitions: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const [vacancies, setVacancies] = useState<JobVacancy[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState<boolean>(false);
  const [selectedVacancyId, setSelectedVacancyId] = useState<number | null>(null);

  // Offer Letter Template Creation states
  const [offerTemplateDialogOpen, setOfferTemplateDialogOpen] = useState<boolean>(false);
  const [selectedVacancyOfferTemplateId, setSelectedVacancyOfferTemplateId] = useState<number | null>(null);
  const [templateName, setTemplateName] = useState<string>("");
  const [offerTemplateFile, setOfferTemplateFile] = useState<File | null>(null);
  const [offerTemplateLoading, setOfferTemplateLoading] = useState<boolean>(false);
  const [offerTemplateError, setOfferTemplateError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVacancies = async () => {
      setLoading(true);
      try {
        const res = await fetch(`${BASE_URL}/hrm/vacancies`, {
          headers: { "Content-Type": "application/json" },
        });
        if (!res.ok) {
          throw new Error("Failed to fetch vacancies");
        }
        const data: JobVacancy[] = await res.json();
        setVacancies(data);
      } catch (err: any) {
        setError(err.message || "Error fetching vacancies");
      } finally {
        setLoading(false);
      }
    };
    fetchVacancies();
  }, [token]);

  const handleViewClick = (id: number) => {
    setSelectedVacancyId(id);
    setViewDialogOpen(true);
  };

  const handleOpenOfferTemplateDialog = (vacancyId: number) => {
    setSelectedVacancyOfferTemplateId(vacancyId);
    setTemplateName(""); // reset input
    setOfferTemplateFile(null); // reset file
    setOfferTemplateError(null);
    setOfferTemplateDialogOpen(true);
  };

  const handleOfferTemplateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!templateName || !selectedVacancyOfferTemplateId || !offerTemplateFile || !token) {
      setOfferTemplateError("Missing required information.");
      console.error("Missing required information:", {
        templateName,
        selectedVacancyOfferTemplateId,
        offerTemplateFile,
        token,
      });
      return;
    }

    // Fetch organisation from the selected vacancy.
    const vacancy = vacancies.find((vac) => vac.id === selectedVacancyOfferTemplateId);
    if (!vacancy) {
      setOfferTemplateError("Selected vacancy not found.");
      console.error("Selected vacancy not found for ID:", selectedVacancyOfferTemplateId);
      return;
    }
    const organisationId = vacancy.job_details.organisation;

    setOfferTemplateLoading(true);
    setOfferTemplateError(null);

    try {
      const formData = new FormData();
      // Build payload per API docs:
      formData.append("template_name", templateName);
      formData.append("organisation", organisationId.toString());
      formData.append("vacancy", selectedVacancyOfferTemplateId.toString());
      formData.append("template_file", offerTemplateFile);

      // Log payload for debugging
      console.log("Offer Letter Template Payload:");
      formData.forEach((value, key) => {
        console.log(`${key}: ${value}`);
      });

      const response = await fetch(`${BASE_URL}/recruitment/offer-letter-templates`, {
        method: "POST",
        headers: {
          // Let the browser set Content-Type with boundary.
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Template creation failed with status:", response.status, errorText);
        throw new Error("Failed to create offer letter template.");
      }
      console.log("Offer letter template created successfully.");
      setOfferTemplateDialogOpen(false);
      setTemplateName("");
      setOfferTemplateFile(null);
    } catch (error: any) {
      console.error("Error creating offer letter template:", error);
      setOfferTemplateError(error.message || "Error creating offer letter template.");
    } finally {
      setOfferTemplateLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded shadow">
      <h2 className="text-xl font-bold mb-4">Open Requisitions</h2>
      {loading ? (
        <p>Loading vacancies...</p>
      ) : error ? (
        <p className="text-red-500">{error}</p>
      ) : vacancies.length === 0 ? (
        <p>No vacancies found.</p>
      ) : (
        <div className="overflow-auto">
          <table className="w-full text-sm">
            <TableCaption>List of Vacancies</TableCaption>
            <thead className="border-b">
              <tr>
                <TableHead>Job Title</TableHead>
                <TableHead className="text-center">Start Date</TableHead>
                <TableHead className="text-center">Status</TableHead>
                <TableHead className="text-center">Actions</TableHead>
              </tr>
            </thead>
            <TableBody>
              {vacancies.map((vac) => (
                <TableRow
                  key={vac.id}
                  className="border-b hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <TableCell>
                    <div className="font-medium">{vac.job_details.job_title}</div>
                    <div className="text-xs text-gray-500">{vac.job_details.job_code}</div>
                  </TableCell>
                  <TableCell className="text-center">
                    {vac.vacancy_start_date || "N/A"}
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="outline">{vac.vacanc_status}</Badge>
                  </TableCell>
                  <TableCell className="text-center flex flex-col gap-2 items-center">
                    <Button size="sm" onClick={() => handleViewClick(vac.id)}>
                      View
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOpenOfferTemplateDialog(vac.id)}
                    >
                      Upload Offer Letter
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </table>
        </div>
      )}
      {selectedVacancyId !== null && (
        <VacancyDetailsDialog
          vacancyId={selectedVacancyId}
          open={viewDialogOpen}
          onClose={() => setViewDialogOpen(false)}
        />
      )}

      {/* Offer Letter Template Creation Dialog */}
      <Dialog open={offerTemplateDialogOpen} onOpenChange={setOfferTemplateDialogOpen}>
        <DialogContent
          className="max-w-md"
          aria-describedby="offer-letter-template-description"
        >
          <DialogHeader>
            <DialogTitle>Upload Offer Letter</DialogTitle>
          </DialogHeader>
          <p id="offer-letter-template-description" className="text-sm">
            Enter a template name and upload the offer letter file. This template will be tied to the selected vacancy.
          </p>
          <form onSubmit={handleOfferTemplateSubmit}>
            <div className="space-y-4">
              <div>
                <Label htmlFor="templateName">Template Name</Label>
                <input
                  type="text"
                  id="templateName"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="Enter template name"
                  className="block w-full mt-1 p-2 border rounded"
                  required
                />
              </div>
              <div>
                <Label htmlFor="offerTemplateFile">Offer Letter File (Word or PDF)</Label>
                <input
                  type="file"
                  accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf"
                  id="offerTemplateFile"
                  onChange={(e) => setOfferTemplateFile(e.target.files?.[0] || null)}
                  className="block w-full mt-1"
                  required
                />
              </div>
              {offerTemplateError && (
                <p className="text-red-500 text-sm">{offerTemplateError}</p>
              )}
            </div>
            <DialogFooter className="mt-4 flex justify-end space-x-2">
              <Button type="button" onClick={() => setOfferTemplateDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={offerTemplateLoading}>
                {offerTemplateLoading ? "Uploading..." : "Upload"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OpenRequisitions;
