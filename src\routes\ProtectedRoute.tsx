import { Navigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";

const ProtectedRoute = ({ children }: { children: JSX.Element }) => {
  const { token, passwordChangeRequired } = useSelector((state: RootState) => state.auth);

  if (!token) {
    return <Navigate to="/login" replace />;
  }
  if (passwordChangeRequired) {
    return <Navigate to="/force-password-reset" replace />;
  }

  return children;
};

export default ProtectedRoute;
