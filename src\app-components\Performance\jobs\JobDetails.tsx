// src/components/performance/jobs/JobDetails.tsx
import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import {
  ArrowLeft,
  Briefcase,
  FileText,
  Users,
  CheckSquare,
} from 'lucide-react';

interface JobDetailsProps {
  job: any;
  onBack: () => void;
  onEdit: () => void;
}
import { Screen } from '@/app-components/layout/screen';


const DetailCard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="p-6 bg-white rounded-2xl shadow-md mb-6">
    {children}
  </div>
);

const SectionHeader: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="flex items-center gap-4 mb-6">{children}</div>
);


const Chip: React.FC<{ status: string }> = ({ status }) => {
  let bgColor = '';
  let textColor = '';
  if (status === 'active') {
    bgColor = 'bg-green-100';
    textColor = 'text-green-800';
  } else if (status === 'closed') {
    bgColor = 'bg-red-100';
    textColor = 'text-red-800';
  } else {
    bgColor = 'bg-yellow-100';
    textColor = 'text-yellow-800';
  }
  return (
    <span className={`ml-2 inline-block px-3 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
      {status}
    </span>
  );
};

const JobDetails: React.FC<JobDetailsProps> = ({ job, onBack, onEdit }) => {
  return (
    <Screen>
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
      <div className="mb-8">
        {/* Header Section */}
        <div className="flex justify-between items-center mb-8">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to List
          </Button>
          <div className="flex gap-4">
            <Button onClick={onEdit}>
              Edit Position
            </Button>
          </div>
        </div>

        <DetailCard>
          <SectionHeader>
            {/* Avatar Replacement */}
            <div className="flex-shrink-0">
              <div className="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center">
                <Briefcase className="h-5 w-5 text-white" />
              </div>
            </div>
            <h1 className="text-2xl font-bold">{job.job_title}</h1>
            <Chip status={job.posission_status} />
          </SectionHeader>

          {/* Grid for Position Details and Requirements */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column: Position Details */}
            <div>
              <SectionHeader>
                <FileText className="h-5 w-5 text-green-500" />
                <h2 className="text-xl font-semibold">Position Details</h2>
              </SectionHeader>
              <p className="text-base mb-4">{job.job_description}</p>
              <div className="mb-6">
                <p className="text-sm font-medium mb-1 flex items-center">
                  Ksh
                  Salary Range: ${job.job_min_salary} - ${job.job_max_salary}
                </p>
                <p className="text-sm font-medium flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  Employees Required: {job.no_of_employees}
                </p>
              </div>
            </div>

            {/* Right Column: Requirements */}
            <div>
              <SectionHeader>
                <CheckSquare className="h-5 w-5 text-green-500" />
                <h2 className="text-xl font-semibold">Requirements</h2>
              </SectionHeader>
              <div className="mb-6">
                <p className="text-sm font-semibold mb-1">Skills:</p>
                <p className="text-sm mb-4">{job.job_skills}</p>
                <p className="text-sm font-semibold mb-1">Experience:</p>
                <p className="text-sm">{job.job_experience}</p>
              </div>
            </div>
          </div>

          <hr className="my-6 border-t border-gray-200" />
        </DetailCard>
      </div>
    </motion.div>
    </Screen> 
  );
};

export default JobDetails;
