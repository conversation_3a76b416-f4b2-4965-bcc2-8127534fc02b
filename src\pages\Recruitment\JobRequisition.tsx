import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";

// Import shadcn UI components (adjust paths as needed)
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
  } from "@/components/ui/breadcrumb";
import { Alert } from "@/components/ui/alert";
import { Loader2 } from "lucide-react"; // spinner icon
import { Screen } from "@/app-components/layout/screen";
 import { BASE_URL } from "@/config";

const positionStatuses = ["Open", "Closed", "Pending Approval"];
const departments = ["Engineering", "Marketing", "Sales", "HR", "Finance"];

interface Job {
  id: number;
  job_code: string;
  job_title: string;
  job_description: string;
  job_responsibilities: string;
  job_requirements: string;
  job_qualifications: string;
  job_experience: string;
  job_skills: string;
  job_min_salary: number;
  job_max_salary: number;
  posission_status: string;
  no_of_employees: number;
  required_proffesional_body: string;
  accademic_qualification: string;
  professional_requirements: string;
  organisation: number;
  department: number;
  created_by: string;
}

interface JobVacancy {
  vacanc_status: "Pending Approval" | "Approved" | "open" | "Rejected";
  job: string;
  created_by: string;
  vacancy_start_date?: string;
  vacancy_end_date?: string;
}

export default function JobRequisitionForm() {
 
  const token = useSelector((state: RootState) => state.auth.token);

  const [jobTitle, setJobTitle] = useState("");
  const [jobCode, setJobCode] = useState("");
  const [jobDescription, setJobDescription] = useState("");
  const [jobResponsibilities, setJobResponsibilities] = useState("");
  const [jobRequirements, setJobRequirements] = useState("");
  const [jobQualifications, setJobQualifications] = useState("");
  const [jobExperience, setJobExperience] = useState("");
  const [jobSkills, setJobSkills] = useState("");
  const [jobMinSalary, setJobMinSalary] = useState<number>(0);
  const [jobMaxSalary, setJobMaxSalary] = useState<number>(0);
  const [positionStatus, setPositionStatus] = useState("");
  const [noOfEmployees, setNoOfEmployees] = useState<number>(1);
  const [requiredProfessionalBody, setRequiredProfessionalBody] = useState("");
  const [academicQualification, setAcademicQualification] = useState("");
  const [professionalRequirements, setProfessionalRequirements] = useState("");
  const [organisation, setOrganisation] = useState<number>(0);
  const [department, setDepartment] = useState<number>(0);
  const [createdBy, setCreatedBy] = useState("");

 
  const [existingJobs, setExistingJobs] = useState<Job[]>([]);
  const [prepopulate, setPrepopulate] = useState<boolean>(false);
  const [selectedExistingJob, setSelectedExistingJob] = useState<string>("");
  const [loadingJobs, setLoadingJobs] = useState<boolean>(false);
  const [loadingSave, setLoadingSave] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [confirmedDetails, setConfirmedDetails] = useState<boolean>(false);

  
  useEffect(() => {
    const fetchJobs = async () => {
      setLoadingJobs(true);
      try {
        const response = await fetch(`${BASE_URL}/hrm/job-positions`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        });
        if (!response.ok) {
          const errorData = await response.json();
          console.error("Failed to fetch jobs:", errorData);
          setStatusMessage("Failed to fetch existing job positions.");
          return;
        }
        const data: Job[] = await response.json();
        setExistingJobs(data);
      } catch (error: any) {
        console.error("Error fetching jobs:", error.message);
        setStatusMessage(`An error occurred: ${error.message}`);
      } finally {
        setLoadingJobs(false);
      }
    };

    if (token) {
      fetchJobs();
    } else {
      setStatusMessage("Authentication token is missing.");
    }
  }, [BASE_URL, token]);

  const handlePrepopulateChange = (checked: boolean) => {
    setPrepopulate(checked);
    if (!checked) {
      setSelectedExistingJob("");
      resetForm();
    }
  };

   const handleExistingJobChange = (value: string) => {
    setSelectedExistingJob(value);
    const job = existingJobs.find((j) => j.job_code === value);
    if (job) {
      setJobTitle(job.job_title);
      setJobCode(job.job_code);
      setJobDescription(job.job_description);
      setJobResponsibilities(job.job_responsibilities);
      setJobRequirements(job.job_requirements);
      setJobQualifications(job.job_qualifications);
      setJobExperience(job.job_experience);
      setJobSkills(job.job_skills);
      setJobMinSalary(job.job_min_salary);
      setJobMaxSalary(job.job_max_salary);
      setPositionStatus(job.posission_status);
      setNoOfEmployees(job.no_of_employees);
      setRequiredProfessionalBody(job.required_proffesional_body);
      setAcademicQualification(job.accademic_qualification);
      setProfessionalRequirements(job.professional_requirements);
      setOrganisation(job.organisation);
      setDepartment(job.department);
      setCreatedBy(job.created_by);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoadingSave(true);
    setStatusMessage(null);
    let jobCodeValue = selectedExistingJob;

     if (!(prepopulate && selectedExistingJob)) {
      const jobData = {
        job_title: jobTitle,
        job_code: jobCode,
        job_description: jobDescription,
        job_responsibilities: jobResponsibilities,
        job_requirements: jobRequirements,
        job_qualifications: jobQualifications,
        job_experience: jobExperience,
        job_skills: jobSkills,
        job_min_salary: jobMinSalary,
        job_max_salary: jobMaxSalary,
        posission_status: positionStatus,
        no_of_employees: noOfEmployees,
        required_proffesional_body: requiredProfessionalBody,
        accademic_qualification: academicQualification,
        professional_requirements: professionalRequirements,
        organisation,
        department,
        created_by: createdBy,
      };

      try {
        const jobResponse = await fetch(`${BASE_URL}/hrm/job-positions`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
          body: JSON.stringify(jobData),
        });
        if (!jobResponse.ok) {
          const errorData = await jobResponse.json();
          console.error("Job creation error:", errorData);
          setStatusMessage(
            errorData.message ||
              (errorData.errors &&
                Object.values(errorData.errors).flat().join(" ")) ||
              "Failed to create job position."
          );
          setLoadingSave(false);
          return;
        }
        const createdJob: Job = await jobResponse.json();
        jobCodeValue = createdJob.job_code;
        setStatusMessage("Job position created successfully.");
      } catch (error: any) {
        console.error("Error creating job position:", error.message);
        setStatusMessage(`An error occurred: ${error.message}`);
        setLoadingSave(false);
        return;
      }
    }

    
    const vacancyData: JobVacancy = {
      vacanc_status: "Pending Approval",
      job: jobCodeValue,
      created_by: createdBy,
    };

    try {
      const vacancyResponse = await fetch(`${BASE_URL}/hrm/vacancies`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Token ${token}`,
        },
        body: JSON.stringify(vacancyData),
      });
      if (!vacancyResponse.ok) {
        const errorData = await vacancyResponse.json();
        console.error("Vacancy creation error:", errorData);
        setStatusMessage(
          errorData.message ||
            (errorData.errors &&
              Object.values(errorData.errors).flat().join(" ")) ||
            "Failed to submit job requisition."
        );
      } else {
        await vacancyResponse.json();
        setStatusMessage(
          "Job requisition submitted successfully and is Pending Approval!"
        );
        resetForm();
      }
    } catch (error: any) {
      console.error("Error submitting job requisition:", error.message);
      setStatusMessage(`An error occurred: ${error.message}`);
    } finally {
      setLoadingSave(false);
    }
  };

  // Reset all form fields
  const resetForm = () => {
    setJobTitle("");
    setJobCode("");
    setJobDescription("");
    setJobResponsibilities("");
    setJobRequirements("");
    setJobQualifications("");
    setJobExperience("");
    setJobSkills("");
    setJobMinSalary(0);
    setJobMaxSalary(0);
    setPositionStatus("");
    setNoOfEmployees(1);
    setRequiredProfessionalBody("");
    setAcademicQualification("");
    setProfessionalRequirements("");
    setOrganisation(0);
    setDepartment(0);
    setCreatedBy("");
    setPrepopulate(false);
    setSelectedExistingJob("");
    setConfirmedDetails(false);
  };

  const isSubmitDisabled =
    !confirmedDetails ||
    !jobTitle ||
    !jobCode ||
    !jobDescription ||
    !jobResponsibilities ||
    !jobRequirements ||
    !jobQualifications ||
    !jobExperience ||
    !jobSkills ||
    jobMinSalary <= 0 ||
    jobMaxSalary <= jobMinSalary ||
    noOfEmployees <= 0 ||
    !requiredProfessionalBody ||
    !academicQualification ||
    !professionalRequirements ||
    organisation <= 0 ||
    department <= 0 ||
    !createdBy;

    const breadcrumb = (
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink
                href="/"
                className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
              >
                Optiven HRMS
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
                Job Requisition
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      );
    
      return (
        <Screen headerContent={breadcrumb}>
    <div className="max-w-4xl mx-auto my-8 p-6 bg-white rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-4">Create or Update Job Requisition</h2>
      {statusMessage && (
        <Alert
          variant={
            statusMessage.toLowerCase().includes("default")
              ? "default"
              : "destructive"
          }
          className="mb-4"
        >
          {statusMessage}
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        
        <div className="flex items-center space-x-2">
          <Checkbox
            id="prepopulate"
            checked={prepopulate}
            onCheckedChange={handlePrepopulateChange}
          />
          <Label htmlFor="prepopulate">Prepopulate from existing job</Label>
        </div>

        {prepopulate && (
          <div className="mb-4">
            {loadingJobs ? (
              <div className="flex justify-center">
                <Loader2 className="animate-spin" size={24} />
              </div>
            ) : (
              <div className="space-y-1">
                <Label htmlFor="existing-job">Select Existing Job</Label>
                <Select
                  value={selectedExistingJob}
                  onValueChange={handleExistingJobChange}
                >
                  <SelectTrigger id="existing-job" className="w-full">
                    {selectedExistingJob
                      ? existingJobs.find(
                          (job) => job.job_code === selectedExistingJob
                        )?.job_title
                      : "Select a job"}
                  </SelectTrigger>
                  <SelectContent>
                    {existingJobs.map((job) => (
                      <SelectItem key={job.id} value={job.job_code}>
                        {job.job_title} ({job.job_code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        )}

        <div className="border-b pb-2">
          <h3 className="text-lg font-semibold">Basic Job Information</h3>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-1">
            <Label htmlFor="jobTitle">Job Title</Label>
            <Input
              id="jobTitle"
              value={jobTitle}
              onChange={(e) => setJobTitle(e.target.value)}
              placeholder="Enter job title"
              required
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="jobCode">Job Code</Label>
            <Input
              id="jobCode"
              value={jobCode}
              onChange={(e) => setJobCode(e.target.value)}
              placeholder="Enter job code"
              required
            />
          </div>
        </div>
        <div className="space-y-1">
          <Label htmlFor="jobDescription">Job Description</Label>
          <Textarea
            id="jobDescription"
            value={jobDescription}
            onChange={(e) => setJobDescription(e.target.value)}
            placeholder="Enter job description"
            rows={3}
            required
          />
        </div>

        <div className="border-b pb-2">
          <h3 className="text-lg font-semibold">
            Responsibilities &amp; Requirements
          </h3>
        </div>
        <div className="space-y-1">
          <Label htmlFor="jobResponsibilities">Job Responsibilities</Label>
          <Textarea
            id="jobResponsibilities"
            value={jobResponsibilities}
            onChange={(e) => setJobResponsibilities(e.target.value)}
            placeholder="Enter responsibilities"
            rows={3}
            required
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="jobRequirements">Job Requirements</Label>
          <Textarea
            id="jobRequirements"
            value={jobRequirements}
            onChange={(e) => setJobRequirements(e.target.value)}
            placeholder="Enter requirements"
            rows={3}
            required
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="jobQualifications">Job Qualifications</Label>
          <Textarea
            id="jobQualifications"
            value={jobQualifications}
            onChange={(e) => setJobQualifications(e.target.value)}
            placeholder="Enter qualifications"
            rows={2}
            required
          />
        </div>

        {/* Skills, Experience, & Salary */}
        <div className="border-b pb-2">
          <h3 className="text-lg font-semibold">
            Skills, Experience, &amp; Salary
          </h3>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-1">
            <Label htmlFor="jobExperience">Job Experience</Label>
            <Input
              id="jobExperience"
              value={jobExperience}
              onChange={(e) => setJobExperience(e.target.value)}
              placeholder="Experience required"
              required
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="jobSkills">Job Skills</Label>
            <Input
              id="jobSkills"
              value={jobSkills}
              onChange={(e) => setJobSkills(e.target.value)}
              placeholder="Enter skills"
              required
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="jobMinSalary">Minimum Salary</Label>
            <Input
              id="jobMinSalary"
              type="number"
              value={jobMinSalary || ""}
              onChange={(e) => setJobMinSalary(Number(e.target.value))}
              placeholder="0"
              required
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="jobMaxSalary">Maximum Salary</Label>
            <Input
              id="jobMaxSalary"
              type="number"
              value={jobMaxSalary || ""}
              onChange={(e) => setJobMaxSalary(Number(e.target.value))}
              placeholder="0"
              required
            />
          </div>
        </div>

        <div className="border-b pb-2">
          <h3 className="text-lg font-semibold">Additional Information</h3>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-1">
            <Label htmlFor="positionStatus">Position Status</Label>
            <Select
              value={positionStatus}
              onValueChange={setPositionStatus}
            >
              <SelectTrigger id="positionStatus" className="w-full">
                {positionStatus || "Select status"}
              </SelectTrigger>
              <SelectContent>
                {positionStatuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-1">
            <Label htmlFor="requiredProfessionalBody">
              Required Professional Body
            </Label>
            <Input
              id="requiredProfessionalBody"
              value={requiredProfessionalBody}
              onChange={(e) => setRequiredProfessionalBody(e.target.value)}
              placeholder="Enter professional body"
              required
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="academicQualification">
              Academic Qualification
            </Label>
            <Input
              id="academicQualification"
              value={academicQualification}
              onChange={(e) => setAcademicQualification(e.target.value)}
              placeholder="Enter academic qualification"
              required
            />
          </div>
          <div className="space-y-1 sm:col-span-2">
            <Label htmlFor="professionalRequirements">
              Professional Requirements
            </Label>
            <Textarea
              id="professionalRequirements"
              value={professionalRequirements}
              onChange={(e) => setProfessionalRequirements(e.target.value)}
              placeholder="Enter professional requirements"
              rows={2}
              required
            />
          </div>
          
          <div className="space-y-1">
            <Label htmlFor="organisation">Organisation (ID)</Label>
            <Input
              id="organisation"
              type="number"
              value={organisation || ""}
              onChange={(e) => setOrganisation(Number(e.target.value))}
              placeholder="Enter organisation ID"
              required
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="department">Department</Label>
            <Select
              value={department.toString()}
              onValueChange={(val) => setDepartment(Number(val))}
            >
              <SelectTrigger id="department" className="w-full">
                {department ? departments[department - 1] : "Select department"}
              </SelectTrigger>
              <SelectContent>
                {departments.map((dept, index) => (
                  <SelectItem key={dept} value={(index + 1).toString()}>
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-1">
            <Label htmlFor="createdBy">Created By</Label>
            <Input
              id="createdBy"
              value={createdBy}
              onChange={(e) => setCreatedBy(e.target.value)}
              placeholder="Enter creator's name"
              required
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="confirmDetails"
            checked={confirmedDetails}
            onCheckedChange={(checked) => setConfirmedDetails(checked === true)}
          />
          <Label htmlFor="confirmDetails">
            I confirm that these details are correct
          </Label>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={isSubmitDisabled || loadingSave}
          className="w-full"
        >
          {loadingSave ? (
            <div className="flex items-center justify-center space-x-2">
              <Loader2 className="animate-spin" size={16} />
              <span>Saving...</span>
            </div>
          ) : (
            "Submit Job Requisition"
          )}
        </Button>
      </form>
    </div>
    </Screen>
  );
}
