import React, { useState, useEffect } from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { PerformanceData } from './PerformanceAnalytics';
import { Loader2, AlertCircle } from 'lucide-react'; // For the loading state and error display

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

interface DepartmentPerformanceProps {
  data: PerformanceData[];
}

const DepartmentPerformance: React.FC<DepartmentPerformanceProps> = ({ data }) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  // Dummy data to simulate department-wise performance
  const generateDummyData = () => {
    const dummyData: PerformanceData[] = [];
    const departments = ['Sales', 'HR', 'IT', 'Finance'];
    for (let i = 0; i < 100; i++) {
      const department = departments[i % departments.length];
      dummyData.push({
        department_id: i % 4 + 1, // 4 departments (Sales, HR, IT, Finance)
        employee_id: `EMP${i + 1}`,
        kpi_score: Math.floor(Math.random() * 101), // Random KPI score between 0-100
        appraisal_period: `2025-Q${(i % 4) + 1}`, // Random quarter
      });
    }
    return dummyData;
  };

  // Aggregating data for department-wise performance
  const aggregateDepartmentData = (data: PerformanceData[]) => {
    const departmentData = data.reduce((acc, { department_id, kpi_score }) => {
      if (!acc[department_id]) {
        acc[department_id] = [];
      }
      acc[department_id].push(kpi_score);
      return acc;
    }, {} as Record<number, number[]>);

    const labels = Object.keys(departmentData).map((id) => `Dept ${id}`);
    const datasets = [
      {
        label: 'Department Performance',
        data: labels.map((label) => {
          const scores = departmentData[parseInt(label.replace('Dept ', ''))];
          return scores.reduce((a, b) => a + b, 0) / scores.length; // Average score for each department
        }),
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
    ];

    return { labels, datasets };
  };

  // Simulating data fetching (replace with real API call later)
  useEffect(() => {
    try {
      const dummyData = generateDummyData();
      const chartData = aggregateDepartmentData(dummyData);
      setLoading(false); // Data loaded
    } catch (err) {
      setError('Failed to load department performance data');
      setLoading(false);
    }
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center">
        <div className="spinner-border animate-spin inline-block w-8 h-8 border-4 rounded-full border-t-green-600" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center text-red-500">
        <AlertCircle className="mr-2" />
        <p>{error}</p>
      </div>
    );
  }

  const chartData = aggregateDepartmentData(data);

  return (
    <div className="bg-white shadow-md rounded-lg p-6">
      <h2 className="text-2xl font-semibold text-gray-700 mb-4">Department Performance</h2>
      <div className="mb-6">
        <Bar data={chartData} options={{
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: 'Department KPI Performance',
              font: {
                size: 16,
                weight: 'bold',
              },
            },
            tooltip: {
              callbacks: {
                title: function (tooltipItem: any) {
                  return `Department: ${tooltipItem[0].label}`;
                },
                label: function (tooltipItem: any) {
                  return `Average KPI: ${tooltipItem.raw.toFixed(2)}`;
                },
              },
            },
          },
          scales: {
            x: {
              title: {
                display: true,
                text: 'Departments',
                color: '#4B5563',
                font: { size: 14, weight: 'bold' },
              },
            },
            y: {
              title: {
                display: true,
                text: 'KPI Score',
                color: '#4B5563',
                font: { size: 14, weight: 'bold' },
              },
              min: 0,
              max: 100,
              ticks: {
                stepSize: 20,
              },
            },
          },
        }} />
      </div>
    </div>
  );
};

export default DepartmentPerformance;
