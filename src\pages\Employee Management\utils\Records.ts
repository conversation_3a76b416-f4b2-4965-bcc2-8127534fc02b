import axios from "axios";
import toast from "react-hot-toast";

export async function updateOrCreateRecord<T extends { id?: number; employee_no: string }>(
  url: string,
  token: string,
  data: T
): Promise<T | null> {
  try {
    const headers = { Authorization: `Token ${token}` };

    // 1) Check if record(s) for this employee_no exist
    const getRes = await axios.get(url, {
      headers,
      params: { employee_no: data.employee_no },
    });
    const records = getRes.data;

    if (Array.isArray(records) && records.length > 0) {
      // We have an existing record => PATCH the first one
      const recordId = records[0].id;
      const patchRes = await axios.patch(`${url}/${recordId}`, data, { headers });
      toast.success("Record updated successfully!");
      return patchRes.data;
    } else {
      // No record => POST
      const postRes = await axios.post(url, data, { headers });
      toast.success("Record created successfully!");
      return postRes.data;
    }
  } catch (err) {
    console.error("Error updating/creating record:", err);
    toast.error("Failed to update/create record.");
    return null;
  }
}
