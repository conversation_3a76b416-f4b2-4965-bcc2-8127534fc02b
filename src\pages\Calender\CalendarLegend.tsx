import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";

interface LegendItemProps {
  color: string;
  label: string;
  gradient?: string;
}

const LegendItem: React.FC<LegendItemProps> = ({ color, label, gradient }) => (
  <div className="flex items-center space-x-2 p-1.5">
    <span
      className="w-4 h-4 rounded-sm inline-block"
      style={{
        backgroundColor: color,
        backgroundImage: gradient || 'none'
      }}
    />
    <span className="text-sm">{label}</span>
  </div>
);

const CalendarLegend: React.FC = () => {
  const legendItems = [
    { color: "#fc2a0d", label: "Public Holiday (Non-working)" },
    { color: "#891d1a", label: "Non-working Day" },
    { color: "#6a0dad", label: "Restricted Day (No Leave Requests)" },
    {
      color: "#5d4037",
      label: "Non-working & Restricted Day",
      gradient: "linear-gradient(45deg, #891d1a 50%, #6a0dad 50%)"
    },
    { color: "#042069", label: "Other Special Day" },
    { color: "#3182CE", label: "Normal Working Day" },
  ];

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Calendar Legend</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-1">
          {legendItems.map((item, index) => (
            <LegendItem key={index} color={item.color} label={item.label} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default CalendarLegend;
