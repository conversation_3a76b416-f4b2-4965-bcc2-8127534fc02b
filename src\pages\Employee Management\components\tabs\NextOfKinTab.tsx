import React, { useEffect, useState } from "react";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { EmployeeNextOfKin } from "../../types/EmployeeTypes";
import { TabLayout, FormGrid, FormField } from "../TabLayout";
import { Users, UserPlus, Phone, Heart } from "lucide-react";

import { BASE_URL } from "@/config";

interface NextOfKinTabProps {
  employeeNo: string;
  onSaveSuccess?: () => void;
}

export default function NextOfKinTab({ employeeNo, onSaveSuccess }: NextOfKinTabProps) {
  const token = useSelector((state: RootState) => state.auth.token);
  const [nokData, setNokData] = useState<EmployeeNextOfKin | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingNokIndex, setEditingNokIndex] = useState<number | null>(null);
  const [editingDepIndex, setEditingDepIndex] = useState<number | null>(null);

  // Fetch existing data
  useEffect(() => {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }
    (async function fetchNok() {
      setLoading(true);
      try {
        const headers = { Authorization: `Token ${token}` };
        const params = { employee_no: employeeNo };
        const res = await axios.get(`${BASE_URL}/users/employee-next-of-kin`, { headers, params });
        if (Array.isArray(res.data)) {
          const record = res.data.find((item: EmployeeNextOfKin) => item.employee_no === employeeNo);
          if (record) {
            setNokData(record);
          } else {
            setNokData({
              employee_no: employeeNo,
              next_of_kin: [],
              dependants: [],
            });
          }
        }
      } catch (err) {
        console.error("Error fetching Next of Kin:", err);
        toast.error("Failed to fetch Next of Kin data");
        setNokData({
          employee_no: employeeNo,
          next_of_kin: [],
          dependants: [],
        });
      } finally {
        setLoading(false);
      }
    })();
  }, [employeeNo, token]);

  // Save all changes
  async function handleSave() {
    if (!nokData || !token || !employeeNo) return;
    try {
      // First, check if a record exists
      const headers = { Authorization: `Token ${token}` };
      const params = { employee_no: employeeNo };

      console.log("Fetching existing Next of Kin records for:", employeeNo);
      const getRes = await axios.get(
        `${BASE_URL}/users/employee-next-of-kin`,
        { headers, params }
      );

      let recordId;
      if (Array.isArray(getRes.data) && getRes.data.length > 0) {
        // Find the record for this employee
        const record = getRes.data.find((r: any) => r.employee_no === employeeNo);
        if (record) {
          recordId = record.id;
          console.log("Found existing Next of Kin record to update:", recordId);
        }
      }

      const payload = { ...nokData, employee_no: employeeNo };
      console.log("Next of Kin payload:", payload);

      let res;
      if (recordId) {
        // Update existing record
        res = await axios.patch(
          `${BASE_URL}/users/employee-next-of-kin/${recordId}`,
          payload,
          { headers }
        );
      } else {
        // Create new record
        res = await axios.post(
          `${BASE_URL}/users/employee-next-of-kin`,
          payload,
          { headers }
        );
      }

      console.log("Next of Kin save response:", res.data);
      setNokData(res.data);
      toast.success("Next of Kin data saved successfully");

      // Call the onSaveSuccess callback if provided
      if (onSaveSuccess) {
        onSaveSuccess();
      }
    } catch (err) {
      console.error("Error saving Next of Kin:", err);
      toast.error("Failed to save Next of Kin data");
    }
  }

  // Field updates
  const updateNokField = (index: number, field: string, value: string) => {
    setNokData((prev) => {
      if (!prev) return prev;
      const updated = [...prev.next_of_kin];
      updated[index] = { ...updated[index], [field]: value };
      return { ...prev, next_of_kin: updated };
    });
  };

  const updateDepField = (index: number, field: string, value: string) => {
    setNokData((prev) => {
      if (!prev) return prev;
      const updated = [...prev.dependants];
      updated[index] = { ...updated[index], [field]: value };
      return { ...prev, dependants: updated };
    });
  };

  // Add and delete handlers
  const addNok = () => {
    const currentLen = nokData?.next_of_kin.length ?? 0;
    setNokData((prev) => {
      if (!prev) return prev;
      return { ...prev, next_of_kin: [...prev.next_of_kin, { name: "", relationship: "", contact_info: "" }] };
    });
    setEditingNokIndex(currentLen);
  };

  const deleteNok = (index: number) => {
    setNokData((prev) => {
      if (!prev) return prev;
      const updated = prev.next_of_kin.filter((_, i) => i !== index);
      return { ...prev, next_of_kin: updated };
    });
    if (editingNokIndex === index) setEditingNokIndex(null);
  };

  const addDependant = () => {
    const currentLen = nokData?.dependants.length ?? 0;
    setNokData((prev) => {
      if (!prev) return prev;
      return { ...prev, dependants: [...prev.dependants, { name: "", relationship: "", contact_info: "" }] };
    });
    setEditingDepIndex(currentLen);
  };

  const deleteDependant = (index: number) => {
    setNokData((prev) => {
      if (!prev) return prev;
      const updated = prev.dependants.filter((_, i) => i !== index);
      return { ...prev, dependants: updated };
    });
    if (editingDepIndex === index) setEditingDepIndex(null);
  };

  if (!nokData) return null;

  return (
    <TabLayout
      title="Next of Kin & Family Information"
      description="Manage emergency contacts and Dependants"
      employeeNo={employeeNo}
      loading={loading}
      actions={
        <Button
          onClick={handleSave}
          className="flex items-center gap-2"
        >
          <Heart className="h-4 w-4" />
          Save Information
        </Button>
      }
    >
      {/* Next of Kin Section */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium flex items-center">
            <Users className="mr-2 h-5 w-5 text-primary" />
            Next of Kin
          </h3>
          <Button size="sm" variant="outline" onClick={addNok} className="flex items-center gap-1">
            <UserPlus className="h-4 w-4" /> Add Contact
          </Button>
        </div>

        <div className="space-y-4">
          {nokData.next_of_kin.length === 0 ? (
            <div className="text-center p-6 border border-dashed rounded-lg bg-muted/20">
              <p className="text-muted-foreground">No next of kin contacts added yet</p>
              <Button size="sm" variant="outline" onClick={addNok} className="mt-2">
                Add Next of Kin
              </Button>
            </div>
          ) : (
            nokData.next_of_kin.map((item, idx) => (
              <div
                key={idx}
                className="border rounded-lg overflow-hidden transition-all hover:shadow-sm"
              >
                <div className="bg-accent/30 px-4 py-2 flex justify-between items-center">
                  <h4 className="font-medium">{item.name || 'New Contact'}</h4>
                  <div className="flex space-x-2">
                    {editingNokIndex === idx ? (
                      <>
                        <Button size="sm" variant="ghost" onClick={() => setEditingNokIndex(null)}>
                          Done
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button size="sm" variant="ghost" onClick={() => setEditingNokIndex(idx)}>
                          Edit
                        </Button>
                        <Button size="sm" variant="ghost" className="text-red-500" onClick={() => deleteNok(idx)}>
                          Delete
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {editingNokIndex === idx ? (
                  <div className="p-4 bg-card">
                    <FormGrid columns={3}>
                      <FormField>
                        <Label>Full Name</Label>
                        <Input
                          value={item.name}
                          onChange={(e) => updateNokField(idx, 'name', e.target.value)}
                          placeholder="Enter full name"
                        />
                      </FormField>
                      <FormField>
                        <Label>Relationship</Label>
                        <Input
                          value={item.relationship}
                          onChange={(e) => updateNokField(idx, 'relationship', e.target.value)}
                          placeholder="E.g. Spouse, Parent, Sibling"
                        />
                      </FormField>
                      <FormField>
                        <Label>Contact Information</Label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            value={item.contact_info}
                            onChange={(e) => updateNokField(idx, 'contact_info', e.target.value)}
                            placeholder="Phone number"
                            className="pl-10"
                          />
                        </div>
                      </FormField>
                    </FormGrid>
                  </div>
                ) : (
                  <div className="p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Name</p>
                      <p className="font-medium">{item.name || '-'}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Relationship</p>
                      <p className="font-medium">{item.relationship || '-'}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Contact</p>
                      <p className="font-medium flex items-center">
                        <Phone className="h-3 w-3 mr-1 text-muted-foreground" />
                        {item.contact_info || '-'}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Dependants Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium flex items-center">
            <Users className="mr-2 h-5 w-5 text-primary" />
            Dependants
          </h3>
          <Button size="sm" variant="outline" onClick={addDependant} className="flex items-center gap-1">
            <UserPlus className="h-4 w-4" /> Add Dependant
          </Button>
        </div>

        <div className="space-y-4">
          {nokData.dependants.length === 0 ? (
            <div className="text-center p-6 border border-dashed rounded-lg bg-muted/20">
              <p className="text-muted-foreground">No Dependants added yet</p>
              <Button size="sm" variant="outline" onClick={addDependant} className="mt-2">
                Add Dependant
              </Button>
            </div>
          ) : (
            nokData.dependants.map((item, idx) => (
              <div
                key={idx}
                className="border rounded-lg overflow-hidden transition-all hover:shadow-sm"
              >
                <div className="bg-accent/30 px-4 py-2 flex justify-between items-center">
                  <h4 className="font-medium">{item.name || 'New Dependant'}</h4>
                  <div className="flex space-x-2">
                    {editingDepIndex === idx ? (
                      <>
                        <Button size="sm" variant="ghost" onClick={() => setEditingDepIndex(null)}>
                          Done
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button size="sm" variant="ghost" onClick={() => setEditingDepIndex(idx)}>
                          Edit
                        </Button>
                        <Button size="sm" variant="ghost" className="text-red-500" onClick={() => deleteDependant(idx)}>
                          Delete
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {editingDepIndex === idx ? (
                  <div className="p-4 bg-card">
                    <FormGrid columns={3}>
                      <FormField>
                        <Label>Full Name</Label>
                        <Input
                          value={item.name}
                          onChange={(e) => updateDepField(idx, 'name', e.target.value)}
                          placeholder="Enter full name"
                        />
                      </FormField>
                      <FormField>
                        <Label>Relationship</Label>
                        <Input
                          value={item.relationship}
                          onChange={(e) => updateDepField(idx, 'relationship', e.target.value)}
                          placeholder="E.g. Child, Spouse"
                        />
                      </FormField>
                      <FormField>
                        <Label>Contact Information</Label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            value={item.contact_info}
                            onChange={(e) => updateDepField(idx, 'contact_info', e.target.value)}
                            placeholder="Phone number (if applicable)"
                            className="pl-10"
                          />
                        </div>
                      </FormField>
                    </FormGrid>
                  </div>
                ) : (
                  <div className="p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Name</p>
                      <p className="font-medium">{item.name || '-'}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Relationship</p>
                      <p className="font-medium">{item.relationship || '-'}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Contact</p>
                      <p className="font-medium flex items-center">
                        <Phone className="h-3 w-3 mr-1 text-muted-foreground" />
                        {item.contact_info || '-'}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </TabLayout>
  );
}