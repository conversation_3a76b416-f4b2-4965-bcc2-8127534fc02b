"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

import { JobVacancy } from "../types";
import { BASE_URL } from "@/config";

interface VacancyDetailsDialogProps {
  vacancyId: number;
  open: boolean;
  onClose: () => void;
}

const VacancyDetailsDialog: React.FC<VacancyDetailsDialogProps> = ({
  vacancyId,
  open,
  onClose,
}) => {
  const [vacancy, setVacancy] = useState<JobVacancy | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open) {
      const fetchVacancyDetails = async () => {
        setLoading(true);
        setError(null);
        try {
          const res = await fetch(`${BASE_URL}/hrm/vacancies/${vacancyId}`, {
            headers: { "Content-Type": "application/json" },
          });
          if (!res.ok) {
            throw new Error("Failed to fetch vacancy details");
          }
          const data: JobVacancy = await res.json();
          setVacancy(data);
        } catch (err: any) {
          setError(err.message || "Error fetching vacancy details");
        } finally {
          setLoading(false);
        }
      };
      fetchVacancyDetails();
    }
  }, [open, vacancyId]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-lg max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Vacancy Details</DialogTitle>
        </DialogHeader>
        {loading ? (
          <p className="text-center">Loading vacancy details...</p>
        ) : error ? (
          <p className="text-center text-red-500">{error}</p>
        ) : vacancy ? (
          <div className="space-y-4">
            <p>
              <strong>Job Title:</strong> {vacancy.job_details.job_title}
            </p>
            <p>
              <strong>Job Code:</strong> {vacancy.job_details.job_code}
            </p>
            <p>
              <strong>Description:</strong> {vacancy.job_details.job_description}
            </p>
            <p>
              <strong>Responsibilities:</strong>{" "}
              {vacancy.job_details.job_responsibilities}
            </p>
            <p>
              <strong>Requirements:</strong> {vacancy.job_details.job_requirements}
            </p>
            <p>
              <strong>Qualifications:</strong>{" "}
              {vacancy.job_details.job_qualifications}
            </p>
            <p>
              <strong>Start Date:</strong> {vacancy.vacancy_start_date || "N/A"}
            </p>
            <p>
              <strong>Status:</strong>{" "}
              <Badge variant="outline">{vacancy.vacanc_status}</Badge>
            </p>
          </div>
        ) : (
          <p>No vacancy details available.</p>
        )}
        <DialogFooter className="mt-4 flex justify-end space-x-2">
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default VacancyDetailsDialog;
