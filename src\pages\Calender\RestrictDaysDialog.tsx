import React, { useState } from "react";
import dayjs from "dayjs";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";

interface RestrictDaysDialogProps {
  open: boolean;
  onClose: () => void;
  onApply: (start: string, end: string, isNonWorking: boolean, isRestricted: boolean) => Promise<void>;
}

const RestrictDaysDialog: React.FC<RestrictDaysDialogProps> = ({
  open,
  onClose,
  onApply,
}) => {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [isNonWorking, setIsNonWorking] = useState(false);
  const [isRestricted, setIsRestricted] = useState(true);
  const [error, setError] = useState("");

  const handleApply = async () => {
    setError("");
    if (!startDate || !endDate) {
      setError("Please specify both Start and End dates.");
      return;
    }
    if (dayjs(startDate).isAfter(dayjs(endDate))) {
      setError("Start date cannot be after End date.");
      return;
    }

    if (!isNonWorking && !isRestricted) {
      setError("Please select at least one option (Non-working or Restricted).");
      return;
    }

    try {
      await onApply(startDate, endDate, isNonWorking, isRestricted);
      onClose();
    } catch (err) {
      console.error("Error applying special days:", err);
      setError("Failed to apply special days.");
    }
  };

  return (
    <Dialog open={open} onOpenChange={(o) => !o && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Manage Special Days</DialogTitle>
          <DialogDescription>
            Mark a range of days as restricted or non-working days in the calendar.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Card className="border-0 shadow-none">
          <CardContent className="p-0">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start-date">Start Date</Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="focus:ring-2 focus:ring-primary"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-date">End Date</Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>

              <div className="pt-2 space-y-3">
                <Label className="text-sm font-medium mb-2 block">Day Type</Label>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="restricted"
                    checked={isRestricted}
                    onCheckedChange={(checked) => setIsRestricted(checked as boolean)}
                  />
                  <Label
                    htmlFor="restricted"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Restricted Days (employees cannot request leave)
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="non-working"
                    checked={isNonWorking}
                    onCheckedChange={(checked) => setIsNonWorking(checked as boolean)}
                  />
                  <Label
                    htmlFor="non-working"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Non-working Days (holidays, weekends, etc.)
                  </Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="default" onClick={handleApply}>
            Apply
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RestrictDaysDialog;
