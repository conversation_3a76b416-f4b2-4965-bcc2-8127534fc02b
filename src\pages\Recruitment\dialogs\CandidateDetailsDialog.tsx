"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Footer, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

import {
  VacancyApplication,
  ExternalUserBioData,
  UserAcademic,
  ProfessionalCertification,
  ApplicantExperience,
  ApplicantReferee,
} from "../types";
import { BASE_URL } from "@/config";

interface CandidateDetailsDialogProps {
  open: boolean;
  application: VacancyApplication | null;
  onClose: () => void;
  onStatusChange?: (status: string) => void;
  onRate?: () => void;
}

const CandidateDetailsDialog: React.FC<CandidateDetailsDialogProps> = ({
  open,
  application,
  onClose,
  onStatusChange,
  onRate,
}) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [bioData, setBioData] = useState<ExternalUserBioData | null>(null);
  const [academics, setAcademics] = useState<UserAcademic[]>([]);
  const [certifications, setCertifications] = useState<ProfessionalCertification[]>([]);
  const [experiences, setExperiences] = useState<ApplicantExperience[]>([]);
  const [referees, setReferees] = useState<ApplicantReferee[]>([]);

  useEffect(() => {
    if (open && application) {
      const fetchCandidateData = async () => {
        try {
          setLoading(true);
          setError(null);
          const extNo = application.external_user_no;
          const [bioRes, academicRes, certRes, expRes, refRes] = await Promise.all([
            fetch(`${BASE_URL}/recruitment/external-user-bio-data?external_user_no=${extNo}`, {
              headers: { "Content-Type": "application/json" },
            }),
            fetch(`${BASE_URL}/recruitment/user-academics?external_user_no=${extNo}`, {
              headers: { "Content-Type": "application/json" },
            }),
            fetch(`${BASE_URL}/recruitment/professional-certification?external_user_no=${extNo}`, {
              headers: { "Content-Type": "application/json" },
            }),
            fetch(`${BASE_URL}/recruitment/applicant-experience?external_user_no=${extNo}`, {
              headers: { "Content-Type": "application/json" },
            }),
            fetch(`${BASE_URL}/recruitment/applicant-referees?external_user_no=${extNo}`, {
              headers: { "Content-Type": "application/json" },
            }),
          ]);
          if (!bioRes.ok) throw new Error("Failed to fetch user bio data");
          if (!academicRes.ok) throw new Error("Failed to fetch academics");
          if (!certRes.ok) throw new Error("Failed to fetch certifications");
          if (!expRes.ok) throw new Error("Failed to fetch experiences");
          if (!refRes.ok) throw new Error("Failed to fetch referees");

          const bioDataJson: ExternalUserBioData[] = await bioRes.json();
          const filteredBio = bioDataJson.find((bio) => bio.external_user_no === extNo);
          setBioData(filteredBio || null);

          const academicJson: UserAcademic[] = await academicRes.json();
          setAcademics(academicJson.filter((item) => item.external_user_no === extNo));

          const certJson: ProfessionalCertification[] = await certRes.json();
          setCertifications(certJson.filter((item) => item.external_user_no === extNo));

          const expJson: ApplicantExperience[] = await expRes.json();
          setExperiences(expJson.filter((item) => item.external_user_no === extNo));

          const refJson: ApplicantReferee[] = await refRes.json();
          setReferees(refJson.filter((item) => item.external_user_no === extNo));
        } catch (err: any) {
          setError(err.message);
        } finally {
          setLoading(false);
        }
      };
      fetchCandidateData();
    }
  }, [open, application, token]);

  if (!application) return null;

  const candidateFullName = bioData
    ? `${bioData.first_name} ${bioData.middle_name ?? ""} ${bioData.last_name}`.trim()
    : application.external_user_no;

  const handleApprove = () => {
    if (onStatusChange) onStatusChange("shortlisted");
  };
  const handleReject = () => {
    if (onStatusChange) onStatusChange("Rejected");
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Candidate Details</DialogTitle>
        </DialogHeader>
        {loading ? (
          <p className="text-center">Loading candidate details...</p>
        ) : error ? (
          <p className="text-center text-red-500">{error}</p>
        ) : (
          <>
            <div className="space-y-2">
              <h2 className="text-lg font-semibold">Basic Info</h2>
              <p>
                <strong>Name:</strong> {candidateFullName}
              </p>
              <p>
                <strong>Email:</strong> {bioData?.email ?? "N/A"}
              </p>
              <p>
                <strong>Gender:</strong> {bioData?.gender ?? "N/A"}
              </p>
              <p>
                <strong>Date of Birth:</strong> {bioData?.date_of_birth ?? "N/A"}
              </p>
              <p>
                <strong>Phone:</strong> {bioData?.home_phone_number ?? "N/A"}
              </p>
            </div>
            <div className="mt-4 space-y-2">
              <h2 className="text-lg font-semibold">Academics</h2>
              {academics.length === 0 ? (
                <p>No academic records found.</p>
              ) : (
                academics.map((ac) => (
                  <div key={ac.id} className="border p-2 rounded mb-2">
                    <p>
                      <strong>Institution:</strong> {ac.institution}
                    </p>
                    <p>
                      <strong>Course:</strong> {ac.course_name}
                    </p>
                    <p>
                      <strong>Dates:</strong> {ac.start_date} - {ac.end_date}
                    </p>
                  </div>
                ))
              )}
            </div>
            <div className="mt-4 space-y-2">
              <h2 className="text-lg font-semibold">Professional Certifications</h2>
              {certifications.length === 0 ? (
                <p>No certifications found.</p>
              ) : (
                certifications.map((cert) => (
                  <div key={cert.id} className="border p-2 rounded mb-2">
                    <p>
                      <strong>Name:</strong> {cert.certification_name}
                    </p>
                    <p>
                      <strong>Body:</strong> {cert.certification_body}
                    </p>
                    <p>
                      <strong>Dates:</strong> {cert.start_date} - {cert.end_date}
                    </p>
                  </div>
                ))
              )}
            </div>
            <div className="mt-4 space-y-2">
              <h2 className="text-lg font-semibold">Work Experience</h2>
              {experiences.length === 0 ? (
                <p>No experience records found.</p>
              ) : (
                experiences.map((exp) => (
                  <div key={exp.id} className="border p-2 rounded mb-2">
                    <p>
                      <strong>Company:</strong> {exp.company_name}
                    </p>
                    <p>
                      <strong>Responsibilities:</strong> {exp.responsibilities || "N/A"}
                    </p>
                    <p>
                      <strong>Dates:</strong> {exp.start_date} - {exp.end_date}
                    </p>
                  </div>
                ))
              )}
            </div>
            <div className="mt-4 space-y-2">
              <h2 className="text-lg font-semibold">Referees</h2>
              {referees.length === 0 ? (
                <p>No referees found.</p>
              ) : (
                referees.map((ref) => (
                  <div key={ref.id} className="border p-2 rounded mb-2">
                    <p>
                      <strong>Name:</strong> {ref.name}
                    </p>
                    <p>
                      <strong>Email:</strong> {ref.personal_email || "N/A"}
                    </p>
                    <p>
                      <strong>Contact:</strong> {ref.contact_number || "N/A"}
                    </p>
                    <p>
                      <strong>Designation:</strong> {ref.designation}
                    </p>
                  </div>
                ))
              )}
            </div>
          </>
        )}
        <DialogFooter className="mt-4 flex justify-end space-x-2">
          {onStatusChange && (
            <>
              <Button variant="default" onClick={handleApprove} disabled={loading}>
                Approve
              </Button>
              <Button variant="destructive" onClick={handleReject} disabled={loading}>
                Reject
              </Button>
            </>
          )}
          {onRate && (
            <Button onClick={onRate} disabled={loading}>
              Rate
            </Button>
          )}
          <Button onClick={onClose} variant="outline" disabled={loading}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CandidateDetailsDialog;
