import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  X,
  Calendar,
  Building2,
  Users,
  Target
} from 'lucide-react';

interface Department {
  id: number;
  name: string;
  title?: string;
}

interface Team {
  id: number;
  name: string;
  description: string;
  group_head: string;
  group_assistant: string;
  group_hr: string;
  group_status_active: boolean;
  organisation: number;
  parent_group: number | null;
}

interface SharedFiltersProps {
  filters: Record<string, any>;
  onFilterChange: (key: string, value: string | number) => void;
  onClearFilters: () => void;
  departments?: Department[];
  teams?: Team[];
  showEmployeeFilter?: boolean;
  showDepartmentFilter?: boolean;
  showTeamFilter?: boolean;
  showDateFilters?: boolean;
  showStatusFilter?: boolean;
  showPerformanceFilters?: boolean;
  className?: string;
}

const SharedFilters: React.FC<SharedFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
  departments = [],
  teams = [],
  showEmployeeFilter = false,
  showDepartmentFilter = true,
  showTeamFilter = false,
  showDateFilters = true,
  showStatusFilter = false,
  showPerformanceFilters = false,
  className = ""
}) => {
  const activeFiltersCount = Object.keys(filters).filter(key => filters[key] && filters[key] !== 'all').length;

  return (
    <Card className={`h-fit ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="h-8 px-2 text-muted-foreground"
            disabled={activeFiltersCount === 0}
          >
            <X size={16} className="mr-1" />
            Clear all
          </Button>
        </div>
        <CardDescription>
          Filters apply automatically as you type
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Active filters display */}
        {activeFiltersCount > 0 && (
          <div className="space-y-2">
            <label className="text-xs font-medium text-muted-foreground">Active Filters</label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(filters)
                .filter(([_, v]) => v && v !== 'all')
                .map(([k, v]) => (
                  <Badge
                    key={k}
                    variant="secondary"
                    className="cursor-pointer transition-all hover:opacity-80"
                    onClick={() => onFilterChange(k, '')}
                  >
                    {k.replace(/_/g, ' ')}: {v}
                    <X size={12} className="ml-1" />
                  </Badge>
                ))}
            </div>
          </div>
        )}

        {/* Search */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-muted-foreground">Search</label>
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search employees, departments, teams..."
              value={filters.search || ''}
              onChange={(e) => onFilterChange('search', e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Employee Number Filter */}
        {showEmployeeFilter && (
          <div className="space-y-2">
            <label className="text-xs font-medium text-muted-foreground">Employee Number</label>
            <Input
              placeholder="Enter employee number"
              value={filters.employee_no || ''}
              onChange={(e) => onFilterChange('employee_no', e.target.value)}
            />
          </div>
        )}

        {/* Department Filter */}
        {showDepartmentFilter && departments.length > 0 && (
          <div className="space-y-2">
            <label className="text-xs font-medium text-muted-foreground">Department</label>
            <Select
              value={filters.department || 'all'}
              onValueChange={(value) => onFilterChange('department', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                {departments.map((dept) => (
                  <SelectItem key={dept.id} value={dept.id.toString()}>
                    <div className="flex items-center">
                      <Building2 className="h-4 w-4 mr-2" />
                      {dept.name || dept.title}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Team Filter */}
        {showTeamFilter && teams.length > 0 && (
          <div className="space-y-2">
            <label className="text-xs font-medium text-muted-foreground">Team</label>
            <Select
              value={filters.team || 'all'}
              onValueChange={(value) => onFilterChange('team', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select team" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Teams</SelectItem>
                {teams.filter(team => team.group_status_active).map((team) => (
                  <SelectItem key={team.id} value={team.id.toString()}>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-2" />
                      {team.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Date Range Filters */}
        {showDateFilters && (
          <>
            <div className="space-y-2">
              <label className="text-xs font-medium text-muted-foreground">Date From</label>
              <div className="relative">
                <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  type="date"
                  value={filters.date_from || ''}
                  onChange={(e) => onFilterChange('date_from', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-xs font-medium text-muted-foreground">Date To</label>
              <div className="relative">
                <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  type="date"
                  value={filters.date_to || ''}
                  onChange={(e) => onFilterChange('date_to', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </>
        )}

        {/* Status Filter */}
        {showStatusFilter && (
          <div className="space-y-2">
            <label className="text-xs font-medium text-muted-foreground">Status</label>
            <Select
              value={filters.status || 'all'}
              onValueChange={(value) => onFilterChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="Open">Open Appraisals</SelectItem>
                <SelectItem value="Self Appraised">Self Appraised</SelectItem>
                <SelectItem value="Employee Accepted">Employee Accepted</SelectItem>
                <SelectItem value="Supervisor Accepted">Supervisor Accepted</SelectItem>
                <SelectItem value="In Review">In Supervisor Review</SelectItem>
                <SelectItem value="Supervisor Appraised">Supervisor Appraised</SelectItem>
                <SelectItem value="HR Appraised">HR Appraised</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Performance Filters */}
        {showPerformanceFilters && (
          <>
            <div className="space-y-2">
              <label className="text-xs font-medium text-muted-foreground">Min Completion Rate (%)</label>
              <div className="relative">
                <Target className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  type="number"
                  min="0"
                  max="100"
                  placeholder="e.g., 80"
                  value={filters.min_completion_rate || ''}
                  onChange={(e) => onFilterChange('min_completion_rate', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-xs font-medium text-muted-foreground">Performance Threshold</label>
              <Select
                value={filters.performance_threshold || 'all'}
                onValueChange={(value) => onFilterChange('performance_threshold', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select threshold" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Performance Levels</SelectItem>
                  <SelectItem value="90">Excellent (90%+)</SelectItem>
                  <SelectItem value="70">Good (70%+)</SelectItem>
                  <SelectItem value="50">Average (50%+)</SelectItem>
                  <SelectItem value="0">All Levels</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default SharedFilters;
