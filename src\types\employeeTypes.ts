// types/employeeTypes.ts

export interface EmployeeListItem {
    id: number;
    employee_no: string;
    first_name: string;
    last_name: string;
  }

  export interface EmployeeBioDetails {
    id?: number;
    employee_no: string;
    first_name: string;
    middle_name: string;
    last_name: string;
    gender?: string;
    marital_status?: string;
    date_of_birth?: string;
    passport_number?: string;
    id_number?: string;
    date_of_first_appointment?: string;
    created_at?: string;
    updated_at?: string;
  }

  export interface EmployeeContactInfo {
    id?: number;
    employee_no: string;
    postal_address?: string;
    home_phone_number?: string;
    residential_address?: string;
    city?: string;
    county?: string;
    company_email?: string;
    country?: string;
    personal_email?: string;
    next_of_kin?: string;
    next_of_kin_contact?: string;
  }

  export interface EmployeeContractInfo {
    id?: number;
    employee_no: string;
    contract_type: string;
    contract_start_date: string;
    current_contract_end?: string;
    end_of_probation_date?: string;
  }

  export interface EmployeeImportantDates {
    id?: number;
    employee_no: string;
    date_of_current_appointment: string;
    date_of_leaveing?: string | null;
  }

  /** Job Info */
  export interface JobInfo {
    id?: number;
    employee_no: string;
    category?: string;
    directorate?: string;
    job_title_code?: string;
    job_title?: string;
    teams_code?: string;
    department?: number;
    work_location?: string;     // New Field
    business_unit?: string;     // New Field
  }

  export interface EmployeePaymentInfo {
    NSSF_number: string;
    id?: number;
    employee_no: string;
    pension_scheme_join: string;
    salary: number;
    bonus: number;
    bank_name?: string;             // New Field
    account_number?: string;        // New Field
    payment_frequency?: string;     // New Field
    KRA_pin?: string;               // New Field
    NHIF_SHIF_number?: string;      // New Field
    HELB_number?: string;           // New Field
    tax_status?: boolean;           // New Field
  }

  export interface EmployeePermissions {
    id?: number;
    employee_no?: string;
    permission_name: string;
    permission_description?: string;
  }

  export interface EmployeeRole {
    id?: number;
    employee_no: string;
    role: number;
  }

  export interface WarningRecord {
    id: number;
    employee_no: string;
    date: string;
    reason: string;
    acknowledged: boolean;
  }

  export interface EmployeeGroups {
    id?: number;
    employee_no?: string;
    name: string;
  }


  export interface EmployeeEducationalQualification {
    id?: number;
    employee_no: string;
    highear_qualificatins?: string; // Note: Typo as per API ('highear_qualificatins' instead of 'higher_qualifications')
    instution: string;              // Note: Typo as per API ('instution' instead of 'institution')
    year_of_graduation: string;
    created_at?: string;
    updated_at?: string;
  }


  export interface NextOfKin {
    id?: number;
    name: string;
    relationship: string;
    contact_info: string;
  }


  export interface Dependant {
    id?: number;
    name: string;
    relationship: string;
    contact_info: string;
  }


  export interface EmployeeNextOfKin {
    id?: number;
    employee_no: string;
    next_of_kin: NextOfKin[];
    dependants: Dependant[];
  }
