"use client";
import * as React from "react";
import { AppSidebar } from "@/app-components/sidebar/app-sidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { useTheme } from "@/hooks/use-theme";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/redux/store";
import { logout } from "@/redux/features/auth/authSlice";
import { useNavigate, useLocation } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Moon, Sun, LogOut, Calendar, Bell } from "lucide-react";

interface ScreenProps {
  children: React.ReactNode;
  headerContent?: React.ReactNode;
}

export function Screen({ children, headerContent }: ScreenProps) {
  const { toggleTheme, theme } = useTheme();

  const { user } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    dispatch(logout());
    navigate("/login");
  };

  // If user is missing, fallback
  const displayName = user?.UserBio?.first_name
    ? `${user.UserBio.first_name} ${user.UserBio.last_name ?? ""}`
    : user?.UserContacts?.company_email ?? "Unknown User";

  return (
    <SidebarProvider>
      <AppSidebar />

      <SidebarInset className="flex flex-col flex-1 w-full overflow-hidden">
        <header
          className="
            flex h-16 shrink-0 items-center justify-between
            px-4 bg-white text-black shadow-md
            dark:bg-gray-900 dark:text-gray-100
            transition-all border-b
          "
        >
          <div className="flex items-center space-x-2">
            <SidebarTrigger className="dark:bg-gray-700 dark:text-white" />
            <Separator orientation="vertical" className="h-6" />

            <div>{headerContent}</div>
          </div>

          <div className="flex items-center space-x-6">
            {/* NOTIFICATION BELL */}
            <button
              className="relative p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              aria-label="Notifications"
            >
              <Bell className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              {/* Optional notification indicator */}
              <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>

            {/* CALENDAR */}
            <button
              onClick={() => navigate("/calender-management")}
              className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              aria-label="Calendar"
            >
              <Calendar className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
            {/* THEME TOGGLE */}
            <div className="flex items-center space-x-2">
              {/* Sun Icon */}
              <Sun className="w-5 h-5 text-yellow-500" />
              <button
                onClick={toggleTheme}
                className="
                  relative inline-flex items-center h-6 w-12
                  rounded-full bg-gray-200 dark:bg-gray-700
                  transition-colors focus:outline-none focus:ring-2 focus:ring-green-500
                "
                aria-label="Toggle theme"
              >
                <span
                  className={`${
                    theme === "dark" ? "translate-x-4" : "-translate-x-4"
                  } inline-block w-4 h-4 transform bg-white rounded-full shadow transition-transform`}
                />
              </button>
              <Moon className="w-5 h-5 text-gray-500 dark:text-gray-300" />
            </div>

            {/* USER AVATAR DROPDOWN */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button
                  className="
                    flex items-center gap-2
                    rounded-full p-0
                    hover:bg-gray-200 dark:hover:bg-gray-800
                    transition-colors
                  "
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage
                      src={"/avatars/shadcn.jpg"}
                      alt={displayName}
                    />
                    <AvatarFallback className="rounded-full bg-green-600 text-white">
                      {displayName[0]?.toUpperCase() ?? "U"}
                    </AvatarFallback>
                  </Avatar>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="
                  w-48 rounded-lg bg-white text-black border
                  dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700
                "
                align="end"
                sideOffset={6}
              >
                <DropdownMenuLabel className="p-2 text-sm">
                  {displayName}
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="dark:bg-gray-700" />
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="flex items-center gap-2 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                >
                  <LogOut className="w-4 h-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

         <main
          key={location.pathname} // Force re-render when route changes
          className="
            flex-1 overflow-auto
            bg-white text-black dark:bg-gray-900 dark:text-gray-100
          "
        >
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
