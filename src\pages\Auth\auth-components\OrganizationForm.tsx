// src/components/auth-components/OrganizationForm.tsx
"use client";

import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import { createOrganization } from "@/redux/features/setup/setupSlice";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface OrganizationFormProps {
  onSuccess: () => void;
}

const OrganizationForm: React.FC<OrganizationFormProps> = ({ onSuccess }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { orgLoading, orgError, organization } = useSelector((state: RootState) => state.setup);
  const { toast } = useToast();

  // Form state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [logo, setLogo] = useState<File | null>(null);
  const [primaryColor, setPrimaryColor] = useState("#000000");
  const [secondaryColor, setSecondaryColor] = useState("#000000");
  const [tertiaryColor, setTertiaryColor] = useState("#000000");
  const [fourthColor, setFourthColor] = useState("#000000");
  const [fifthColor, setFifthColor] = useState("#000000");
  const [contact, setContact] = useState("");
  const [address, setAddress] = useState("");
  const [domain, setDomain] = useState("");

  // AlertDialog state
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertTitle, setAlertTitle] = useState("");
  const [alertDescription, setAlertDescription] = useState("");

  // Show error alert if needed.
  useEffect(() => {
    if (orgError) {
      setAlertTitle("Error");
      setAlertDescription(orgError);
      setAlertOpen(true);
    }
  }, [orgError]);

  // On success, show success alert.
  useEffect(() => {
    if (organization) {
      setAlertTitle("Organization Created");
      setAlertDescription(`ID: ${organization.id}, Name: ${organization.name}`);
      setAlertOpen(true);
    }
  }, [organization]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!logo) {
      setAlertTitle("Error");
      setAlertDescription("Please select a logo file.");
      setAlertOpen(true);
      return;
    }
    dispatch(
      createOrganization({
        name,
        description,
        logo,
        primary_color: primaryColor,
        secondary_color: secondaryColor,
        tertiary_color: tertiaryColor,
        fourth_color: fourthColor,
        fifth_color: fifthColor,
        contact,
        address,
        domain,
      })
    );
  };

  const handleAlertClose = () => {
    setAlertOpen(false);
    if (organization) {
      // On success, notify parent to move to next step.
      onSuccess();
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Create Organization</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="orgName">Organization Name</Label>
          <Input
            id="orgName"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter organization name"
            required
          />
        </div>
        <div>
          <Label htmlFor="orgDescription">Description</Label>
          <Input
            id="orgDescription"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter description"
            required
          />
        </div>
        <div>
          <Label htmlFor="orgLogo">Logo File</Label>
          <Input
            id="orgLogo"
            type="file"
            accept="image/*"
            onChange={(e) => {
              if (e.target.files && e.target.files.length > 0) {
                setLogo(e.target.files[0]);
              }
            }}
            required
          />
        </div>
        <div>
          <Label htmlFor="primaryColor">Primary Color</Label>
          <Input id="primaryColor" type="color" value={primaryColor} onChange={(e) => setPrimaryColor(e.target.value)} required />
        </div>
        <div>
          <Label htmlFor="secondaryColor">Secondary Color</Label>
          <Input id="secondaryColor" type="color" value={secondaryColor} onChange={(e) => setSecondaryColor(e.target.value)} required />
        </div>
        <div>
          <Label htmlFor="tertiaryColor">Tertiary Color</Label>
          <Input id="tertiaryColor" type="color" value={tertiaryColor} onChange={(e) => setTertiaryColor(e.target.value)} required />
        </div>
        <div>
          <Label htmlFor="fourthColor">Fourth Color</Label>
          <Input id="fourthColor" type="color" value={fourthColor} onChange={(e) => setFourthColor(e.target.value)} required />
        </div>
        <div>
          <Label htmlFor="fifthColor">Fifth Color</Label>
          <Input id="fifthColor" type="color" value={fifthColor} onChange={(e) => setFifthColor(e.target.value)} required />
        </div>
        <div>
          <Label htmlFor="orgContact">Contact</Label>
          <Input id="orgContact" value={contact} onChange={(e) => setContact(e.target.value)} placeholder="Enter contact information" required />
        </div>
        <div>
          <Label htmlFor="orgAddress">Address</Label>
          <Input id="orgAddress" value={address} onChange={(e) => setAddress(e.target.value)} placeholder="Enter address" required />
        </div>
        <div>
          <Label htmlFor="orgDomain">Domain</Label>
          <Input id="orgDomain" value={domain} onChange={(e) => setDomain(e.target.value)} placeholder="Enter domain" required />
        </div>
        <Button type="submit" disabled={orgLoading}>
          {orgLoading ? "Creating..." : "Create Organization"}
        </Button>
      </form>
      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{alertTitle}</AlertDialogTitle>
            <AlertDialogDescription>{alertDescription}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleAlertClose}>OK</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default OrganizationForm;
