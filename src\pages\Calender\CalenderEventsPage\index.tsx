import React, { useEffect, useState, useMemo } from "react";
import axios from "axios";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { BASE_URL } from "../../../config/index";
import CalendarEventForm, { OrganisationCalendar } from "../CalenderEventForm";
import RestrictDaysDialog from "../RestrictDaysDialog";

import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { Plus, Edit, Trash } from "lucide-react";
import { Screen } from "@/app-components/layout/screen";

import { Calendar, dayjsLocalizer } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import "../calendar-styles.css";
import dayjs from "dayjs";

import CalendarLegend from "../CalendarLegend";

export interface ExtendedOrganisationCalendar extends OrganisationCalendar {
  id?: number;
}

interface CalendarEvent {
  title: string;
  start: Date;
  end: Date;
  allDay?: boolean;
  type?: string;
}

const localizer = dayjsLocalizer(dayjs);

const CalendarEventsPage: React.FC = () => {
  const token = useSelector((state: RootState) => state.auth.token);

  const [events, setEvents] = useState<ExtendedOrganisationCalendar[]>([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingEvent, setEditingEvent] = useState<ExtendedOrganisationCalendar | null>(null);

  // For restricting days
  const [restrictDialogOpen, setRestrictDialogOpen] = useState(false);

  // --- FETCH ORGANIZATION CALENDARS ---
  const fetchEvents = async () => {
    if (!token) return;
    setLoading(true);
    setErrorMessage("");
    setSuccessMessage("");
    try {
      const response = await axios.get<ExtendedOrganisationCalendar[]>(
        `${BASE_URL}/users/organization-calendar`,
        { headers: { Authorization: `Token ${token}` } }
      );
      setEvents(response.data);
    } catch (err: unknown) {
      if (axios.isAxiosError(err)) {
        setErrorMessage(err.response?.data?.message || "Failed to fetch calendar events.");
      } else {
        setErrorMessage("Failed to fetch calendar events.");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchEvents();
    }
  }, [token]);

  // CREATE/EDIT/DELETE HANDLERS
  const handleOpenCreate = () => {
    setEditingEvent(null);
    setDialogOpen(true);
  };

  const handleOpenEdit = (ev: ExtendedOrganisationCalendar) => {
    setEditingEvent(ev);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingEvent(null);
  };

  const handleSaveEvent = async (data: OrganisationCalendar) => {
    if (
      !data.calendar_name ||
      !data.calendar_description ||
      !data.calendar_start_date ||
      !data.calendar_end_date ||
      !data.organisation
    ) {
      setErrorMessage("All fields (name, description, start/end dates, organisation) are required.");
      return;
    }

    // Transform special_days for the API
    const transformedSpecialDays = (data.special_days || []).map((day) => ({
      date: day.date,
      title: day.title,
      day: dayjs(day.date).format("dddd"),
      special_day: !day.non_working_day,
      non_working_day: day.non_working_day,
      is_public_holiday: day.is_public_holiday,
      leave_restricted_day: day.leave_restricted_day,
      Reason: day.reason || "",
    }));

    const payload = {
      ...data,
      special_days: transformedSpecialDays,
    };

    try {
      if (editingEvent && editingEvent.id) {
        // Patch existing calendar
        await axios.patch(
          `${BASE_URL}/users/organization-calendar/${editingEvent.id}`,
          payload,
          { headers: { Authorization: `Token ${token}` } }
        );
        setSuccessMessage("Calendar updated successfully!");
      } else {
        // Create new calendar
        await axios.post(`${BASE_URL}/users/organization-calendar`, payload, {
          headers: { Authorization: `Token ${token}` },
        });
        setSuccessMessage("Calendar created successfully!");
      }
      setDialogOpen(false);
      fetchEvents();
    } catch (err: unknown) {
      if (axios.isAxiosError(err)) {
        setErrorMessage(err.response?.data?.message || "Operation failed.");
      } else {
        setErrorMessage("Operation failed.");
      }
    }
  };

  const handleDelete = async (calendarId: number) => {
    if (!token) return;
    try {
      await axios.delete(`${BASE_URL}/users/organization-calendar/${calendarId}`, {
        headers: { Authorization: `Token ${token}` },
      });
      setSuccessMessage("Calendar deleted successfully!");
      fetchEvents();
    } catch (err: unknown) {
      if (axios.isAxiosError(err)) {
        setErrorMessage(err.response?.data?.message || "Deletion failed.");
      } else {
        setErrorMessage("Deletion failed.");
      }
    }
  };

  // Bulk apply special days with checkboxes for non-working and restricted
  const handleApplyRestrict = async (
    start: string,
    end: string,
    isNonWorking: boolean,
    isRestricted: boolean
  ) => {
    try {
      if (!events.length) throw new Error("No calendars available to update");
      const calendarToPatch = events[0];

      const startDate = dayjs(start);
      const endDate = dayjs(end);
      let current = startDate;

      // Build updated special_days
      const updatedSpecialDays = [...(calendarToPatch.special_days || [])];

      while (current.isSameOrBefore(endDate, "day")) {
        const dateStr = current.format("YYYY-MM-DD");
        const idx = updatedSpecialDays.findIndex((d) => d.date === dateStr);

        if (!isNonWorking && !isRestricted) {
          // If neither option is selected, remove the day from special days if it exists
          if (idx >= 0) {
            updatedSpecialDays.splice(idx, 1);
          }
        } else if (idx >= 0) {
          // Update existing day
          updatedSpecialDays[idx].non_working_day = isNonWorking;
          updatedSpecialDays[idx].leave_restricted_day = isRestricted;

          // Set appropriate title
          if (isNonWorking && isRestricted) {
            updatedSpecialDays[idx].title = "Non-working & Restricted Day";
          } else if (isNonWorking) {
            updatedSpecialDays[idx].title = "Non-working Day";
          } else if (isRestricted) {
            updatedSpecialDays[idx].title = "Restricted Day";
          }
        } else {
          // Add new day
          updatedSpecialDays.push({
            date: dateStr,
            title: isNonWorking && isRestricted
              ? "Non-working & Restricted Day"
              : isNonWorking
                ? "Non-working Day"
                : "Restricted Day",
            non_working_day: isNonWorking,
            is_public_holiday: false,
            leave_restricted_day: isRestricted,
            reason: "",
          });
        }
        current = current.add(1, "day");
      }

      // Patch the calendar with updated special_days
      await axios.patch(
        `${BASE_URL}/users/organization-calendar/${calendarToPatch.id}`,
        {
          ...calendarToPatch,
          special_days: updatedSpecialDays,
        },
        { headers: { Authorization: `Token ${token}` } }
      );

      let successMsg = "";
      if (!isNonWorking && !isRestricted) {
        successMsg = "Days reset to normal working days successfully!";
      } else if (isNonWorking && isRestricted) {
        successMsg = "Days marked as non-working and restricted successfully!";
      } else if (isNonWorking) {
        successMsg = "Days marked as non-working successfully!";
      } else {
        successMsg = "Days marked as restricted successfully!";
      }

      setSuccessMessage(successMsg);
      fetchEvents();
    } catch (error) {
      setErrorMessage(
        (axios.isAxiosError(error) && error.response?.data?.message) ||
          `Failed to update calendar days.`
      );
    }
  };

  // BREADCRUMB for navigation
  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Calendar Management
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  // Transform organization calendar data into calendar events for display
  const calendarEvents: CalendarEvent[] = useMemo(() => {
    const combined: CalendarEvent[] = [];

    events.forEach((cal) => {
      cal.special_days?.forEach((sd) => {
        combined.push({
          title: sd.title,
          start: dayjs(sd.date).toDate(),
          end: dayjs(sd.date).toDate(),
          allDay: true,
          type: (() => {
            if (sd.is_public_holiday) return "public";
            if (sd.non_working_day && sd.leave_restricted_day) return "nonWorkingAndRestricted";
            if (sd.leave_restricted_day) return "restricted";
            if (sd.non_working_day) return "nonWorking";
            return "special";
          })(),
        });
      });
    });

    return combined;
  }, [events]);

  // Styling for calendar events based on type
  const eventStyleGetter = (
    event: CalendarEvent,
    start: Date,
    end: Date,
    isSelected: boolean
  ) => {
    let backgroundColor = "#3182CE"; // default
    let fontWeight = "normal";
    let border = "none";
    let backgroundImage = "none";

    switch (event.type) {
      case "public":
        backgroundColor = "#fc2a0d";
        fontWeight = "bold";
        break;
      case "restricted":
        backgroundColor = "#6a0dad";
        border = "1px dashed #fff";
        break;
      case "nonWorking":
        backgroundColor = "#891d1a";
        break;
      case "nonWorkingAndRestricted":
        backgroundColor = "#5d4037"; // Brown color for combined type
        border = "1px dashed #fff";
        backgroundImage = "linear-gradient(45deg, #891d1a 50%, #6a0dad 50%)";
        fontWeight = "bold";
        break;
      case "special":
        backgroundColor = "#042069";
        break;
      default:
        backgroundColor = "#3182CE";
    }

    return {
      style: {
        backgroundColor,
        backgroundImage,
        color: "#fff",
        borderRadius: "4px",
        border,
        fontWeight,
        padding: "2px 4px",
        fontSize: "0.85rem",
        boxShadow: isSelected ? "0 0 0 2px rgba(255,255,255,0.9)" : "none",
      },
    };
  };

  return (
    <Screen headerContent={breadcrumb}>
      <div className="p-6">
        <h1 className="text-2xl font-semibold mb-4">Calendar Management</h1>
        {errorMessage && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}
        {successMessage && (
          <Alert variant="default" className="mb-4">
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{successMessage}</AlertDescription>
          </Alert>
        )}

        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="link"
            onClick={handleOpenCreate}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" /> Create New Calendar
          </Button>

          <Button
            variant="outline"
            onClick={() => setRestrictDialogOpen(true)}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" /> Manage Special Days
          </Button>
        </div>

        {/* Display existing calendars in a table */}
        <div className="overflow-x-auto border rounded-md mb-8">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {["Calendar Name", "Description", "Start Date", "End Date", "Actions"].map(
                  (heading) => (
                    <th
                      key={heading}
                      className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase"
                    >
                      {heading}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={5} className="px-4 py-4 text-center text-sm text-gray-500">
                    Loading...
                  </td>
                </tr>
              ) : events.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-4 py-4 text-center text-sm text-gray-500">
                    No calendars found.
                  </td>
                </tr>
              ) : (
                events.map((ev) => (
                  <tr key={ev.id || ev.calendar_name}>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                      {ev.calendar_name}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                      {ev.calendar_description}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                      {ev.calendar_start_date}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                      {ev.calendar_end_date}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-right">
                      <Button variant="ghost" onClick={() => handleOpenEdit(ev)} className="p-1">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        onClick={() => ev.id && handleDelete(ev.id)}
                        className="p-1"
                      >
                        <Trash className="w-4 h-4 text-red-600" />
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Calendar View and Legend */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-xl">Calendar View</CardTitle>
              </CardHeader>
              <CardContent>
                <div style={{ height: 600 }} className="calendar-container">
                  <Calendar
                    localizer={localizer}
                    events={calendarEvents}
                    startAccessor="start"
                    endAccessor="end"
                    eventPropGetter={eventStyleGetter}
                    views={["month", "week", "day"]}
                    defaultView="month"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="lg:w-1/3">
            <CalendarLegend />
          </div>
        </div>
      </div>

      {/* Create/Edit Calendar Dialog */}
      <CalendarEventForm
        open={dialogOpen}
        onClose={handleCloseDialog}
        initialData={editingEvent || undefined}
        onSave={handleSaveEvent}
      />

      {/* Restrict Days Dialog */}
      <RestrictDaysDialog
        open={restrictDialogOpen}
        onClose={() => setRestrictDialogOpen(false)}
        onApply={handleApplyRestrict}
      />
    </Screen>
  );
};

export default CalendarEventsPage;
