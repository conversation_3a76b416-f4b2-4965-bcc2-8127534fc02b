import { useEffect, useState } from "react";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { EmployeeContractInfo } from "../../types/EmployeeTypes";
import { updateOrCreateContractInfoRecord } from "../../utils/EmployeeRecords";
import { TabLayout, FormGrid, FormField } from "../TabLayout";
import { FileText, Calendar } from "lucide-react";

import { BASE_URL } from "@/config";

interface ContractInfoTabProps {
  employeeNo: string;
  onSaveSuccess?: () => void;
}

export default function ContractInfoTab({ employeeNo, onSaveSuccess }: ContractInfoTabProps) {
  const token = useSelector((state: RootState) => state.auth.token);
  const [contractData, setContractData] = useState<EmployeeContractInfo | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  // Contract types from API documentation
  const CONTRACT_TYPES = [
  "Permanent",
  "Temporary",
  "Internship",
  "Consultant",
  "Contractor",
  "Volunteer",
  "Probation",
  "Management Trainee",
];

  // Function to fetch contract data
  async function fetchContractData() {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }

    setLoading(true);
    try {
      const headers = { Authorization: `Token ${token}` };
      const params = { employee_no: employeeNo };
      console.log("Fetching Contract Info for employee:", employeeNo);
      const res = await axios.get(
        `${BASE_URL}/users/employee-contract-info-details`,
        { headers, params }
      );
      console.log("Response from Contract Info API:", res.data);

      if (Array.isArray(res.data)) {
        // Find all records for this employee
        const records = res.data.filter(
          (item: EmployeeContractInfo) => item.employee_no === employeeNo
        );

        console.log(`Found ${records.length} contract info records for employee ${employeeNo}:`, records);

        if (records.length > 0) {
          // Always use the record with the highest ID (most recent)
          const record = records.reduce((prev, current) =>
            (prev.id && current.id && prev.id > current.id) ? prev : current
          );

          console.log("Selected contract info record:", record);

          // Ensure proper data structure with all required fields
          const contractRecord: EmployeeContractInfo = {
            id: record.id,
            employee_no: employeeNo,
            contract_type: record.contract_type || "",
            contract_start_date: record.contract_start_date || "",
            current_contract_end: record.current_contract_end || null,
            end_of_probation_date: record.end_of_probation_date || null,
            on_pip: Boolean(record.on_pip),
          };

          setContractData(contractRecord);
          return true;
        } else {
          console.log("No contract info record found, creating empty record");
          setContractData({
            employee_no: employeeNo,
            contract_type: "",
            contract_start_date: "",
            current_contract_end: null,
            end_of_probation_date: null,
            on_pip: false,
          });
        }
      }
    } catch (err: any) {
      console.error("Error fetching Contract Info:", err);
      toast.error("Failed to fetch Contract Info");
      setContractData({
        employee_no: employeeNo,
        contract_type: "",
        contract_start_date: "",
        current_contract_end: null,
        end_of_probation_date: null,
        on_pip: false,
      });
    } finally {
      setLoading(false);
    }
    return false;
  }

  useEffect(() => {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }

    fetchContractData();
  }, [token, employeeNo]);

  // Add a separate effect to handle refresh when component remounts or when the tab becomes active
  useEffect(() => {
    console.log("ContractInfoTab component mounted/refreshed for employee:", employeeNo);
    if (token && employeeNo) {
      // Force refresh on every mount to ensure latest data
      fetchContractData();
    }
  }, []); // Empty dependency array means this runs on mount

  // Add effect to refresh data when the component becomes visible again
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && token && employeeNo) {
        console.log("Tab became visible, refreshing contract data");
        fetchContractData();
      }
    };

    const handleFocus = () => {
      if (token && employeeNo) {
        console.log("Window focused, refreshing contract data");
        fetchContractData();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [token, employeeNo]);

  async function handleSave() {
    if (!contractData || !token || !employeeNo) return;

    // Validate required fields
    if (!contractData.contract_type || !contractData.contract_start_date) {
      toast.error("Contract type and start date are required");
      return;
    }

    try {
      // Prepare data for API
      const dataToSend = {
        ...contractData,
        employee_no: employeeNo,
        // Ensure empty date fields are handled properly
        current_contract_end: contractData.current_contract_end && contractData.current_contract_end.trim() !== ''
          ? contractData.current_contract_end
          : null,
        end_of_probation_date: contractData.end_of_probation_date && contractData.end_of_probation_date.trim() !== ''
          ? contractData.end_of_probation_date
          : null,
        // Ensure on_pip is a boolean
        on_pip: contractData.on_pip === undefined ? false : Boolean(contractData.on_pip)
      };

      console.log("Sending contract data from component:", dataToSend);

      const updated = await updateOrCreateContractInfoRecord<EmployeeContractInfo>(
        `${BASE_URL}/users/employee-contract-info-details`,
        token,
        dataToSend
      );

      if (updated) {
        console.log("Contract data updated successfully:", updated);

        // Update local state with the returned data, ensuring proper structure
        const updatedContractData: EmployeeContractInfo = {
          id: updated.id,
          employee_no: employeeNo,
          contract_type: updated.contract_type || "",
          contract_start_date: updated.contract_start_date || "",
          current_contract_end: updated.current_contract_end || null,
          end_of_probation_date: updated.end_of_probation_date || null,
          on_pip: Boolean(updated.on_pip),
        };

        setContractData(updatedContractData);
        toast.success("Contract info saved successfully");

        // Force refresh the data after save to ensure UI is up to date
        await fetchContractData();

        // Call the onSaveSuccess callback if provided
        if (onSaveSuccess) {
          onSaveSuccess();
        }
      } else {
        toast.error("Failed to save contract info - no data returned");
      }
    } catch (error) {
      console.error("Error saving Contract Info:", error);
      toast.error("Failed to save Contract info");
    }
  }

  if (loading) {
    return null;
  }
  if (!contractData) return null;

  return (
    <TabLayout
      title="Contract Information"
      description="Manage employee contract details and status"
      employeeNo={employeeNo}
      loading={loading}
      actions={
        <Button
          onClick={handleSave}
          className="flex items-center gap-2"
        >
          <FileText className="h-4 w-4" />
          Save Contract
        </Button>
      }
    >
      <FormGrid columns={2}>
        <FormField>
          <Label>Contract Type</Label>
          <select
            className="w-full border rounded px-2 py-1 bg-background focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all"
            value={contractData.contract_type}
            onChange={(e) =>
              setContractData((prev) =>
                prev ? { ...prev, contract_type: e.target.value } : prev
              )
            }
          >
            <option value="">Select...</option>
            {CONTRACT_TYPES.map((opt) => (
              <option key={opt} value={opt}>
                {opt}
              </option>
            ))}
          </select>
        </FormField>

        <FormField>
          <Label>Contract Start Date</Label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="date"
              value={contractData.contract_start_date || ""}
              onChange={(e) =>
                setContractData((prev) =>
                  prev
                    ? { ...prev, contract_start_date: e.target.value }
                    : prev
                )
              }
              className="pl-10"
            />
          </div>
        </FormField>

        <FormField>
          <Label>Current Contract End</Label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="date"
              value={contractData.current_contract_end || ""}
              onChange={(e) =>
                setContractData((prev) =>
                  prev
                    ? { ...prev, current_contract_end: e.target.value }
                    : prev
                )
              }
              className="pl-10"
            />
          </div>
        </FormField>

        <FormField>
          <Label>End of Probation Date</Label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="date"
              value={contractData.end_of_probation_date || ""}
              onChange={(e) =>
                setContractData((prev) =>
                  prev
                    ? { ...prev, end_of_probation_date: e.target.value }
                    : prev
                )
              }
              className="pl-10"
            />
          </div>
        </FormField>

        {/* ON PIP (PERFORMANCE IMPROVEMENT PLAN) */}
        <FormField className="col-span-1 md:col-span-2">
          <Label>Performance Improvement Plan (PIP) Status</Label>
          <div className="flex items-center mt-2 p-3 rounded-md border bg-muted/20">
            <input
              type="checkbox"
              id="pip-checkbox"
              className="mr-3 h-4 w-4 rounded border-primary text-primary focus:ring-primary"
              checked={contractData.on_pip || false}
              onChange={(e) =>
                setContractData((prev) =>
                  prev
                    ? { ...prev, on_pip: e.target.checked }
                    : prev
                )
              }
            />
            <label htmlFor="pip-checkbox" className="text-sm font-medium cursor-pointer">
              Employee is currently on Performance Improvement Plan
            </label>
          </div>
        </FormField>
      </FormGrid>
    </TabLayout>
  );
}
