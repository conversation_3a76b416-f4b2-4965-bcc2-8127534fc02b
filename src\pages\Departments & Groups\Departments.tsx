import { useState, useEffect, useMemo, useCallback } from "react";
import axios from "axios";
import { BASE_URL } from "../../config";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Building,
  Plus,
  Pencil,
  Trash2,
  RefreshCw,
  AlertCircle,
  Search,
  CheckCircle2,
  ArrowUpDown,
  Filter,
} from "lucide-react";
import DepartmentForm from "./components/DepartmentForm";

// Type definitions
interface Department {
  id: number;
  name: string;
  description: string;
  dep_head: string;
  dep_head_assistant: string;
  dep_hr: string;
  department_status_active: boolean;
  organisation: number;
  parent_department: number | null;
}

interface Employee {
  employee_no: string;
  username: string;
  first_name: string;
  last_name: string;
  // additional fields if needed
}

// Updated form schema validation to make parent_department mandatory
const departmentSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().min(1, "Description is required"),
  dep_head: z.string().min(1, "Department head is required").max(60, "Department head must be less than 60 characters"),
  dep_head_assistant: z.string().min(1, "Department head assistant is required").max(60, "Department head assistant must be less than 60 characters"),
  dep_hr: z.string().min(1, "Department HR is required").max(60, "Department HR must be less than 60 characters"),
  department_status_active: z.boolean().default(true),
  organisation: z.number(),
  // Make parent_department required
  parent_department: z.number({ required_error: "Parent department is required" })
    .min(1, "Parent department is required"),
});

export default function Departments() {
  const { token } = useSelector((state: RootState) => state.auth);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentDepartment, setCurrentDepartment] = useState<Department | null>(null);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState("");
  const [sortConfig, setSortConfig] = useState<{ key: keyof Department; direction: 'ascending' | 'descending' } | null>(null);
  const [successMessage, setSuccessMessage] = useState("");

  // Default organisation ID
  const defaultOrganisationId = 1;

  // Initialize react-hook-form
  const form = useForm<z.infer<typeof departmentSchema>>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: "",
      description: "",
      dep_head: "",
      dep_head_assistant: "",
      dep_hr: "",
      department_status_active: true,
      organisation: defaultOrganisationId,
      // Force user to select a valid department (no "none" / 0 by default)
      parent_department: 1,
    },
  });

  // Reset form when dialog closes
  useEffect(() => {
    if (!isAddDialogOpen && !isEditDialogOpen) {
      form.reset();
      setFormError("");
    }
  }, [isAddDialogOpen, isEditDialogOpen, form]);

  // Optimized fetch functions with useCallback
  const fetchDepartments = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      const response = await axios.get(`${BASE_URL}/users/departments`, {
        headers: { Authorization: `Token ${token}` },
      });
      setDepartments(response.data);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching departments:", err);
      setError("Failed to load departments. Please try again.");
      setLoading(false);
    }
  }, [token]);

  const fetchEmployees = useCallback(async () => {
    try {
      const response = await axios.get(`${BASE_URL}/users/all-employees`, {
        headers: { Authorization: `Token ${token}` },
      });
      setEmployees(response.data);
    } catch (err) {
      console.error("Error fetching employees:", err);
    }
  }, [token]);

  // Initial data fetch
  useEffect(() => {
    if (token) {
      Promise.all([fetchDepartments(), fetchEmployees()]);
    }
  }, [token, fetchDepartments, fetchEmployees]);

  // Memoized employee name lookup
  const employeeMap = useMemo(() => {
    return employees.reduce((acc, emp) => {
      // Create full name with proper spacing
      const fullName = `${emp.first_name} ${emp.last_name}`.trim();
      acc[emp.employee_no] = fullName || emp.username; // Fallback to username if names are empty
      return acc;
    }, {} as Record<string, string>);
  }, [employees]);

  const getEmployeeName = useCallback((employeeNo: string) => {
    return employeeMap[employeeNo] || employeeNo;
  }, [employeeMap]);

  // Memoized filtering and sorting
  const filteredDepartments = useMemo(() => {
    let result = [...departments];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      result = result.filter(
        (dept) =>
          dept.name.toLowerCase().includes(searchLower) ||
          dept.description.toLowerCase().includes(searchLower) ||
          getEmployeeName(dept.dep_head).toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (statusFilter) {
      result = statusFilter === "active"
        ? result.filter((dept) => dept.department_status_active)
        : result.filter((dept) => !dept.department_status_active);
    }

    // Apply sorting
    if (sortConfig !== null) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.key] ?? '';
        const bValue = b[sortConfig.key] ?? '';
        if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
        return 0;
      });
    }

    return result;
  }, [departments, searchTerm, statusFilter, sortConfig, getEmployeeName]);

  const requestSort = useCallback((key: keyof Department) => {
    let direction: "ascending" | "descending" = "ascending";
    if (sortConfig && sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  }, [sortConfig]);



  // Optimized form submission handlers with auto-refresh
  const onSubmitAdd = useCallback(async (data: z.infer<typeof departmentSchema>) => {
    try {
      setIsSubmitting(true);
      setFormError("");
      await axios.post(`${BASE_URL}/users/departments`, data, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchDepartments();
      setIsAddDialogOpen(false);
      form.reset();
      setSuccessMessage("Department created successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err: any) {
      console.error("Error creating department:", err);
      setFormError(err.response?.data?.message || "Failed to create department. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [token, fetchDepartments, form]);

  const onSubmitEdit = useCallback(async (data: z.infer<typeof departmentSchema>) => {
    if (!currentDepartment) return;
    try {
      setIsSubmitting(true);
      setFormError("");
      await axios.patch(`${BASE_URL}/users/departments/${currentDepartment.id}`, data, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchDepartments();
      setIsEditDialogOpen(false);
      setCurrentDepartment(null);
      form.reset();
      setSuccessMessage("Department updated successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err: any) {
      console.error("Error updating department:", err);
      setFormError(err.response?.data?.message || "Failed to update department. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [currentDepartment, token, fetchDepartments, form]);

  const handleDelete = useCallback(async () => {
    if (!deleteId) return;
    try {
      setIsSubmitting(true);
      await axios.delete(`${BASE_URL}/users/departments/${deleteId}`, {
        headers: { Authorization: `Token ${token}` },
      });
      await fetchDepartments();
      setDeleteId(null);
      setSuccessMessage("Department deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (err: any) {
      console.error("Error deleting department:", err);
      setError(err.response?.data?.message || "Failed to delete department. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [deleteId, token, fetchDepartments]);

  const handleEdit = (department: Department) => {
    setCurrentDepartment(department);
    form.reset({
      name: department.name,
      description: department.description,
      dep_head: department.dep_head,
      dep_head_assistant: department.dep_head_assistant,
      dep_hr: department.dep_hr,
      department_status_active: department.department_status_active,
      organisation: department.organisation,
      // If there's no parent_department, we'll coerce to 0 or fallback.
      // But since it's mandatory, ensure the user picks something in the form.
      parent_department: department.parent_department || 1,
    });
    setIsEditDialogOpen(true);
  };

  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setStatusFilter(null);
    setSortConfig(null);
  }, []);

  // Breadcrumb navigation
  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink href="/" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block" />
        <BreadcrumbItem>
          <BreadcrumbLink href="/dashboard" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Dashboard
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Departments</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  if (loading && departments.length === 0) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading departments...</p>
          </div>
        </div>
      </Screen>
    );
  }

  if (error && departments.length === 0) {
    return (
      <Screen headerContent={breadcrumb}>
        <div className="container mx-auto p-6 flex justify-center items-center h-[70vh]">
          <div className="bg-red-100 dark:bg-red-900/30 p-6 rounded-lg max-w-md text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-red-700 dark:text-red-400 mb-2">Error Loading Departments</h2>
            <p className="text-gray-700 dark:text-gray-300">{error}</p>
            <button
              onClick={() => fetchDepartments()}
              className="mt-4 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
            >
              <RefreshCw className="w-4 h-4 inline-block mr-2" />
              Retry
            </button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen headerContent={breadcrumb}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
              <Building className="w-7 h-7 mr-2 text-green-600 dark:text-green-400" />
              Departments Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Create, view, update, and manage your organization's departments
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700 shadow-lg hover:shadow-xl transition-all duration-200">
                <Plus className="w-4 h-4 mr-2" />
                Add Department
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="text-xl font-semibold">Add New Department</DialogTitle>
                <DialogDescription>
                  Create a new department in your organization. Fill out the details below.
                </DialogDescription>
              </DialogHeader>
              <DepartmentForm
                form={form}
                onSubmit={onSubmitAdd}
                isSubmitting={isSubmitting}
                employees={employees}
                departments={departments}
                submitButtonText="Create Department"
                formError={formError}
              />
            </DialogContent>
          </Dialog>
        </div>
        {successMessage && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4 flex items-center">
            <CheckCircle2 className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
            <p className="text-green-600 dark:text-green-400">{successMessage}</p>
          </div>
        )}
        <Card className="shadow-sm">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col lg:flex-row gap-4 lg:items-center justify-between">
              <div className="flex-1 relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search departments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10 border-gray-200 focus:border-green-500 focus:ring-green-500"
                />
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <Select
                  value={statusFilter || "all"}
                  onValueChange={(value) => setStatusFilter(value === "all" ? null : value)}
                >
                  <SelectTrigger className="w-full sm:w-[180px] h-10">
                    <div className="flex items-center">
                      <Filter className="w-4 h-4 mr-2" />
                      <SelectValue placeholder="Filter by status" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="h-10 hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center justify-between text-lg font-semibold">
              <span>Departments ({filteredDepartments.length})</span>
              {loading && (
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Refreshing data...
                </div>
              )}
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400">
              Overview of all departments in your organization
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredDepartments.length === 0 ? (
              <div className="text-center py-8">
                <Building className="w-12 h-12 mx-auto text-gray-400 mb-3" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">No departments found</h3>
                <p className="text-gray-500 dark:text-gray-400 mt-1">
                  {searchTerm || statusFilter
                    ? "Try adjusting your search or filters"
                    : "Create your first department to get started"}
                </p>
                {(searchTerm || statusFilter) && (
                  <Button variant="outline" onClick={clearFilters} className="mt-4">
                    Clear Filters
                  </Button>
                )}
              </div>
            ) : (
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead
                          className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 min-w-[150px]"
                          onClick={() => requestSort('name')}
                        >
                          <div className="flex items-center">
                            Name
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead className="hidden md:table-cell min-w-[200px]">Description</TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 min-w-[150px]"
                          onClick={() => requestSort('dep_head')}
                        >
                          <div className="flex items-center">
                            <span className="hidden sm:inline">Department </span>Head
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead className="hidden lg:table-cell min-w-[150px]">Department HR</TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 min-w-[100px]"
                          onClick={() => requestSort('department_status_active')}
                        >
                          <div className="flex items-center">
                            Status
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </div>
                        </TableHead>
                        <TableHead className="text-right min-w-[120px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredDepartments.map((department) => (
                        <TableRow key={department.id} className="hover:bg-gray-50 dark:hover:bg-gray-800/50">
                          <TableCell className="font-medium">{department.name}</TableCell>
                          <TableCell className="hidden md:table-cell max-w-xs truncate">{department.description}</TableCell>
                          <TableCell>{getEmployeeName(department.dep_head)}</TableCell>
                          <TableCell className="hidden lg:table-cell">{getEmployeeName(department.dep_hr)}</TableCell>
                          <TableCell>
                            {department.department_status_active ? (
                              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                Active
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                                Inactive
                              </Badge>
                            )}
                          </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-1 sm:space-x-2">
                            <Dialog
                              open={isEditDialogOpen && currentDepartment?.id === department.id}
                              onOpenChange={setIsEditDialogOpen}
                            >
                              <DialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-green-50 hover:border-green-300 dark:hover:bg-green-900/20"
                                  onClick={() => handleEdit(department)}
                                >
                                  <Pencil className="h-3 w-3" />
                                  <span className="sr-only">Edit department</span>
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
                                <DialogHeader>
                                  <DialogTitle className="text-xl font-semibold">Edit Department</DialogTitle>
                                  <DialogDescription>
                                    Update information for {currentDepartment?.name}
                                  </DialogDescription>
                                </DialogHeader>
                                <DepartmentForm
                                  form={form}
                                  onSubmit={onSubmitEdit}
                                  isSubmitting={isSubmitting}
                                  employees={employees}
                                  departments={departments}
                                  submitButtonText="Update Department"
                                  formError={formError}
                                />
                              </DialogContent>
                            </Dialog>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-red-50 hover:border-red-300 dark:hover:bg-red-900/20"
                                  onClick={() => setDeleteId(department.id)}
                                >
                                  <Trash2 className="h-3 w-3" />
                                  <span className="sr-only">Delete department</span>
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent className="sm:max-w-md">
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action will permanently delete the department "{department.name}" and cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel onClick={() => setDeleteId(null)}>
                                    Cancel
                                  </AlertDialogCancel>
                                  <AlertDialogAction onClick={handleDelete} disabled={isSubmitting}>
                                    {isSubmitting ? (
                                      <>
                                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Screen>
  );
}
