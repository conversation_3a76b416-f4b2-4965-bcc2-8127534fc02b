import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { Notification } from '@/utils/Notification';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import CreateJobModal from './CreateJobModal';
import JobDetails from './JobDetails';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Plus, Trash, Filter, FileText, ArrowLeft, Eye,} from 'lucide-react';
import { Link } from 'react-router-dom';


function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(() => window.matchMedia(query).matches);
  useEffect(() => {
    const mediaQueryList = window.matchMedia(query);
    const listener = (e: MediaQueryListEvent) => setMatches(e.matches);
    mediaQueryList.addEventListener('change', listener);
    return () => mediaQueryList.removeEventListener('change', listener);
  }, [query]);
  return matches;
}


const Spinner = () => (
  <svg className="animate-spin h-5 w-5 text-green-500 mx-auto" viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
    />
  </svg>
);


const TableContainer = ({ children }: { children: React.ReactNode }) => (
  <div className="bg-white rounded-lg shadow overflow-auto mt-6 min-w-[600px] scrollbar-thin scrollbar-thumb-gray-300">
    {children}
  </div>
);

interface StatusPillProps {
  status: string;
  children: React.ReactNode;
}
const StatusPill = ({ status, children }: StatusPillProps) => {
  let bgColor = '';
  let textColor = '';
  if (status === 'open') {
    bgColor = 'bg-green-100';
    textColor = 'text-green-800';
  } else if (status === 'closed') {
    bgColor = 'bg-red-100';
    textColor = 'text-red-800';
  } else {
    bgColor = 'bg-yellow-100';
    textColor = 'text-yellow-800';
  }
  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}
    >
      {children}
    </span>
  );
};


interface Job {
  id: number;
  job_title: string;
  job_description: string;
  posission_status: string;
  job_min_salary: number;
  job_max_salary: number;
  date_created: string;
  no_of_employees: number;
}

interface JobListProps {
  onJobSelect?: (job: Job) => void;
  filters?: Record<string, unknown>;
  compact?: boolean;
}



export const JobList = ({}: JobListProps) => {
  const isMobile = useMediaQuery('(max-width: 640px)');
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<number | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const token = useSelector((state: RootState) => state.auth.token);


  const fetchJobs = useCallback(async () => {
    try {
      const response = await axios.get(`${BASE_URL}/hrm/job-positions`, {
        headers: { Authorization: `Token ${token}` },
      });
      setJobs(response.data);
      console.log('Jobs', response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching jobs:', error);
      Notification.error('Failed to load jobs. Please try again later.');
      setLoading(false);
    }
  }, [token]);

  useEffect(() => {
    if (token) fetchJobs();
  }, [fetchJobs, token]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value.toLowerCase());
  };

  const filteredJobs = jobs.filter(
    (job) =>
      job.job_title.toLowerCase().includes(searchTerm) ||
      job.job_description.toLowerCase().includes(searchTerm)
  );

  const handleDelete = async (id: number) => {
    try {
      await axios.delete(`${BASE_URL}/hrm/job-positions/${id}`, {
        headers: { Authorization: `Token ${token}` },
      });
      setJobs((prev) => prev.filter((job) => job.id !== id));
      Notification.success('Job position deleted successfully');
    } catch (error) {
      console.error('Error deleting job:', error);
      Notification.error('Failed to delete job. Please try again.');
    }
  };


  return (
    <TooltipProvider>
      <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
        <div className="mb-4">
          {/* Header Section */}
          <div className="flex justify-between items-center mb-4">
            <h1 className={isMobile ? 'text-lg font-bold' : 'text-2xl font-bold'}>Job Positions</h1>
            <Button
              onClick={() => setModalOpen(true)}
              className="bg-gradient-to-br from-green-500 to-green-800 hover:shadow-lg text-white"
            >
              <Plus className="mr-2 h-4 w-4" /> New Position
            </Button>
          </div>

          {/* Search & Filters */}
          <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'} gap-2 mb-3`}>
            <Input
              placeholder="Search jobs..."
              onChange={handleSearch}
              className="rounded-lg"
            />
            <Button variant="outline" className="min-w-[120px] rounded-lg">
              <Filter className="mr-2 h-4 w-4" /> Filters
            </Button>
          </div>

          {/* Jobs Table */}
          <TableContainer>
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-green-600">
                <tr>
                  <th className="px-4 py-2 text-left text-white font-semibold text-sm whitespace-nowrap">
                    Job Title
                  </th>
                  {!isMobile && (
                    <>
                      <th className="px-4 py-2 text-left text-white font-semibold text-sm whitespace-nowrap">
                        Status
                      </th>
                      <th className="px-4 py-2 text-left text-white font-semibold text-sm whitespace-nowrap">
                        Salary Range
                      </th>
                      <th className="px-4 py-2 text-left text-white font-semibold text-sm whitespace-nowrap">
                        Employees
                      </th>
                      <th className="px-4 py-2 text-left text-white font-semibold text-sm whitespace-nowrap">
                        Created
                      </th>
                    </>
                  )}
                  <th className="px-4 py-2 text-left text-white font-semibold text-sm whitespace-nowrap">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="text-center py-4">
                      <Spinner />
                    </td>
                  </tr>
                ) : filteredJobs.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="py-8 text-center">
                      <div className="p-3">
                        <FileText className="mx-auto mb-1 h-10 w-10 text-gray-400" />
                        <p className="text-gray-500">No job positions found</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredJobs.map((job) => (
                    <tr key={job.id} className="hover:bg-gray-50">
                      <td className="px-4 py-2">
                        <div className="font-medium">{job.job_title}</div>
                        <div className="text-sm text-gray-500">
                          {job.job_description.slice(0, isMobile ? 30 : 50)}...
                        </div>
                      </td>
                      {!isMobile && (
                        <>
                          <td className="px-4 py-2">
                            <StatusPill status={job.posission_status}>{job.posission_status}</StatusPill>
                          </td>
                          <td className="px-4 py-2 whitespace-nowrap">
                            Ksh {job.job_min_salary} - Ksh {job.job_max_salary}
                          </td>
                          <td className="px-4 py-2">{job.no_of_employees}</td>
                          <td className="px-4 py-2 whitespace-nowrap">
                            {new Date(job.date_created).toLocaleDateString()}
                          </td>
                        </>
                      )}
                      <td className="px-4 py-2">
                        <div className="flex gap-1">
                          {/* View Details Tooltip */}
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Link to={`/job-management/${job.id}`} className="text-green-500 hover:text-green-700">
                                <Eye className="h-5 w-5" />
                              </Link>
                            </TooltipTrigger>
                            <TooltipContent side="top">View Details</TooltipContent>
                          </Tooltip>
                          {/* Delete Tooltip */}
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <button
                                onClick={() => {
                                  setJobToDelete(job.id);
                                  setDeleteConfirmOpen(true);
                                }}
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash className="h-5 w-5" />
                              </button>
                            </TooltipTrigger>
                            <TooltipContent side="top">Delete</TooltipContent>
                          </Tooltip>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </TableContainer>
          <Dialog
            open={deleteConfirmOpen}
            onOpenChange={(open) => !open && setDeleteConfirmOpen(false)}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Job Position</DialogTitle>
              </DialogHeader>
              <div className="py-4">
                Are you sure you want to permanently delete this job position?
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setDeleteConfirmOpen(false)}>
                  Cancel
                </Button>
                <Button
                  color="destructive"
                  onClick={() => {
                    if (jobToDelete) handleDelete(jobToDelete);
                    setDeleteConfirmOpen(false);
                  }}
                >
                  Confirm Delete
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          <CreateJobModal
            open={modalOpen}
            onClose={() => setModalOpen(false)}
            onSuccess={(newJob) => {
              setJobs((prev) => [...prev, newJob]);
              setModalOpen(false);
            }}
          />
        </div>
      </motion.div>
    </TooltipProvider>
  );
};

export default JobList;


export function JobDetailsWrapper() {
  const { jobId } = useParams<{ jobId: string }>();
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const token = useSelector((state: RootState) => state.auth.token);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchJob = async () => {
      try {
        const response = await axios.get(`${BASE_URL}/hrm/job-positions/${jobId}`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (response.data) {
          setJob(response.data);
        } else {
          setError('Job not found');
        }
      } catch (err) {
        setError('Failed to load job details');
        Notification.error('Failed to load job details');
      } finally {
        setLoading(false);
      }
    };

    if (jobId) fetchJob();
  }, [jobId, token]);

  const handleBack = () => navigate('/job-management');

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <Spinner />
      </div>
    );
  }

  if (error || !job) {
    return (
      <div className="p-4 text-center">
        <Link to = "/job-management">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Jobs
        </Link>
        <h2 className="text-lg text-red-500">{error || 'Job not found'}</h2>
      </div>
    );
  }

  return (
    <div className="p-4">
      <Button onClick={handleBack} variant="outline" className="mb-3">
        <ArrowLeft className="mr-2 h-4 w-4" /> Back to Jobs
      </Button>
      <JobDetails
        job={job}
        onBack={handleBack}
        onEdit={() => navigate(`/job-management/${job.id}/edit`)}
      />
    </div>
  );
}
