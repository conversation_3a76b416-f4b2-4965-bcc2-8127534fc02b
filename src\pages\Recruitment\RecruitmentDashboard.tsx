// RecruiterHub.tsx
"use client";

import React, { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, Breadcrumb<PERSON>age, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Ta<PERSON>, Ta<PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from "@/components/ui/tabs";

import PipelineAnalytics from "./tabs/PipelineAnalytics";
import OpenRequisitions from "./tabs/OpenRequisitions";
import CandidatePool from "./tabs/CandidatePool";
import InterviewsTab from "./tabs/InterviewsTab";
import InterviewResultsTab from "./tabs/InterviewResultsTab";
// Import your new TalentPoolTab
import TalentPoolTab from "./tabs/TalentPool";

const RecruiterHub: React.FC = () => {
  const [tabIndex, setTabIndex] = useState<number>(0);
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null);

  return (
    <Screen>
      <div className="container mx-auto p-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Recruiter Hub</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <h1 className="text-3xl font-bold my-4">Recruiter Hub</h1>

        <Tabs
          value={tabIndex.toString()}
          onValueChange={(v) => setTabIndex(parseInt(v, 10))}
        >
          <TabsList className="mb-4">
            <TabsTrigger value="0">Pipeline Analytics</TabsTrigger>
            <TabsTrigger value="1">Open Requisitions</TabsTrigger>
            <TabsTrigger value="2">Candidate Pool</TabsTrigger>
            <TabsTrigger value="3">Manage Interviews</TabsTrigger>
            <TabsTrigger value="4">Interview Results</TabsTrigger>
            {/* Replaced 'Reports' with 'Talent Pool' */}
            <TabsTrigger value="5">Talent Pool</TabsTrigger>
          </TabsList>

          <TabsContent value="0">
            <PipelineAnalytics
              selectedJobId={selectedJobId}
              setSelectedJobId={setSelectedJobId}
            />
          </TabsContent>
          <TabsContent value="1">
            <OpenRequisitions />
          </TabsContent>
          <TabsContent value="2">
            <CandidatePool />
          </TabsContent>
          <TabsContent value="3">
            <InterviewsTab />
          </TabsContent>
          <TabsContent value="4">
            <InterviewResultsTab />
          </TabsContent>
          <TabsContent value="5">
            {/* Talent Pool Tab */}
            <TalentPoolTab />
          </TabsContent>
        </Tabs>
      </div>
    </Screen>
  );
};

export default RecruiterHub;
