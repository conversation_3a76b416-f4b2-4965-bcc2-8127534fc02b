// Updated interfaces based on HRDashboard.tsx
export interface AppraisalViewModel {
    // Combined data structure for appraisal period and employee appraisal
    id: number; // This is the EmployeeAppraisal.id
    period_id?: number; // This field may need to be derived
    name: string;
    start_date: string;
    end_date: string;
    cycle_type: string;
    description: string;
    is_active: boolean;
    date_created: string;
    closed_date: string | null;
    
    // Employee Appraisal data
    status: string;
    employee_acceptance: boolean;
    supervisor_acceptance: boolean | null;
    total_emp_self_rating_score: number | null;
    total_supervisor_rating_score: number | null;
    final_hr_comments: string | null;
    supervisor_comments: string | null;
    way_forward: string | null;
    employeeid: string;
    supervisor: string;
    
    // Metadata
    created_by: string;
    closed_by: string | null;
    
    // Additional fields for UI display
    employee_name?: string;
    department_name?: string;
    department_id?: number;
    kpis?: KPI[];
  }
  
  export interface AppraisalResult {
    id: number;
    MIB_target?: number | null;
    MIB_Achieved?: number | null;
    Sales_target?: number | null;
    Sales_Achieved?: number | null;
    employee_rating: number | null;
    employee_comments: string | null;
    supervisor_comments: string | null;
    supervisor_rating: number | null;
    manager_comments?: string | null;
    manager_rating?: number | null;
    extra_comments?: string | null;
    extra_rating?: number | null;
    appraisal_period: number;
    what: number;
    employee: string;
  }
  
  export interface KPI {
    id: number;
    Kpi_type: string;
    what: string;
    MIB_target: number | null;
    Sales_target: number | null;
    total_marks: number;
    status: string;
    created_by: string;
    hows?: KpiHow[];
    created_at: string;
    kpi_type: "default" | "custom" | "MIB Target" | "Sales Target";
  }
  
  export interface Employee {
    id: number;
    employee_no_id: string;
    teams_code_id: number | null;
    department_id: number;
    first_name: string;
    last_name: string;
    email: string;
    job_title: string;
    is_active: number;
    // Keep these for backward compatibility if needed
    employee_no?: string;
  }
  
  export interface Department {
    id: number;
    name: string;
  }

  export interface KpiHow {
    id: number;
    how: string;
  }
  
  export interface AppraisalPeriod {
    id: number;
    name: string;
    start_date: string;
    end_date: string;
    cycle_type: string;
    description: string;
    is_active: boolean;
    created_by: string;
  }
  
  export interface AppraisalPeriodForm {
    name: string;
    start_date: string;
    end_date: string;
    cycle_type: string;
    description: string;
    contract_type?: string;
    end_of_probation_date?: string | null;
    on_pip?: boolean;
  }
  
  export interface KpiDetail {
    id: number;
    what: string;
    hows: string[];
    total_marks: number;
    employee_rating?: number;
    supervisor_rating?: number;
    employee_comments?: string;
    supervisor_comments?: string;
    manager_comments?: string;
    manager_rating?: number;
    extra_comments?: string;
    extra_rating?: number;
    kpi_type?: string;
    MIB_target?: number | null;
    MIB_Achieved?: number | null;
    Sales_target?: number | null;
    Sales_Achieved?: number | null;
  }
  