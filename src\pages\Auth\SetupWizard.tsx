// src/pages/SetupPage.tsx
"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import OrganizationTab from "./auth-components/OrganizationForm";
import DepartmentsTab from "./auth-components/DepartmentsForm";
import GroupsTab from "./auth-components/GroupsForm";
import EmploymentTypesTab from "./auth-components/EmploymentTypesForm";
import UserTab from "./auth-components/UserForm";

type TabValue = "organization" | "departments" | "groups" | "employment" | "user";

const SetupPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabValue>("organization");

  const handleOrgSuccess = () => {
    // Organization created – move to Departments.
    setActiveTab("departments");
  };

  const handleDepartmentsNext = () => {
    // After departments are added, move to Groups.
    setActiveTab("groups");
  };

  const handleGroupsNext = () => {
    // Groups are optional; once finished (or skipped) move to Employment Types.
    setActiveTab("employment");
  };

  const handleEmploymentNext = () => {
    // Move to User creation.
    setActiveTab("user");
  };

  const handleUserFinish = () => {
    // Final step – complete setup (navigate to dashboard, etc.)
    console.log("Setup complete. Navigate to dashboard.");
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-semibold mb-4">Organization Setup</h1>
      <Tabs value={activeTab} onValueChange={(val) => setActiveTab(val as TabValue)}>
        <TabsList>
          <TabsTrigger value="organization">Organization</TabsTrigger>
          <TabsTrigger value="departments" disabled={activeTab === "organization"}>
            Departments
          </TabsTrigger>
          <TabsTrigger value="groups" disabled={activeTab === "organization" || activeTab === "departments"}>
            Groups (Optional)
          </TabsTrigger>
          <TabsTrigger value="employment" disabled={activeTab === "organization" || activeTab === "departments" || activeTab === "groups"}>
            Employment Types
          </TabsTrigger>
          <TabsTrigger value="user" disabled={activeTab === "organization" || activeTab === "departments" || activeTab === "groups" || activeTab === "employment"}>
            User
          </TabsTrigger>
        </TabsList>

        <TabsContent value="organization">
          <OrganizationTab onSuccess={handleOrgSuccess} />
        </TabsContent>
        <TabsContent value="departments">
          <DepartmentsTab onNext={handleDepartmentsNext} />
        </TabsContent>
        <TabsContent value="groups">
          <GroupsTab onNext={handleGroupsNext} onSkip={() => setActiveTab("employment")} />
        </TabsContent>
        <TabsContent value="employment">
          <EmploymentTypesTab onNext={handleEmploymentNext} />
        </TabsContent>
        <TabsContent value="user">
          <UserTab onFinish={handleUserFinish} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SetupPage;
