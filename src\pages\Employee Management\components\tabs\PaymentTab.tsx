import { useEffect, useState } from "react";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { EmployeePaymentInfo } from "../../types/EmployeeTypes";
import { updateOrCreatePaymentInfoRecord } from "../../utils/EmployeeRecords";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";

import { BASE_URL } from "@/config";

interface PaymentTabProps {
  employeeNo: string;
  onSaveSuccess?: () => void;
}

// Payment frequency options as per API documentation
const PAYMENT_FREQUENCY_OPTIONS = [
  "Monthly",
  "Weekly",
  "Bi-Weekly",
  "Quarterly",
  "Yearly",
  "Bi-Monthly"
];

export default function PaymentTab({ employeeNo, onSaveSuccess }: PaymentTabProps) {
  const token = useSelector((state: RootState) => state.auth.token);
  const [payment, setPayment] = useState<EmployeePaymentInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const fetchPaymentInfo = async () => {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }

    setLoading(true);
    try {
      const headers = { Authorization: `Token ${token}` };
      const params = { employee_no: employeeNo };
      console.log("Fetching Payment Info for employee:", employeeNo);
      const res = await axios.get(
        `${BASE_URL}/users/employee-payment-info-details`,
        { headers, params }
      );
      console.log("Response from Payment Info API:", res);
      if (Array.isArray(res.data)) {
        const record = res.data.find(
          (item: EmployeePaymentInfo) => item.employee_no === employeeNo
        );
        if (record) {
          // Normalize numeric fields to avoid null values.
          setPayment({
            ...record,
            salary: record.salary != null ? record.salary : 0,
            bonus: record.bonus != null ? record.bonus : 0,
          });
        } else {
          // If no record is found, create a new record with default values.
          setPayment({
            employee_no: employeeNo,
            pension_scheme_join: "",
            salary: 0,
            bonus: 0,
            bank_name: "",
            account_number: "",
            payment_frequency: "",
            KRA_pin: "",
            tax_status: false,
            NHIF_SHIF_number: "",
            HELB_number: "",
            NSSF_number: "",
          });
        }
      }
    } catch (err) {
      console.error("Error fetching Payment Info:", err);
      toast.error("Failed to fetch Payment Info");
      setPayment({
        employee_no: employeeNo,
        pension_scheme_join: "",
        salary: 0,
        bonus: 0,
        bank_name: "",
        account_number: "",
        payment_frequency: "",
        KRA_pin: "",
        tax_status: false,
        NHIF_SHIF_number: "",
        HELB_number: "",
        NSSF_number: "",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPaymentInfo();
  }, [employeeNo, token]);

  async function handleSave() {
    if (!payment || !token || !employeeNo) return;
    setSaving(true);
    try {
      const updated = await updateOrCreatePaymentInfoRecord<EmployeePaymentInfo>(
        `${BASE_URL}/users/employee-payment-info-details`,
        token,
        { ...payment, employee_no: employeeNo }
      );
      if (updated) {
        setPayment(updated);
        toast.success("Payment information saved successfully");
        // Auto-refresh data after save
        await fetchPaymentInfo();
        // Call the onSaveSuccess callback if provided
        if (onSaveSuccess) {
          onSaveSuccess();
        }
      }
    } catch (error) {
      console.error("Error saving Payment Info:", error);
      toast.error("Failed to save Payment Info");
    } finally {
      setSaving(false);
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading Payment Info...</span>
      </div>
    );
  }

  if (!payment) return null;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold">
          Payment Information
        </h3>
        <Button
          onClick={handleSave}
          className="ml-auto"
          disabled={saving}
        >
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Payment Information"
          )}
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {/* Salary Information Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardContent className="pt-6">
            <h4 className="text-lg font-medium mb-4">Salary Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="salary">Salary</Label>
                <Input
                  id="salary"
                  type="number"
                  value={payment.salary != null ? payment.salary.toString() : ""}
                  onChange={(e) =>
                    setPayment((prev) =>
                      prev ? { ...prev, salary: Number(e.target.value) } : prev
                    )
                  }
                  className="focus:border-primary"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bonus">Bonus</Label>
                <Input
                  id="bonus"
                  type="number"
                  value={payment.bonus != null ? payment.bonus.toString() : ""}
                  onChange={(e) =>
                    setPayment((prev) =>
                      prev ? { ...prev, bonus: Number(e.target.value) } : prev
                    )
                  }
                  className="focus:border-primary"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="payment_frequency">Payment Frequency</Label>
                <Select
                  value={payment.payment_frequency || ""}
                  onValueChange={(value) =>
                    setPayment((prev) =>
                      prev ? { ...prev, payment_frequency: value } : prev
                    )
                  }
                >
                  <SelectTrigger id="payment_frequency" className="w-full">
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    {PAYMENT_FREQUENCY_OPTIONS.map((option) => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tax_status">Tax Status</Label>
                <Select
                  value={payment.tax_status ? "true" : "false"}
                  onValueChange={(value) =>
                    setPayment((prev) =>
                      prev ? { ...prev, tax_status: value === "true" } : prev
                    )
                  }
                >
                  <SelectTrigger id="tax_status" className="w-full">
                    <SelectValue placeholder="Select tax status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Yes</SelectItem>
                    <SelectItem value="false">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="pension_scheme_join">Pension Scheme Join Date</Label>
                <Input
                  id="pension_scheme_join"
                  type="date"
                  value={payment.pension_scheme_join}
                  onChange={(e) =>
                    setPayment((prev) =>
                      prev ? { ...prev, pension_scheme_join: e.target.value } : prev
                    )
                  }
                  className="focus:border-primary"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Banking Information Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardContent className="pt-6">
            <h4 className="text-lg font-medium mb-4">Banking Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bank_name">Bank Name</Label>
                <Input
                  id="bank_name"
                  value={payment.bank_name}
                  onChange={(e) =>
                    setPayment((prev) =>
                      prev ? { ...prev, bank_name: e.target.value } : prev
                    )
                  }
                  className="focus:border-primary"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="account_number">Account Number</Label>
                <Input
                  id="account_number"
                  value={payment.account_number}
                  onChange={(e) =>
                    setPayment((prev) =>
                      prev ? { ...prev, account_number: e.target.value } : prev
                    )
                  }
                  className="focus:border-primary"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Government IDs Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardContent className="pt-6">
            <h4 className="text-lg font-medium mb-4">Government IDs</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="kra_pin">KRA PIN</Label>
                <Input
                  id="kra_pin"
                  value={payment.KRA_pin}
                  onChange={(e) =>
                    setPayment((prev) =>
                      prev ? { ...prev, KRA_pin: e.target.value } : prev
                    )
                  }
                  className="focus:border-primary"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nhif_number">NHIF Number</Label>
                <Input
                  id="nhif_number"
                  value={payment.NHIF_SHIF_number}
                  onChange={(e) =>
                    setPayment((prev) =>
                      prev ? { ...prev, NHIF_SHIF_number: e.target.value } : prev
                    )
                  }
                  className="focus:border-primary"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nssf_number">NSSF Number</Label>
                <Input
                  id="nssf_number"
                  value={payment.NSSF_number}
                  onChange={(e) =>
                    setPayment((prev) =>
                      prev ? { ...prev, NSSF_number: e.target.value } : prev
                    )
                  }
                  className="focus:border-primary"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="helb_number">HELB Number</Label>
                <Input
                  id="helb_number"
                  value={payment.HELB_number}
                  onChange={(e) =>
                    setPayment((prev) =>
                      prev ? { ...prev, HELB_number: e.target.value } : prev
                    )
                  }
                  className="focus:border-primary"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
