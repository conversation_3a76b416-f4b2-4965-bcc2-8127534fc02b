"use client";

import React, { use<PERSON>ffe<PERSON>, use<PERSON><PERSON><PERSON>, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useNavigate } from "react-router-dom";

import { BASE_URL } from "@/config";

interface AuthState {
  token: string;
}

import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

import {
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ian<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Label as RechartsLabel,
  Cell,
} from "recharts";

import { ChartContainer, ChartTooltipContent } from "@/components/ui/chart";

// Types
interface Department {
  id: number;
  name: string;
  // ...other fields from your /users/departments response if necessary
}

interface DemographicsData {
  status: string;
  age_distribution: { [age: string]: number };
  gender_ratios: { [gender: string]: number };
  marital_status_counts: { [status: string]: number };
  total_employees: number;
}

interface EmployeeContractInfo {
  id: number;
  employee_no: string;
  contract_type: string;
  contract_start_date: string;
  current_contract_end: string | null;
  end_of_probation_date: string | null;
  on_pip: boolean;
}

// Contract types from API documentation
const CONTRACT_TYPES = [
  "Permanent",
  "Temporary",
  "Internship",
  "Consultant",
  "Contractor",
  "Volunteer",
  "Probation",
  "Management Trainee",
];

const CustomAreaTooltip: React.FC<any> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-2 border border-gray-200 shadow">
        <p className="text-sm font-medium">Age: {label}</p>
        <p className="text-xs">Count: {payload[0].value}</p>
      </div>
    );
  }
 // const { token } = useSelector((state: RootState) => state.auth as AuthState);
};

const EmployeeAnalyticsDashboard: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth as AuthState);
  const navigate = useNavigate();

  // Departments
  const [departments, setDepartments] = useState<Department[]>([]);
  const [departmentsError, setDepartmentsError] = useState<string | null>(null);

  // Contract Info for filtering
  const [contractInfoList, setContractInfoList] = useState<EmployeeContractInfo[]>([]);
  const [contractInfoLoading, setContractInfoLoading] = useState<boolean>(false);

  // Fetch departments and contract info
  useEffect(() => {
    if (!token) return;

    const fetchDepartments = async () => {
      try {
        setDepartmentsError(null);
        const res = await fetch(`${BASE_URL}/users/departments`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!res.ok) {
          throw new Error(`Failed to fetch departments: ${res.status}`);
        }
        const data: Department[] = await res.json();
        setDepartments(data);
      } catch (err: any) {
        setDepartmentsError(err.message || "Failed to fetch departments.");
      }
    };

    const fetchContractInfo = async () => {
      try {
        setContractInfoLoading(true);
        const res = await fetch(`${BASE_URL}/users/employee-contract-info-details`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!res.ok) {
          throw new Error(`Failed to fetch contract info: ${res.status}`);
        }
        const data: EmployeeContractInfo[] = await res.json();
        setContractInfoList(data);
      } catch (err: any) {
        console.error("Failed to fetch contract info:", err);
      } finally {
        setContractInfoLoading(false);
      }
    };

    const fetchAllEmployees = async () => {
      try {
        const res = await fetch(`${BASE_URL}/users/all-employees`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!res.ok) {
          throw new Error(`Failed to fetch employees: ${res.status}`);
        }
        const employees = await res.json();
        setAllEmployees(employees);
      } catch (err: any) {
        console.error("Failed to fetch all employees:", err);
      }
    };

    const fetchEmployeeBioDetails = async () => {
      try {
        const res = await fetch(`${BASE_URL}/users/employee-bio-details`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!res.ok) {
          throw new Error(`Failed to fetch bio details: ${res.status}`);
        }
        const bioDetails = await res.json();
        setEmployeeBioDetails(bioDetails);
      } catch (err: any) {
        console.error("Failed to fetch employee bio details:", err);
      }
    };

    fetchDepartments();
    fetchContractInfo();
    fetchAllEmployees();
    fetchEmployeeBioDetails();
  }, [token]);

  // Filter inputs (now directly applied - no separate applied state needed)
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [selectedMinAge, setSelectedMinAge] = useState<number | undefined>(undefined);
  const [selectedMaxAge, setSelectedMaxAge] = useState<number | undefined>(undefined);
  const [selectedContractType, setSelectedContractType] = useState<string>("all");

  // Local state for age inputs to prevent constant API calls while typing
  const [minAgeInput, setMinAgeInput] = useState<string>("");
  const [maxAgeInput, setMaxAgeInput] = useState<string>("");

  // Debounce age changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSelectedMinAge(minAgeInput === "" ? undefined : Number(minAgeInput));
    }, 500);
    return () => clearTimeout(timeoutId);
  }, [minAgeInput]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSelectedMaxAge(maxAgeInput === "" ? undefined : Number(maxAgeInput));
    }, 500);
    return () => clearTimeout(timeoutId);
  }, [maxAgeInput]);

  // Demographics data
  const [data, setData] = useState<DemographicsData | null>(null);
  const [rawData, setRawData] = useState<DemographicsData | null>(null); // Unfiltered data from API
  const [allEmployees, setAllEmployees] = useState<any[]>([]); // All employee data for filtering
  const [employeeBioDetails, setEmployeeBioDetails] = useState<any[]>([]); // Bio details for demographics
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const buildQueryParams = () => {
    const params = new URLSearchParams();
    if (selectedDepartment !== "all") {
      params.append("department", selectedDepartment);
    }
    if (selectedMinAge !== undefined && String(selectedMinAge) !== "") {
      params.append("min_age", String(selectedMinAge));
    }
    if (selectedMaxAge !== undefined && String(selectedMaxAge) !== "") {
      params.append("max_age", String(selectedMaxAge));
    }

    // Only include active employees
    params.append("is_active", "true");
    return params.toString();
  };

  // Helper function to calculate age from date of birth
  const calculateAge = (dateOfBirth: string): number | null => {
    try {
      const birthDate = new Date(dateOfBirth);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      return age >= 0 ? age : null;
    } catch (error) {
      return null;
    }
  };

  // Apply contract type filtering on client side
  const applyContractTypeFilter = (demographicsData: DemographicsData): DemographicsData => {
    if (selectedContractType === "all" || contractInfoList.length === 0) {
      return demographicsData; // No filtering needed
    }

    // Get employee numbers that match the selected contract type
    const matchingEmployeeNos = contractInfoList
      .filter(contract => contract.contract_type === selectedContractType)
      .map(contract => contract.employee_no);

    if (matchingEmployeeNos.length === 0) {
      // No employees match the contract type, return empty data
      return {
        ...demographicsData,
        age_distribution: {},
        gender_ratios: {},
        marital_status_counts: {},
        total_employees: 0,
      };
    }

    // Filter employees from allEmployees based on contract type
    const filteredEmployees = allEmployees.filter(emp =>
      matchingEmployeeNos.includes(emp.employee_no)
    );

    if (filteredEmployees.length === 0) {
      return {
        ...demographicsData,
        age_distribution: {},
        gender_ratios: {},
        marital_status_counts: {},
        total_employees: 0,
      };
    }

    // Recalculate demographics for filtered employees using bio details
    const ageDistribution: { [age: string]: number } = {};
    const genderRatios: { [gender: string]: number } = {};
    const maritalStatusCounts: { [status: string]: number } = {};

    filteredEmployees.forEach(emp => {
      // Find corresponding bio details for this employee
      const bioDetail = employeeBioDetails.find(bio => bio.employee_no === emp.employee_no);

      // Age distribution (calculate from date_of_birth if available)
      if (bioDetail?.date_of_birth) {
        const age = calculateAge(bioDetail.date_of_birth);
        if (age) {
          ageDistribution[age.toString()] = (ageDistribution[age.toString()] || 0) + 1;
        }
      }

      // Gender ratios (from bio details or employee record)
      const gender = bioDetail?.gender || emp.gender;
      if (gender) {
        genderRatios[gender] = (genderRatios[gender] || 0) + 1;
      }

      // Marital status (from bio details)
      if (bioDetail?.marital_status) {
        maritalStatusCounts[bioDetail.marital_status] = (maritalStatusCounts[bioDetail.marital_status] || 0) + 1;
      }
    });

    return {
      ...demographicsData,
      age_distribution: ageDistribution,
      gender_ratios: genderRatios,
      marital_status_counts: maritalStatusCounts,
      total_employees: filteredEmployees.length,
    };
  };

  // Fetch demographics whenever filters change
  useEffect(() => {
    if (!token) {
      setError("Missing authorization token.");
      return;
    }

    const fetchDemographics = async () => {
      setLoading(true);
      setError(null);
      setData(null);

      const qs = buildQueryParams();
      const endpoint = qs
        ? `${BASE_URL}/analytics/demographics/?${qs}`
        : `${BASE_URL}/analytics/demographics/`;

      try {
        const res = await fetch(endpoint, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        });
        if (!res.ok) {
          const text = await res.text();
          throw new Error(`Request failed with status ${res.status}: ${text}`);
        }
        const json: DemographicsData = await res.json();

        // Store raw data and apply contract type filtering
        setRawData(json);
        const filteredData = applyContractTypeFilter(json);
        setData(filteredData);
      } catch (err: any) {
        setError(err.message || "Failed to fetch demographics data.");
      } finally {
        setLoading(false);
      }
    };

    fetchDemographics();
  }, [token, selectedDepartment, selectedMinAge, selectedMaxAge]);

  // Separate effect for contract type filtering (client-side)
  useEffect(() => {
    if (rawData && contractInfoList.length > 0 && allEmployees.length > 0 && employeeBioDetails.length > 0) {
      const filteredData = applyContractTypeFilter(rawData);
      setData(filteredData);
    }
  }, [selectedContractType, contractInfoList, rawData, allEmployees, employeeBioDetails]);

  // Transform age distribution data into a chart-friendly format
  const ageDistributionData = useMemo(() => {
    if (!data?.age_distribution) return [];
    return Object.entries(data.age_distribution).map(([age, count]) => ({
      age: Number(age),
      count,
    }));
  }, [data]);

  // Prepare gender data
  const genderMapping: Record<string, { label: string; color: string }> = {
    M: { label: "Male", color: "#2196F3" },
    F: { label: "Female", color: "#E91E63" },
    O: { label: "Other", color: "#9E9E9E" },
  };

  const genderData = useMemo(() => {
    if (!data?.gender_ratios) return [];
    return Object.entries(data.gender_ratios).map(([gender, val]) => {
      const key = gender.toUpperCase();
      const mapping = genderMapping[key] || { label: gender, color: "#8884d8" };
      return {
        name: mapping.label,
        value: val,
        color: mapping.color,
      };
    });
  }, [data]);

  // Prepare marital data for pie chart
  const maritalStatusColors = [
    "#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#00ff00", "#ff00ff"
  ];

  const maritalData = useMemo(() => {
    if (!data?.marital_status_counts) return [];
    return Object.entries(data.marital_status_counts).map(([status, count], index) => ({
      name: status,
      value: count,
      color: maritalStatusColors[index % maritalStatusColors.length],
    }));
  }, [data]);

  // Loading and error states
  if (loading || contractInfoLoading) {
    return (
      <Screen>
        <div className="p-4">
          {contractInfoLoading ? "Loading contract information..." : "Loading demographics data..."}
        </div>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <div className="p-4 text-red-500">{error}</div>
      </Screen>
    );
  }

  if (!data) {
    return (
      <Screen>
        <div className="p-4">No demographics data found.</div>
      </Screen>
    );
  }

  return (
    <Screen>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Demographics Analytics</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="p-4 space-y-8">
        {/* Filters Card */}
        <Card>
          <CardHeader>
            <CardTitle>Analytics Filters</CardTitle>
            <CardDescription>Filter analytics by department, contract type, and age range. Changes apply automatically.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Department select */}
              <div className="flex flex-col space-y-2">
                <Label htmlFor="department-filter" className="text-sm font-medium">Department</Label>
                <Select
                  value={selectedDepartment}
                  onValueChange={(val: string) => setSelectedDepartment(val)}
                >
                  <SelectTrigger id="department-filter" className="h-10">
                    <SelectValue placeholder="All Departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={String(dept.id)}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {departmentsError && (
                  <p className="text-xs text-red-500 mt-1">{departmentsError}</p>
                )}
              </div>

              {/* Contract Type select */}
              <div className="flex flex-col space-y-2">
                <Label htmlFor="contract-type-filter" className="text-sm font-medium">Contract Type</Label>
                <Select
                  value={selectedContractType}
                  onValueChange={(val: string) => setSelectedContractType(val)}
                >
                  <SelectTrigger id="contract-type-filter" className="h-10">
                    <SelectValue placeholder="All Contract Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Contract Types</SelectItem>
                    {CONTRACT_TYPES.map((contractType) => (
                      <SelectItem key={contractType} value={contractType}>
                        {contractType}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Min Age */}
              <div className="flex flex-col space-y-2">
                <Label htmlFor="min-age-filter" className="text-sm font-medium">Min Age</Label>
                <Input
                  id="min-age-filter"
                  type="number"
                  placeholder="e.g. 25"
                  className="h-10"
                  value={minAgeInput}
                  onChange={(e) => setMinAgeInput(e.target.value)}
                />
              </div>

              {/* Max Age */}
              <div className="flex flex-col space-y-2">
                <Label htmlFor="max-age-filter" className="text-sm font-medium">Max Age</Label>
                <Input
                  id="max-age-filter"
                  type="number"
                  placeholder="e.g. 60"
                  className="h-10"
                  value={maxAgeInput}
                  onChange={(e) => setMaxAgeInput(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Employees */}
        <Card>
          <CardHeader>
            <CardTitle>Total Employees</CardTitle>
            <CardDescription>Overall headcount</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{data.total_employees}</p>
          </CardContent>
        </Card>

        {/* Age Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Age Distribution</CardTitle>
            <CardDescription>Employee counts by age</CardDescription>
          </CardHeader>
          <CardContent>
            {ageDistributionData.length === 0 ? (
              <p>No age distribution data available.</p>
            ) : (
              <ChartContainer config={{}} className="w-full h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={ageDistributionData}>
                    <defs>
                      <linearGradient id="ageColor" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopOpacity={0.8} />
                        <stop offset="95%" stopOpacity={0.1} />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="age" />
                    <YAxis />
                    <Tooltip cursor={false} content={<CustomAreaTooltip />} />
                    <Area
                      type="monotone"
                      dataKey="count"
                      stroke="var(--color-desktop, #8884d8)"
                      fill="url(#ageColor)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </ChartContainer>
            )}
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Gender Ratios */}
          <Card>
            <CardHeader>
              <CardTitle>Gender Ratios</CardTitle>
              <CardDescription>Female vs. Male vs. Other</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              {genderData.length === 0 ? (
                <p>No gender ratio data available.</p>
              ) : (
                <ChartContainer config={{}} className="w-full max-h-[300px]">
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Tooltip
                        cursor={false}
                        content={<ChartTooltipContent hideLabel />}
                      />
                      <Pie
                        data={genderData}
                        dataKey="value"
                        nameKey="name"
                        innerRadius={60}
                        outerRadius={100}
                        strokeWidth={5}
                      >
                        <RechartsLabel
                          content={({ viewBox }) => {
                            if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                              const total = genderData.reduce(
                                (acc, curr) => acc + curr.value,
                                0
                              );
                              return (
                                <text
                                  x={viewBox.cx}
                                  y={viewBox.cy}
                                  textAnchor="middle"
                                  dominantBaseline="middle"
                                >
                                  <tspan className="fill-foreground text-2xl font-bold">
                                    {total}
                                  </tspan>
                                  <tspan
                                    x={viewBox.cx}
                                    y={(viewBox.cy || 0) + 20}
                                    className="fill-muted-foreground text-sm"
                                  >
                                    Employees
                                  </tspan>
                                </text>
                              );
                            }
                            return null;
                          }}
                        />
                        {genderData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              )}
            </CardContent>
          </Card>

          {/* Marital Status */}
          <Card>
            <CardHeader>
              <CardTitle>Marital Status</CardTitle>
              <CardDescription>Distribution by marital status</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              {maritalData.length === 0 ? (
                <p>No marital status data available.</p>
              ) : (
                <ChartContainer config={{}} className="w-full max-h-[300px]">
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Tooltip
                        cursor={false}
                        content={<ChartTooltipContent hideLabel />}
                      />
                      <Pie
                        data={maritalData}
                        dataKey="value"
                        nameKey="name"
                        innerRadius={60}
                        outerRadius={100}
                        strokeWidth={5}
                      >
                        <RechartsLabel
                          content={({ viewBox }) => {
                            if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                              const total = maritalData.reduce(
                                (acc, curr) => acc + curr.value,
                                0
                              );
                              return (
                                <text
                                  x={viewBox.cx}
                                  y={viewBox.cy}
                                  textAnchor="middle"
                                  dominantBaseline="middle"
                                >
                                  <tspan className="fill-foreground text-2xl font-bold">
                                    {total}
                                  </tspan>
                                  <tspan
                                    x={viewBox.cx}
                                    y={(viewBox.cy || 0) + 20}
                                    className="fill-muted-foreground text-sm"
                                  >
                                    Total
                                  </tspan>
                                </text>
                              );
                            }
                            return null;
                          }}
                        />
                        {maritalData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              )}
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={() => navigate("/employees-list")} variant="link">
                View Employees
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default EmployeeAnalyticsDashboard;
