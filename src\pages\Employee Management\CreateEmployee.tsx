import { Screen } from "@/app-components/layout/screen";
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { BASE_URL } from "@/config";
import {
  Dependant,
  EmployeeEducationalQualification,
  NextOfKin,
} from "./types/EmployeeTypes";
import axios from "axios";
import { EmployeeCreateData } from "./EmployeeCreation";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import toast from "react-hot-toast";
import {
  Book,
  Briefcase,
  Calendar,
  CloudUpload,
  CreditCard,
  Download,
  FileText,
  Info,
  Phone,
  Trash2,
  Upload,
  User,
  Users,
} from "lucide-react";
import { Alert } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { Input } from "@/components/ui/input";
import { Label } from "@radix-ui/react-label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import triangle from "../../assets/triangle.svg";
import * as XLSX from "xlsx"; // <-- XLSX library

type Props = {};

const steps = [
  "User Registration", // 0
  "Bio Details", // 1
  "Contact Information", // 2
  "Contract Information", // 3
  "Important Dates", // 4
  "Job Information", // 5
  "Payment Information", // 6
  "Educational Qualifications", // 7
  "Next of Kin & Dependants", // 8
];

const CreateEmployee = ({}: Props) => {
  const navigate = useNavigate();
  const token = useSelector((state: RootState) => state.auth.token);

  // ----------- NEW STATE for Single vs. Bulk ------------
  const [mode, setMode] = useState<"single" | "bulk">("single");
  // -------------------------------------------------------
  // ---------- BULK UPLOAD STATE -----------
  const [bulkStep, setBulkStep] = useState<0 | 1 | 2>(0); // 0=Upload, 1=Preview, 2=Finished
  const [bulkFile, setBulkFile] = useState<File | null>(null);
  const [bulkData, setBulkData] = useState<any[]>([]); // parsed Excel rows
  const [bulkError, setBulkError] = useState<string | null>(null);
  const [bulkLoading, setBulkLoading] = useState(false);
  // ----------------------------------------

  const [activeStep, setActiveStep] = useState(0);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [employeeId, setEmployeeId] = useState<number | null>(null);
  const [finished, setFinished] = useState(false);

  // Dropdown data
  const [organizations, setOrganizations] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [employeeGroups, setEmployeeGroups] = useState<any[]>([]);

  // Master form data
  const [formData, setFormData] = useState<EmployeeCreateData>({
    userRegistration: {
      email: "",
      password: "********",
      organisation_id: "",
      department_id: "",
      group_id: "",
    },
    bio: {
      first_name: "",
      middle_name: "",
      last_name: "",
    },
    contact: {},
    contractInfo: {
      contract_type: "",
      contract_start_date: "",
    },
    importantDates: {
      date_of_current_appointment: "",
    },
    jobInfo: {
      work_location: "",
      business_unit: "",
    },
    paymentInfo: {
      pension_scheme_join: "",
      salary: 0,
      bonus: 0,
      bank_name: "",
      account_number: "",
      payment_frequency: "",
      KRA_pin: "",
      tax_status: false,
      NHIF_SHIF_number: "",
      HELB_number: "",
      NSSF_number: "",
    },
    educationalQualifications: [
      {
        highear_qualificatins: "",
        instution: "",
        year_of_graduation: "",
      },
    ],
    nextOfKin: {
      next_of_kin: [{ name: "", relationship: "", contact_info: "" }],
      dependants: [{ name: "", relationship: "", contact_info: "" }],
    },
  });

  // Fetch dropdown data once token is available
  useEffect(() => {
    const fetchDropdownData = async () => {
      try {
        const [orgRes, depRes, groupRes] = await Promise.all([
          axios.get(`${BASE_URL}/users/organization`, {
            headers: { Authorization: `Token ${token}` },
          }),
          axios.get(`${BASE_URL}/users/departments`, {
            headers: { Authorization: `Token ${token}` },
          }),
          axios.get(`${BASE_URL}/users/organization_groups`, {
            headers: { Authorization: `Token ${token}` },
          }),
        ]);
        setOrganizations(orgRes.data);
        setDepartments(depRes.data);
        setEmployeeGroups(groupRes.data);
      } catch (err) {
        console.error("Error fetching dropdown data:", err);
      }
    };
    if (token) fetchDropdownData();
  }, [token]);

  const handleChange = (
    section: keyof EmployeeCreateData,
    field: string,
    value: string | number | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [section]: { ...prev[section], [field]: value },
    }));
  };

  // Clean payload helper
  const cleanPayload = (payload: any) => {
    const cleaned: any = {};
    Object.keys(payload).forEach((key) => {
      const value = payload[key];
      if (
        value !== "" &&
        value !== null &&
        value !== undefined &&
        !(typeof value === "string" && value.trim() === "")
      ) {
        cleaned[key] = value;
      }
    });
    return cleaned;
  };

  const handleNext = async () => {
    setError(null);
    setSubmitting(true);
    try {
      let endpoint = "";
      let payload: any = {};

      switch (activeStep) {
        case 0:
          // USER REGISTRATION
          endpoint = "/users/registration";
          payload = {
            user: {
              email: formData.userRegistration.email,
              password: formData.userRegistration.password,
              organisation_id: formData.userRegistration.organisation_id,
              department_id: formData.userRegistration.department_id,
              group_id: formData.userRegistration.group_id,
            },
          };
          break;
        case 1:
          // BIO DETAILS
          endpoint = "/users/employee-bio-details";
          if (!employeeId)
            throw new Error(
              "No employee ID found. Complete registration first."
            );
          payload = { ...formData.bio, employee_no: employeeId };
          if (payload.gender) {
            payload.gender = payload.gender.charAt(0).toUpperCase();
          }
          break;
        case 2:
          // CONTACT INFORMATION
          endpoint = "/users/employee-contact-info-details";
          if (!employeeId)
            throw new Error(
              "No employee ID found. Complete registration first."
            );
          payload = { ...formData.contact, employee_no: employeeId };
          break;
        case 3:
          // CONTRACT INFORMATION
          endpoint = "/users/employee-contract-info-details";
          if (!employeeId)
            throw new Error(
              "No employee ID found. Complete registration first."
            );
          payload = { ...formData.contractInfo, employee_no: employeeId };
          break;
        case 4:
          // IMPORTANT DATES
          endpoint = "/users/employee-important-dates-details";
          if (!employeeId)
            throw new Error(
              "No employee ID found. Complete registration first."
            );
          payload = { ...formData.importantDates, employee_no: employeeId };
          break;
        case 5:
          // JOB INFORMATION
          endpoint = "/users/employee-job-info-details";
          if (!employeeId)
            throw new Error(
              "No employee ID found. Complete registration first."
            );
          payload = { ...formData.jobInfo, employee_no: employeeId };
          break;
        case 6:
          // PAYMENT INFORMATION
          endpoint = "/users/employee-payment-info-details";
          if (!employeeId)
            throw new Error(
              "No employee ID found. Complete registration first."
            );
          payload = { ...formData.paymentInfo, employee_no: employeeId };
          break;
        case 7:
          // EDUCATIONAL QUALIFICATIONS
          endpoint = "/users/employee-education-qualifications";
          if (!employeeId)
            throw new Error(
              "No employee ID found. Complete registration first."
            );

          for (const [
            index,
            q,
          ] of formData.educationalQualifications.entries()) {
            const qualificationPayload = cleanPayload({
              highear_qualificatins: q.highear_qualificatins || null,
              instution: q.instution,
              year_of_graduation: q.year_of_graduation,
              employee_no: employeeId.toString(),
            });
            console.log(
              `Submitting Qualification ${index + 1}:`,
              qualificationPayload
            );
            try {
              await axios.post(`${BASE_URL}${endpoint}`, qualificationPayload, {
                headers: {
                  Authorization: `Token ${token}`,
                  "Content-Type": "application/json",
                },
              });
            } catch (err: any) {
              console.error(
                `Error adding Qualification ${index + 1}:`,
                err.response?.data || err.message
              );
              throw new Error(`Failed to submit Qualification ${index + 1}.`);
            }
          }
          break;
        case 8:
          // NEXT OF KIN & DEPENDANTS
          endpoint = "/users/employee-next-of-kin";
          if (!employeeId)
            throw new Error(
              "No employee ID found. Complete registration first."
            );
          payload = {
            employee_no: employeeId.toString(),
            next_of_kin: formData.nextOfKin.next_of_kin.map((kin) => ({
              name: kin.name,
              relationship: kin.relationship,
              contact_info: kin.contact_info,
            })),
            dependants: formData.nextOfKin.dependants.map((dep) => ({
              name: dep.name,
              relationship: dep.relationship,
              contact_info: dep.contact_info,
            })),
          };
          formData.nextOfKin.next_of_kin.forEach((kin, index) => {
            if (
              !kin.name.trim() ||
              !kin.relationship.trim() ||
              !kin.contact_info.trim()
            ) {
              throw new Error(
                `Next of Kin ${index + 1}: All fields are required.`
              );
            }
          });
          formData.nextOfKin.dependants.forEach((dep, index) => {
            if (
              !dep.name.trim() ||
              !dep.relationship.trim() ||
              !dep.contact_info.trim()
            ) {
              throw new Error(
                `Dependant ${index + 1}: All fields are required.`
              );
            }
          });
          break;
        default:
          break;
      }

      if (activeStep !== 7 && activeStep !== 8) {
        payload = cleanPayload(payload);
        const response = await axios.post(`${BASE_URL}${endpoint}`, payload, {
          headers: {
            Authorization: `Token ${token}`,
            "Content-Type": "application/json",
          },
        });
        if (activeStep === 0) {
          const empNo = response.data.employee_no || response.data.employee_id;
          if (empNo) setEmployeeId(empNo);
        }
      } else if (activeStep === 8) {
        payload = cleanPayload(payload);
        await axios.post(`${BASE_URL}${endpoint}`, payload, {
          headers: {
            Authorization: `Token ${token}`,
            "Content-Type": "application/json",
          },
        });
      }

      // If we are on the last step, finish the flow
      if (activeStep === steps.length - 1) {
        toast.success("Employee created successfully!");
        setFinished(true);
      } else {
        setActiveStep((prev) => prev + 1);
      }
    } catch (err: any) {
      console.error(
        `Error in step ${activeStep + 1}:`,
        err.response?.data || err.message
      );
      setError(`Failed to submit ${steps[activeStep]}.`);
    } finally {
      setSubmitting(false);
    }
  };

  const handleBack = () => {
    setError(null);
    setActiveStep((prev) => prev - 1);
  };

  const handleCancel = () => {
    navigate("/employees");
  };

  const addQualification = () => {
    setFormData((prev) => ({
      ...prev,
      educationalQualifications: [
        ...prev.educationalQualifications,
        { highear_qualificatins: "", instution: "", year_of_graduation: "" },
      ],
    }));
  };

  const removeQualification = (index: number) => {
    setFormData((prev) => {
      const quals = [...prev.educationalQualifications];
      quals.splice(index, 1);
      return { ...prev, educationalQualifications: quals };
    });
  };

  const handleQualificationChange = (
    index: number,
    field: keyof EmployeeEducationalQualification,
    value: string
  ) => {
    setFormData((prev) => {
      const quals = [...prev.educationalQualifications];
      quals[index] = { ...quals[index], [field]: value };
      return { ...prev, educationalQualifications: quals };
    });
  };

  const addNextOfKin = () => {
    setFormData((prev) => ({
      ...prev,
      nextOfKin: {
        ...prev.nextOfKin,
        next_of_kin: [
          ...prev.nextOfKin.next_of_kin,
          { name: "", relationship: "", contact_info: "" },
        ],
      },
    }));
  };

  const removeNextOfKin = (index: number) => {
    setFormData((prev) => {
      const kin = [...prev.nextOfKin.next_of_kin];
      kin.splice(index, 1);
      return { ...prev, nextOfKin: { ...prev.nextOfKin, next_of_kin: kin } };
    });
  };

  const handleNextOfKinChange = (
    index: number,
    field: keyof NextOfKin,
    value: string
  ) => {
    setFormData((prev) => {
      const kin = [...prev.nextOfKin.next_of_kin];
      kin[index] = { ...kin[index], [field]: value };
      return { ...prev, nextOfKin: { ...prev.nextOfKin, next_of_kin: kin } };
    });
  };

  const addDependant = () => {
    setFormData((prev) => ({
      ...prev,
      nextOfKin: {
        ...prev.nextOfKin,
        dependants: [
          ...prev.nextOfKin.dependants,
          { name: "", relationship: "", contact_info: "" },
        ],
      },
    }));
  };

  const removeDependant = (index: number) => {
    setFormData((prev) => {
      const deps = [...prev.nextOfKin.dependants];
      deps.splice(index, 1);
      return { ...prev, nextOfKin: { ...prev.nextOfKin, dependants: deps } };
    });
  };

  const handleDependantChange = (
    index: number,
    field: keyof Dependant,
    value: string
  ) => {
    setFormData((prev) => {
      const deps = [...prev.nextOfKin.dependants];
      deps[index] = { ...deps[index], [field]: value };
      return { ...prev, nextOfKin: { ...prev.nextOfKin, dependants: deps } };
    });
  };

  // ---------- BULK UPLOAD FUNCTIONS ---------
  const [progress, setProgress] = useState<number>(0);
  const [tableHeaders, setTableHeaders] = useState<any>();
  const [previewURL, setPreviewURL] = useState<string | null>(null);

  const handleBulkFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setBulkFile(event.target.files[0]);
      setProgress(0);
      handleBulkFileUpload(event.target.files[0]);
    }
  };

  // Step 1: parse the XLSX file

  const handleBulkFileUpload = (file: any) => {
    if (!file) return;
    setBulkError(null);

    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
      setBulkError("Please upload an Excel file (.xlsx or .xls).");
      return;
    }

    const reader = new FileReader();

    reader.onprogress = (event) => {
      if (event.lengthComputable) {
        const percentLoaded = Math.round((event.loaded * 100) / event.total);
        setProgress(percentLoaded);
      }
    };

    reader.onload = (e) => {
      if (!e.target?.result) {
        setBulkError("Could not read the file.");
        return;
      }
      try {
        const data = new Uint8Array(e.target.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        let jsonData: any[] = XLSX.utils.sheet_to_json(worksheet, {
          header: 0,
        });

        if (!jsonData || jsonData.length === 0) {
          setBulkError("No rows found in the uploaded file.");
          return;
        }
        const tbHeader = Object.keys(jsonData[0]);
        setTableHeaders(tbHeader);

        // ----------- Skip the first row (row #1) -----------
        // If row #1 is your header, you can remove it by shifting:
        // This removes the 0th element from the array
        // jsonData.shift();
        console.log("jsonData", jsonData);

        setPreviewURL(reader.result as string);
        setProgress(100);

        setBulkData(jsonData);
        setBulkStep(1);
      } catch (err: any) {
        setBulkError(`Failed to parse Excel file: ${err.message}`);
      }
    };
    reader.onerror = () => {
      setBulkError("Error reading file.");
    };
    reader.readAsArrayBuffer(file);
  };

  // Step 2: confirm + bulk create
  const handleConfirmBulkUpload = async () => {
    if (!bulkData || bulkData.length === 0) return;
    setBulkLoading(true);
    setBulkError(null);

    try {
      for (let i = 0; i < bulkData.length; i++) {
        const row = bulkData[i];

        // (1) Gather user email & default password
        // We'll assume your Excel file has a column named exactly "work email address".
        // If it is named differently, update below to match your real header:
        const userEmail = (row["Work Email Address"] || "").trim();
        if (!userEmail) {
          throw new Error(
            `Row #${
              i + 1
            } is missing 'work email address' - cannot create user.`
          );
        }
        const userPassword = "********";

        // 2. Register user
        const registrationPayload = {
          user: {
            email: userEmail,
            password: userPassword,
            organisation_id: row.organisation_id
              ? Number(row.organisation_id)
              : null,
            department_id: row.department_id ? Number(row.department_id) : null,
            group_id: row.group_id ? Number(row.group_id) : null,
          },
        };
        const regRes = await axios.post(
          `${BASE_URL}/users/registration`,
          registrationPayload,
          {
            headers: {
              Authorization: `Token ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        const employeeNo = regRes.data.employee_no || regRes.data.employee_id;
        if (!employeeNo) {
          throw new Error(
            `Missing employee_no from registration for row #${i + 1}`
          );
        }

        // 3. Bio
        const bioPayload = cleanPayload({
          first_name: row.first_name,
          middle_name: row.middle_name,
          last_name: row.last_name,
          gender: row.gender,
          marital_status: row.marital_status,
          date_of_birth: row.date_of_birth,
          passport_number: row.passport_number,
          id_number: row.id_number,
          date_of_first_appointment: row.date_of_first_appointment,
          year_in_employment: row.year_in_employment
            ? Number(row.year_in_employment)
            : null,
          employee_no: employeeNo,
        });
        if (Object.keys(bioPayload).length > 1) {
          await axios.post(
            `${BASE_URL}/users/employee-bio-details`,
            bioPayload,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );
        }

        // 4. Contact Info
        const contactPayload = cleanPayload({
          postal_address: row.postal_address,
          home_phone_number: row.home_phone_number,
          residential_address: row.residential_address,
          city: row.city,
          county: row.county,
          company_email: userEmail,
          country: row.country,
          personal_email: row.personal_email,
          employee_no: employeeNo,
        });
        if (Object.keys(contactPayload).length > 1) {
          await axios.post(
            `${BASE_URL}/users/employee-contact-info-details`,
            contactPayload,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );
        }

        // 5. Contract Info
        const contractPayload = cleanPayload({
          contract_type: row.contract_type,
          contract_start_date: row.contract_start_date,
          current_contract_end: row.current_contract_end,
          end_of_probation_date: row.end_of_probation_date,
          on_probation:
            row.on_probation === "true" || row.on_probation === true,
          employee_no: employeeNo,
        });
        if (Object.keys(contractPayload).length > 2) {
          await axios.post(
            `${BASE_URL}/users/employee-contract-info-details`,
            contractPayload,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );
        }

        // 6. Important Dates
        const datesPayload = cleanPayload({
          date_of_current_appointment: row.date_of_current_appointment,
          date_of_leaveing: row.date_of_leaveing || null,
          employee_no: employeeNo,
        });
        if (Object.keys(datesPayload).length > 2) {
          await axios.post(
            `${BASE_URL}/users/employee-important-dates-details`,
            datesPayload,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );
        }

        // 7. Job Info
        const jobInfoPayload = cleanPayload({
          category: row.category,
          directorate: row.directorate,
          job_title_code: row.job_title_code,
          job_title: row.job_title,
          teams_code: row.teams_code,
          work_location: row.work_location,
          business_unit: row.business_unit,
          department: row.department ? Number(row.department) : null,
          employee_no: employeeNo,
        });
        if (Object.keys(jobInfoPayload).length > 2) {
          await axios.post(
            `${BASE_URL}/users/employee-job-info-details`,
            jobInfoPayload,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );
        }

        // 8. Payment Info
        const paymentInfoPayload = cleanPayload({
          pension_scheme_join: row.pension_scheme_join,
          salary: row.salary ? Number(row.salary) : 0,
          bonus: row.bonus ? Number(row.bonus) : 0,
          tax_status: row.tax_status === "true" || row.tax_status === true,
          bank_name: row.bank_name,
          account_number: row.account_number,
          payment_frequency: row.payment_frequency,
          KRA_pin: row.KRA_pin,
          NHIF_SHIF_number: row.NHIF_SHIF_number,
          NSSF_number: row.NSSF_number,
          HELB_number: row.HELB_number,
          employee_no: employeeNo,
        });
        if (Object.keys(paymentInfoPayload).length > 3) {
          await axios.post(
            `${BASE_URL}/users/employee-payment-info-details`,
            paymentInfoPayload,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );
        }

        // 9. Education
        if (row.instution && row.year_of_graduation) {
          const eduPayload = cleanPayload({
            highear_qualificatins: row.highear_qualificatins,
            instution: row.instution,
            year_of_graduation: row.year_of_graduation,
            employee_no: employeeNo,
          });
          await axios.post(
            `${BASE_URL}/users/employee-education-qualifications`,
            eduPayload,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );
        }

        // 10. Next of kin
        if (row.kin_name || row.dep_name) {
          const nextOfKinPayload = {
            employee_no: employeeNo,
            next_of_kin: row.kin_name
              ? [
                  {
                    name: row.kin_name,
                    relationship: row.kin_relationship,
                    contact_info: row.kin_contact_info,
                  },
                ]
              : [],
            dependants: row.dep_name
              ? [
                  {
                    name: row.dep_name,
                    relationship: row.dep_relationship,
                    contact_info: row.dep_contact_info,
                  },
                ]
              : [],
          };
          const finalKinPayload = cleanPayload(nextOfKinPayload);
          await axios.post(
            `${BASE_URL}/users/employee-next-of-kin`,
            finalKinPayload,
            {
              headers: { Authorization: `Token ${token}` },
            }
          );
        }

        console.log(`Created employee for row #${i + 1} => ${userEmail}`);
      }

      // Done
      toast.success("Bulk upload completed successfully!");
      setBulkStep(2);
    } catch (err: any) {
      console.error("Bulk upload error:", err.response?.data || err.message);
      setBulkError(`Bulk upload failed: ${err.message}`);
    } finally {
      setBulkLoading(false);
    }
  };

  // const readFileWithProgress = (file: File) => {
  //   const reader = new FileReader();

  //   reader.onprogress = (event) => {
  //     if (event.lengthComputable) {
  //       const percentLoaded = Math.round((event.loaded * 100) / event.total);
  //       setProgress(percentLoaded);
  //     }
  //   };

  //   reader.onload = () => {
  //     setPreviewURL(reader.result as string);
  //     setProgress(100); // Mark as fully loaded
  //   };

  //   reader.readAsDataURL(file);
  // };

  const tabItems = [
    { label: "User Registration", icon: <User size={16} className="mr-1" /> },
    { label: "Bio Details", icon: <Info size={16} className="mr-1" /> },
    { label: "Contact Info", icon: <Phone size={16} className="mr-1" /> },
    { label: "Contract Info", icon: <FileText size={16} className="mr-1" /> },
    { label: "Important Dates", icon: <Calendar size={16} className="mr-1" /> },
    { label: "Job Info", icon: <Briefcase size={16} className="mr-1" /> },
    { label: "Payment Info", icon: <CreditCard size={16} className="mr-1" /> },
    { label: "Education", icon: <Book size={16} className="mr-1" /> },
    { label: "Next of Kin", icon: <Users size={16} className="mr-1" /> },
  ];

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Employee Creation
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
  return (
    <Screen headerContent={breadcrumb}>
      <div className="container mx-auto p-6">
        <h1 className="text-3xl font-bold mb-4">Create New Employee</h1>

        {/* Toggle for Single / Bulk */}
        <div className="flex items-center space-x-4 mb-6">
          <Button
            variant={mode === "single" ? "default" : "outline"}
            onClick={() => setMode("single")}
          >
            Single
          </Button>
          <Button
            variant={mode === "bulk" ? "default" : "outline"}
            onClick={() => setMode("bulk")}
          >
            Bulk Upload <Upload className="ml-1" size={16} />
          </Button>

          {/* "Download Template" Button */}
          {mode === "bulk" && (
            <a
              href="/HRMIS_EMPLOYEE_DATA.xlsx"
              download
              className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md"
            >
              Download Template
              <Download className="ml-1" size={16} />
            </a>
          )}
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4 flex flex-wrap">
            <>{error}</>
          </Alert>
        )}

        {bulkFile && mode == "bulk" && bulkStep != 0 && (
          <div className="mt-4">
            <p className="text-sm text-gray-700">Uploading: {bulkFile.name}</p>

            {/* Progress Bar */}
            <div className="mt-2 w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-green-600 h-2.5 rounded-full transition-all"
                style={{ width: `${progress}%` }}
              ></div>
            </div>

            <p className="text-sm mt-2 text-gray-700">{progress}%</p>
          </div>
        )}

        {mode == "single" && (
          <>
            {!finished && (
              <div className=" overflow-x-auto">
                <div className="flex  space-x-0">
                  {tabItems.map((tab, index) => (
                    <div
                      key={index}
                      className={`relative flex items-center space-x-0 px-4 py-2 flex-shrink-0 cursor-pointer ${
                        index === activeStep
                          ? "font-bold bg-green-600 text-white border-t-2 border-x-2 rounded-t-md border-t-green-600 border-x-green-600"
                          : "text-gray-500  shadow"
                      }`}
                    >
                      {tab.icon}
                      <span className="hidden md:inline">{tab.label}</span>
                      {index === activeStep && (
                        <img
                          src={triangle}
                          className="h-5 w-5 absolute -bottom-[10px] -left-[10px] !z-20"
                        />
                      )}
                      {index === activeStep && (
                        <img
                          src={triangle}
                          className="h-5 w-5 absolute -bottom-[10px] -right-[10px] !z-20"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {!finished ? (
              <form className="space-y-6 border rounded border-green-600 p-8 shadow min-h-[400px]">
                {activeStep === 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2 col-span-1">
                      <Label className="text-sm" htmlFor="email">
                        Email <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        className="focus:!ring-0 "
                        autoComplete="false"
                        placeholder="Enter email"
                        value={formData.userRegistration.email}
                        onChange={(e) =>
                          handleChange(
                            "userRegistration",
                            "email",
                            e.target.value
                          )
                        }
                        required
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="organisation">
                        Organization <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={
                          formData.userRegistration.organisation_id === ""
                            ? "0"
                            : formData.userRegistration.organisation_id.toString()
                        }
                        onValueChange={(value) =>
                          handleChange(
                            "userRegistration",
                            "organisation_id",
                            Number(value)
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Organization" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Organization</SelectLabel>
                            <SelectItem value="0">None</SelectItem>
                            {organizations.map((org) => (
                              <SelectItem
                                key={org.id}
                                value={org.id.toString()}
                              >
                                {org.name}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="department">
                        Department <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={
                          formData.userRegistration.department_id === ""
                            ? "0"
                            : formData.userRegistration.department_id.toString()
                        }
                        onValueChange={(value) =>
                          handleChange(
                            "userRegistration",
                            "department_id",
                            Number(value)
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Department" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Department</SelectLabel>
                            <SelectItem value="0">None</SelectItem>
                            {departments.map((dep) => (
                              <SelectItem
                                key={dep.id}
                                value={dep.id.toString()}
                              >
                                {dep.name}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="group">
                        Group <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={
                          formData.userRegistration.group_id === ""
                            ? "0"
                            : formData.userRegistration.group_id.toString()
                        }
                        onValueChange={(value) =>
                          handleChange(
                            "userRegistration",
                            "group_id",
                            Number(value)
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Group" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Group</SelectLabel>
                            <SelectItem value="0">None</SelectItem>
                            {employeeGroups.map((g) => (
                              <SelectItem key={g.id} value={g.id.toString()}>
                                {g.name || `Group ${g.id}`}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {activeStep === 1 && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label className="text-sm" htmlFor="first_name">
                        First Name <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="first_name"
                        className="focus:!ring-0  "
                        placeholder="First Name"
                        value={formData.bio.first_name}
                        onChange={(e) =>
                          handleChange("bio", "first_name", e.target.value)
                        }
                        required
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="middle_name">
                        Middle Name <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="middle_name"
                        className="focus:!ring-0  "
                        placeholder="Middle Name"
                        value={formData.bio.middle_name}
                        onChange={(e) =>
                          handleChange("bio", "middle_name", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="last_name">
                        Last Name <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="last_name"
                        className="focus:!ring-0  "
                        placeholder="Last Name"
                        value={formData.bio.last_name}
                        onChange={(e) =>
                          handleChange("bio", "last_name", e.target.value)
                        }
                        required
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="gender">
                        Gender
                      </Label>
                      <Select
                        value={formData.bio.gender || "none"}
                        onValueChange={(value) =>
                          handleChange(
                            "bio",
                            "gender",
                            value === "none" ? "" : value
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Gender" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Gender</SelectLabel>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="Male">Male</SelectItem>
                            <SelectItem value="Female">Female</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="marital_status">
                        Marital Status
                      </Label>
                      <Select
                        value={formData.bio.marital_status || "none"}
                        onValueChange={(value) =>
                          handleChange(
                            "bio",
                            "marital_status",
                            value === "none" ? "" : value
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Marital Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Marital Status</SelectLabel>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="Single">Single</SelectItem>
                            <SelectItem value="Married">Married</SelectItem>
                            <SelectItem value="Divorced">Divorced</SelectItem>
                            <SelectItem value="Widowed">Widowed</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="date_of_birth">
                        Date of Birth
                      </Label>
                      <Input
                        id="date_of_birth"
                        className="focus:!ring-0  "
                        type="date"
                        value={formData.bio.date_of_birth || ""}
                        onChange={(e) =>
                          handleChange("bio", "date_of_birth", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="passport_number">
                        Passport / ID Number
                      </Label>
                      <Input
                        id="passport_number"
                        className="focus:!ring-0 "
                        placeholder="Enter passport or ID"
                        value={formData.bio.passport_number || ""}
                        onChange={(e) =>
                          handleChange("bio", "passport_number", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label
                        className="text-sm"
                        htmlFor="date_of_first_appointment"
                      >
                        Date of First Appointment
                      </Label>
                      <Input
                        id="date_of_first_appointment"
                        className="focus:!ring-0 "
                        type="date"
                        value={formData.bio.date_of_first_appointment || ""}
                        onChange={(e) =>
                          handleChange(
                            "bio",
                            "date_of_first_appointment",
                            e.target.value
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="id_number">
                        ID Number
                      </Label>
                      <Input
                        id="id_number"
                        placeholder="Enter ID number"
                        value={formData.bio.id_number || ""}
                        onChange={(e) =>
                          handleChange("bio", "id_number", e.target.value)
                        }
                      />
                    </div>
                  </div>
                )}

                {activeStep === 2 && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <p className="text-green-500">Contacts</p>
                    <div className="md:col-span-3 col-span grid grid-cols-1 md:grid-cols-3 gap-4 shadow p-3">
                      <div>
                        <Label className="text-sm" htmlFor="company_email">
                          Company Email
                        </Label>
                        <Input
                          id="company_email"
                          type="email"
                          placeholder="Enter company email"
                          value={formData.contact.company_email || ""}
                          onChange={(e) =>
                            handleChange(
                              "contact",
                              "company_email",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="personal_email">
                          Personal Email
                        </Label>
                        <Input
                          id="personal_email"
                          type="email"
                          placeholder="Enter personal email"
                          value={formData.contact.personal_email || ""}
                          onChange={(e) =>
                            handleChange(
                              "contact",
                              "personal_email",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="home_phone_number">
                          Home Phone Number
                        </Label>
                        <Input
                          id="home_phone_number"
                          placeholder="Enter home phone number"
                          value={formData.contact.home_phone_number || ""}
                          onChange={(e) =>
                            handleChange(
                              "contact",
                              "home_phone_number",
                              e.target.value
                            )
                          }
                        />
                      </div>
                    </div>
                    <p className="text-green-500">Address</p>
                    <div className="md:col-span-3 col-span grid grid-cols-1 md:grid-cols-2 gap-4 shadow p-3">
                      <div>
                        <Label className="text-sm" htmlFor="postal_address">
                          Postal Address
                        </Label>
                        <Input
                          id="postal_address"
                          placeholder="Enter postal address"
                          value={formData.contact.postal_address || ""}
                          onChange={(e) =>
                            handleChange(
                              "contact",
                              "postal_address",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div>
                        <Label
                          className="text-sm"
                          htmlFor="residential_address"
                        >
                          Residential Address
                        </Label>
                        <Input
                          id="residential_address"
                          placeholder="Enter residential address"
                          value={formData.contact.residential_address || ""}
                          onChange={(e) =>
                            handleChange(
                              "contact",
                              "residential_address",
                              e.target.value
                            )
                          }
                        />
                      </div>
                    </div>
                    <p className="text-green-500">Location</p>
                    <div className="md:col-span-3 col-span grid grid-cols-1 md:grid-cols-3 gap-4 shadow p-3">
                      <div>
                        <Label className="text-sm" htmlFor="city">
                          City
                        </Label>
                        <Input
                          id="city"
                          placeholder="Enter city"
                          value={formData.contact.city || ""}
                          onChange={(e) =>
                            handleChange("contact", "city", e.target.value)
                          }
                        />
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="county">
                          County
                        </Label>
                        <Input
                          id="county"
                          placeholder="Enter county"
                          value={formData.contact.county || ""}
                          onChange={(e) =>
                            handleChange("contact", "county", e.target.value)
                          }
                        />
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="country">
                          Country
                        </Label>
                        <Input
                          id="country"
                          placeholder="Enter country"
                          value={formData.contact.country || ""}
                          onChange={(e) =>
                            handleChange("contact", "country", e.target.value)
                          }
                        />
                      </div>
                    </div>
                  </div>
                )}

                {activeStep === 3 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm" htmlFor="contract_type">
                        Contract Type
                      </Label>
                      <Select
                        value={formData.contractInfo.contract_type || "none"}
                        onValueChange={(value) =>
                          handleChange(
                            "contractInfo",
                            "contract_type",
                            value === "none" ? "" : value
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Contract Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Contract Type</SelectLabel>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="Permanent">Permanent</SelectItem>
                            <SelectItem value="Contract">Contract</SelectItem>
                            <SelectItem value="Temporary">Temporary</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="contract_start_date">
                        Contract Start Date{" "}
                        <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="contract_start_date"
                        type="date"
                        value={formData.contractInfo.contract_start_date}
                        onChange={(e) =>
                          handleChange(
                            "contractInfo",
                            "contract_start_date",
                            e.target.value
                          )
                        }
                        required
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="current_contract_end">
                        Current Contract End
                      </Label>
                      <Input
                        id="current_contract_end"
                        type="date"
                        value={formData.contractInfo.current_contract_end || ""}
                        onChange={(e) =>
                          handleChange(
                            "contractInfo",
                            "current_contract_end",
                            e.target.value
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label
                        className="text-sm"
                        htmlFor="end_of_probation_date"
                      >
                        End of Probation Date
                      </Label>
                      <Input
                        id="end_of_probation_date"
                        type="date"
                        value={
                          formData.contractInfo.end_of_probation_date || ""
                        }
                        onChange={(e) =>
                          handleChange(
                            "contractInfo",
                            "end_of_probation_date",
                            e.target.value
                          )
                        }
                      />
                    </div>
                  </div>
                )}

                {activeStep === 4 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label
                        className="text-sm"
                        htmlFor="date_of_current_appointment"
                      >
                        Date of Current Appointment *
                      </Label>
                      <Input
                        id="date_of_current_appointment"
                        type="date"
                        value={
                          formData.importantDates.date_of_current_appointment
                        }
                        onChange={(e) =>
                          handleChange(
                            "importantDates",
                            "date_of_current_appointment",
                            e.target.value
                          )
                        }
                        required
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        The date when the employee was appointed to their current position
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="date_of_leaveing">
                        Date of Leaving
                      </Label>
                      <Input
                        id="date_of_leaveing"
                        type="date"
                        value={formData.importantDates.date_of_leaveing || ""}
                        onChange={(e) =>
                          handleChange(
                            "importantDates",
                            "date_of_leaveing",
                            e.target.value || null
                          )
                        }
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        The date when the employee left or will leave the organization (optional)
                      </p>
                    </div>
                  </div>
                )}

                {activeStep === 5 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm" htmlFor="category">
                        Category
                      </Label>
                      <Input
                        id="category"
                        placeholder="Enter category"
                        value={formData.jobInfo.category || ""}
                        onChange={(e) =>
                          handleChange("jobInfo", "category", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="directorate">
                        Directorate
                      </Label>
                      <Input
                        id="directorate"
                        placeholder="Enter directorate"
                        value={formData.jobInfo.directorate || ""}
                        onChange={(e) =>
                          handleChange("jobInfo", "directorate", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="job_title_code">
                        Job Title Code
                      </Label>
                      <Input
                        id="job_title_code"
                        placeholder="Enter job title code"
                        value={formData.jobInfo.job_title_code || ""}
                        onChange={(e) =>
                          handleChange(
                            "jobInfo",
                            "job_title_code",
                            e.target.value
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="job_title">
                        Job Title
                      </Label>
                      <Input
                        id="job_title"
                        placeholder="Enter job title"
                        value={formData.jobInfo.job_title || ""}
                        onChange={(e) =>
                          handleChange("jobInfo", "job_title", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="teams_code">
                        Teams Code
                      </Label>
                      <Input
                        id="teams_code"
                        placeholder="Enter teams code"
                        value={formData.jobInfo.teams_code || ""}
                        onChange={(e) =>
                          handleChange("jobInfo", "teams_code", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="work_location">
                        Work Location
                      </Label>
                      <Input
                        id="work_location"
                        placeholder="Enter work location"
                        value={formData.jobInfo.work_location || ""}
                        onChange={(e) =>
                          handleChange(
                            "jobInfo",
                            "work_location",
                            e.target.value
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm" htmlFor="business_unit">
                        Business Unit
                      </Label>
                      <Input
                        id="business_unit"
                        placeholder="Enter business unit"
                        value={formData.jobInfo.business_unit || ""}
                        onChange={(e) =>
                          handleChange(
                            "jobInfo",
                            "business_unit",
                            e.target.value
                          )
                        }
                      />
                    </div>
                  </div>
                )}

                {activeStep === 6 && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <p className="text-green-500">Bank Details</p>
                    <div className="md:col-span-3 col-span grid grid-cols-1 md:grid-cols-2 gap-4 shadow p-3">
                      <div>
                        <Label className="text-sm" htmlFor="bank_name">
                          Bank Name
                        </Label>
                        <Input
                          id="bank_name"
                          placeholder="Enter bank name"
                          value={formData.paymentInfo.bank_name}
                          onChange={(e) =>
                            handleChange(
                              "paymentInfo",
                              "bank_name",
                              e.target.value
                            )
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="account_number">
                          Account Number
                        </Label>
                        <Input
                          id="account_number"
                          placeholder="Enter account number"
                          value={formData.paymentInfo.account_number}
                          onChange={(e) =>
                            handleChange(
                              "paymentInfo",
                              "account_number",
                              e.target.value
                            )
                          }
                          required
                        />
                      </div>
                    </div>

                    <p className="text-green-500">Salary Scope</p>
                    <div className="md:col-span-3 col-span grid grid-cols-1 md:grid-cols-3 gap-4 shadow p-3">
                      <div>
                        <Label className="text-sm" htmlFor="salary">
                          Salary
                        </Label>
                        <Input
                          id="salary"
                          type="number"
                          min="0"
                          value={formData.paymentInfo.salary}
                          onChange={(e) =>
                            handleChange(
                              "paymentInfo",
                              "salary",
                              Number(e.target.value)
                            )
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="bonus">
                          Bonus
                        </Label>
                        <Input
                          id="bonus"
                          type="number"
                          min="0"
                          value={formData.paymentInfo.bonus}
                          onChange={(e) =>
                            handleChange(
                              "paymentInfo",
                              "bonus",
                              Number(e.target.value)
                            )
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="payment_frequency">
                          Payment Frequency
                        </Label>
                        <Select
                          value={
                            formData.paymentInfo.payment_frequency || "none"
                          }
                          onValueChange={(value) =>
                            handleChange(
                              "paymentInfo",
                              "payment_frequency",
                              value === "none" ? "" : value
                            )
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select Payment Frequency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectLabel>Payment Frequency</SelectLabel>
                              <SelectItem value="none">None</SelectItem>
                              <SelectItem value="Monthly">Monthly</SelectItem>
                              <SelectItem value="Weekly">Weekly</SelectItem>
                              <SelectItem value="Bi-Weekly">
                                Bi-Weekly
                              </SelectItem>
                              <SelectItem value="Bi-Annually">
                                Bi-Annually
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <p className="text-green-500">Statutory </p>
                    <div className="md:col-span-3 col-span grid grid-cols-1 md:grid-cols-3 gap-4 shadow p-3">
                      <div>
                        <Label className="text-sm" htmlFor="tax_status">
                          Tax Status
                        </Label>
                        <div className="flex space-x-4">
                          <Button
                            variant={
                              formData.paymentInfo.tax_status
                                ? "default"
                                : "outline"
                            }
                            onClick={() =>
                              handleChange("paymentInfo", "tax_status", true)
                            }
                          >
                            Yes
                          </Button>
                          <Button
                            variant={
                              !formData.paymentInfo.tax_status
                                ? "default"
                                : "outline"
                            }
                            onClick={() =>
                              handleChange("paymentInfo", "tax_status", false)
                            }
                          >
                            No
                          </Button>
                        </div>
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="KRA_pin">
                          KRA Pin
                        </Label>
                        <Input
                          id="KRA_pin"
                          placeholder="Enter KRA pin"
                          value={formData.paymentInfo.KRA_pin}
                          onChange={(e) =>
                            handleChange(
                              "paymentInfo",
                              "KRA_pin",
                              e.target.value
                            )
                          }
                          required
                        />
                      </div>

                      <div>
                        <Label className="text-sm" htmlFor="NHIF_SHIF_number">
                          NHIF Number
                        </Label>
                        <Input
                          id="NHIF_SHIF_number"
                          placeholder="Enter NHIF number"
                          value={formData.paymentInfo.NHIF_SHIF_number}
                          onChange={(e) =>
                            handleChange(
                              "paymentInfo",
                              "NHIF_SHIF_number",
                              e.target.value
                            )
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="HELB_number">
                          HELB Number
                        </Label>
                        <Input
                          id="HELB_number"
                          placeholder="Enter HELB number"
                          value={formData.paymentInfo.HELB_number}
                          onChange={(e) =>
                            handleChange(
                              "paymentInfo",
                              "HELB_number",
                              e.target.value
                            )
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label className="text-sm" htmlFor="NSSF_number">
                          NSSF Number
                        </Label>
                        <Input
                          id="NSSF_number"
                          placeholder="Enter NSSF number"
                          value={formData.paymentInfo.NSSF_number}
                          onChange={(e) =>
                            handleChange(
                              "paymentInfo",
                              "NSSF_number",
                              e.target.value
                            )
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label
                          className="text-sm"
                          htmlFor="pension_scheme_join"
                        >
                          Pension Scheme Join
                        </Label>
                        <Input
                          id="pension_scheme_join"
                          type="date"
                          value={formData.paymentInfo.pension_scheme_join}
                          onChange={(e) =>
                            handleChange(
                              "paymentInfo",
                              "pension_scheme_join",
                              e.target.value
                            )
                          }
                          required
                        />
                      </div>
                    </div>
                  </div>
                )}

                {activeStep === 7 && (
                  <div className="space-y-4">
                    {formData.educationalQualifications.map(
                      (qualification, index) => (
                        <div
                          key={index}
                          className="border border-gray-300 dark:border-gray-700 p-4 rounded-md space-y-2"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                              <Label
                                className="text-sm"
                                htmlFor={`hq-${index}`}
                              >
                                Higher Qualification
                              </Label>
                              <Input
                                id={`hq-${index}`}
                                placeholder="Enter qualification"
                                value={
                                  qualification.highear_qualificatins || ""
                                }
                                onChange={(e) =>
                                  handleQualificationChange(
                                    index,
                                    "highear_qualificatins",
                                    e.target.value
                                  )
                                }
                              />
                            </div>
                            <div>
                              <Label
                                className="text-sm"
                                htmlFor={`inst-${index}`}
                              >
                                Institution
                              </Label>
                              <Input
                                id={`inst-${index}`}
                                placeholder="Enter institution"
                                value={qualification.instution}
                                onChange={(e) =>
                                  handleQualificationChange(
                                    index,
                                    "instution",
                                    e.target.value
                                  )
                                }
                                required
                              />
                            </div>
                            <div>
                              <Label
                                className="text-sm"
                                htmlFor={`yog-${index}`}
                              >
                                Year of Graduation
                              </Label>
                              <Input
                                id={`yog-${index}`}
                                type="date"
                                value={qualification.year_of_graduation}
                                onChange={(e) =>
                                  handleQualificationChange(
                                    index,
                                    "year_of_graduation",
                                    e.target.value
                                  )
                                }
                                required
                              />
                            </div>
                            <div className="flex flex-col">
                              <Label
                                className="text-sm text-white"
                                htmlFor={`l-${index}`}
                              >
                                _
                              </Label>

                              <Button
                                variant="destructive"
                                className="flex flex-wrap gap-2"
                                onClick={() => removeQualification(index)}
                                disabled={
                                  formData.educationalQualifications.length ===
                                  1
                                }
                              >
                                <Trash2 />
                                <span>Remove Qualification</span>
                              </Button>
                            </div>
                          </div>
                        </div>
                      )
                    )}
                    <Button onClick={addQualification}>
                      Add Qualification
                    </Button>
                  </div>
                )}

                {activeStep === 8 && (
                  <div className="space-y-6">
                    <div>
                      <h2 className="text-lg font-semibold mb-2">
                        Next of Kin
                      </h2>
                      {formData.nextOfKin.next_of_kin.map((kin, index) => (
                        <div
                          key={index}
                          className="border border-gray-300 dark:border-gray-700 p-4 rounded-md space-y-2 mb-2"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                              <Label
                                className="text-sm"
                                htmlFor={`kin-name-${index}`}
                              >
                                Name
                              </Label>
                              <Input
                                id={`kin-name-${index}`}
                                placeholder="Enter name"
                                value={kin.name}
                                onChange={(e) =>
                                  handleNextOfKinChange(
                                    index,
                                    "name",
                                    e.target.value
                                  )
                                }
                                required
                              />
                            </div>
                            <div>
                              <Label
                                className="text-sm"
                                htmlFor={`kin-relationship-${index}`}
                              >
                                Relationship
                              </Label>
                              <Input
                                id={`kin-relationship-${index}`}
                                placeholder="Enter relationship"
                                value={kin.relationship}
                                onChange={(e) =>
                                  handleNextOfKinChange(
                                    index,
                                    "relationship",
                                    e.target.value
                                  )
                                }
                                required
                              />
                            </div>
                            <div>
                              <Label
                                className="text-sm"
                                htmlFor={`kin-contact-${index}`}
                              >
                                Contact
                              </Label>
                              <Input
                                id={`kin-contact-${index}`}
                                placeholder="Enter contact info"
                                value={kin.contact_info}
                                onChange={(e) =>
                                  handleNextOfKinChange(
                                    index,
                                    "contact_info",
                                    e.target.value
                                  )
                                }
                                required
                              />
                            </div>
                            <div className="flex flex-col ">
                              <p className="text-white">_</p>
                              <Button
                                variant="outline"
                                className="text-red-500 border border-red-500 hover:text-white hover:bg-red-500 transition ease-in-out duration-700"
                                onClick={() => removeNextOfKin(index)}
                                disabled={
                                  formData.nextOfKin.next_of_kin.length === 1
                                }
                              >
                                <Trash2 /> <span>Remove Next of Kin</span>
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                      <Button onClick={addNextOfKin}>Add Next of Kin</Button>
                    </div>

                    <div>
                      <h2 className="text-lg font-semibold mb-2">Dependants</h2>
                      {formData.nextOfKin.dependants.map((dep, index) => (
                        <div
                          key={index}
                          className="border border-gray-300 dark:border-gray-700 p-4 rounded-md space-y-2 mb-2"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                              <Label
                                className="text-sm"
                                htmlFor={`dep-name-${index}`}
                              >
                                Name
                              </Label>
                              <Input
                                id={`dep-name-${index}`}
                                placeholder="Enter name"
                                value={dep.name}
                                onChange={(e) =>
                                  handleDependantChange(
                                    index,
                                    "name",
                                    e.target.value
                                  )
                                }
                                required
                              />
                            </div>
                            <div>
                              <Label
                                className="text-sm"
                                htmlFor={`dep-relationship-${index}`}
                              >
                                Relationship
                              </Label>
                              <Input
                                id={`dep-relationship-${index}`}
                                placeholder="Enter relationship"
                                value={dep.relationship}
                                onChange={(e) =>
                                  handleDependantChange(
                                    index,
                                    "relationship",
                                    e.target.value
                                  )
                                }
                                required
                              />
                            </div>
                            <div>
                              <Label
                                className="text-sm"
                                htmlFor={`dep-contact-${index}`}
                              >
                                Contact
                              </Label>
                              <Input
                                id={`dep-contact-${index}`}
                                placeholder="Enter contact info"
                                value={dep.contact_info}
                                onChange={(e) =>
                                  handleDependantChange(
                                    index,
                                    "contact_info",
                                    e.target.value
                                  )
                                }
                                required
                              />
                            </div>
                            <div className="flex flex-col ">
                              <p className="text-white">_</p>
                              <Button
                                variant="outline"
                                className="text-red-500 border border-red-500 hover:text-white hover:bg-red-500 transition ease-in-out duration-700"
                                onClick={() => removeDependant(index)}
                                disabled={
                                  formData.nextOfKin.dependants.length === 1
                                }
                              >
                                <Trash2 /> Remove Dependant
                              </Button>
                            </div>
                          </div>
                          <div></div>
                        </div>
                      ))}
                      <Button onClick={addDependant}>Add Dependant</Button>
                    </div>
                  </div>
                )}
              </form>
            ) : (
              <div className="mt-6 text-center">
                <Alert variant="default" className="mb-4">
                  Employee created successfully!
                </Alert>
                <Button onClick={() => navigate("/employees")}>
                  Go to Employees
                </Button>
              </div>
            )}

            {!finished && (
              <div className="flex space-x-4 mt-6">
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={activeStep === 0 || submitting}
                >
                  Back
                </Button>
                <Button onClick={handleNext} disabled={submitting}>
                  {submitting ? (
                    <Loader size="sm" />
                  ) : activeStep === steps.length - 1 ? (
                    "Finish"
                  ) : (
                    "Next"
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={submitting}
                >
                  Cancel
                </Button>
              </div>
            )}
          </>
        )}

        {mode == "bulk" && (
          <div>
            {bulkStep === 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-3">
                  Step 1: Upload Excel File
                </h2>
                {bulkError && (
                  <Alert variant="destructive" className="mb-4">
                    {bulkError}
                  </Alert>
                )}
                <label
                  htmlFor="bulkFile"
                  className=" flex flex-col justify-center items-center gap-2 border border-dashed border-slate-500 p-5 rounded hover:bg-slate-100 transition duration-700 ease-linear"
                >
                  <CloudUpload size={64} />
                  <p>Upload Excel File (.xlsx or .xls)</p>
                  <div hidden>
                    <Input
                      id="bulkFile"
                      type="file"
                      accept=".xlsx, .xls"
                      onChange={handleBulkFileChange}
                    />
                  </div>
                </label>

                <Button
                  className="mt-4"
                  onClick={() => handleBulkFileUpload(bulkFile)}
                  disabled={!bulkFile}
                >
                  Parse &amp; Preview
                </Button>
              </div>
            )}

            {bulkStep === 1 && (
              <div>
                <h2 className="text-xl font-semibold mb-3">
                  Step 2: Preview File
                </h2>
                {bulkError && (
                  <Alert variant="destructive" className="mb-4">
                    {bulkError}
                  </Alert>
                )}
                <p className="mb-4">
                  Below is a preview of the first few rows parsed from the file
                  (with row #1 skipped automatically). Please confirm the data
                  looks correct before proceeding.
                </p>
                <div className="max-h-64 overflow-auto border border-gray-300 rounded-md">
                  <table className="min-w-full text-sm text-left">
                    <thead className="border-b bg-gray-50">
                      {bulkData.length > 0 && (
                        <tr>
                          {tableHeaders.map((colKey: string) => (
                            <th key={colKey} className="px-4 font-medium">
                              {colKey}
                            </th>
                          ))}
                        </tr>
                      )}
                    </thead>
                    <tbody>
                      {/* {bulkData.slice(0, 5).map((row, idx) => (
                        <tr key={idx} className="border-b hover:bg-gray-100">
                          {Object.keys(row).map((key) => (
                            <td key={key} className="px-4 py-2">
                              {row[key]}
                            </td>
                          ))}
                        </tr>
                      ))} */}
                      {bulkData.slice(0, 5).map((row, rowIndex) => (
                        <tr
                          key={rowIndex}
                          className="border-b hover:bg-gray-100"
                        >
                          {tableHeaders.map((key: string, colIndex: number) => (
                            <td key={colIndex} className=" px-4 py-2">
                              {row[key]}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="flex space-x-4 mt-4">
                  <Button variant="outline" onClick={() => setBulkStep(0)}>
                    Reset
                  </Button>
                  <Button
                    onClick={handleConfirmBulkUpload}
                    disabled={bulkLoading}
                  >
                    {bulkLoading ? (
                      <Loader size="sm" />
                    ) : (
                      "Confirm & Bulk Create"
                    )}
                  </Button>
                </div>
              </div>
            )}

            {bulkStep === 2 && (
              <div>
                <Alert variant="default" className="mb-4">
                  Bulk upload process completed!
                </Alert>
                <Button onClick={() => navigate("/employees")}>
                  Go to Employees
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </Screen>
  );
};

export default CreateEmployee;
