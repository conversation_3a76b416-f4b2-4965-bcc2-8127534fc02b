import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { BASE_URL } from '@/config';
import { RootState } from '@/redux/store';
import { useToast } from '@/hooks/use-toast';
import { Loader2, AlertCircle, TrendingUp, Users, CheckCircle2 } from 'lucide-react';
import HRAppraisalRecord from './HRAppraisalRecord';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Employee, Department, AppraisalViewModel, KPI } from './types';

const HRDashboard: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const periodFromQuery = queryParams.get('period');
  const [selectedPeriod] = useState<string>(periodFromQuery || '');
  const [appraisalRecords, setAppraisalRecords] = useState<AppraisalViewModel[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<AppraisalViewModel[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [selectedDepartmentFilter, setSelectedDepartmentFilter] = useState<string>('');
  const [selectedStatusFilter, setSelectedStatusFilter] = useState<string>('all');
  const [appliedDepartmentFilter, setAppliedDepartmentFilter] = useState<string>('');
  const [appliedStatusFilter, setAppliedStatusFilter] = useState<string>('all');

  // Appraisal summary states
  const [appraisalSummary, setAppraisalSummary] = useState<{ [key: string]: number }>({});
  const [summaryLoading, setSummaryLoading] = useState<boolean>(false);

  // Fetch appraisal summary data
  const fetchAppraisalSummary = async () => {
    setSummaryLoading(true);
    try {
      // Fetch summary for all statuses
      const statusesToFetch = ['ALL', 'Open', 'Self Appraised', 'Employee Accepted', 'Supervisor Accepted', 'In Review', 'Supervisor Appraised', 'HR Appraised', 'Completed'];

      const summaryData: { [key: string]: number } = {};

      for (const status of statusesToFetch) {
        try {
          const res = await fetch(`${BASE_URL}/appraisal_summary/?status=${encodeURIComponent(status)}`, {
            headers: { Authorization: `Token ${token}` },
          });

          if (res.ok) {
            const data = await res.json();
            if (status === 'ALL') {
              // For ALL, sum up all the counts
              summaryData['Total'] = data.results?.reduce((sum: number, item: any) => sum + (item.count || 0), 0) || 0;
            } else {
              // For specific statuses, find the matching result
              const statusResult = data.results?.find((item: any) => item.status === status);
              summaryData[status] = statusResult?.count || 0;
            }
          } else {
            console.warn(`Failed to fetch summary for status: ${status}`);
            summaryData[status] = 0;
          }
        } catch (err) {
          console.warn(`Error fetching summary for status ${status}:`, err);
          summaryData[status] = 0;
        }
      }

      setAppraisalSummary(summaryData);
    } catch (err: any) {
      console.error('Error fetching appraisal summary:', err);
      toast({
        variant: 'destructive',
        title: 'Warning',
        description: 'Unable to load appraisal summary data',
      });
    } finally {
      setSummaryLoading(false);
    }
  };

  const getUniqueAppraisalPeriods = (records: AppraisalViewModel[]) => {
    const uniquePeriods = Array.from(new Set(records.map(record =>
      JSON.stringify({
        id: record.id,
        name: record.name,
        start_date: record.start_date,
        end_date: record.end_date,
        cycle_type: record.cycle_type
      })
    ))).map(periodStr => JSON.parse(periodStr));

    return uniquePeriods;
  };

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const res = await fetch(`${BASE_URL}/users/departments`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!res.ok) throw new Error('Failed to fetch departments');
        const data = await res.json();
        setDepartments(data);
      } catch (err: any) {
        console.error('Error fetching departments:', err.message);
      }
    };
    fetchDepartments();
  }, [token]);

  // Fetch appraisal summary on component mount
  useEffect(() => {
    if (token) {
      fetchAppraisalSummary();
    }
  }, [token]);

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        const res = await fetch(`${BASE_URL}/employee_details/?team=ALL&department=ALL`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!res.ok) throw new Error('Failed to fetch employees');
        const data = await res.json();

        // Transform the new API response to match our interface
        const transformedEmployees = data.results.map((emp: any) => ({
          ...emp,
          employee_no: emp.employee_no_id, // Map for backward compatibility
          department_id: emp.department_id, // Ensure it's a number
        }));

        setEmployees(transformedEmployees);
      } catch (err: any) {
        setError(err.message || 'Unable to fetch employee data');
        toast({
          variant: 'destructive',
          title: 'Error',
          description: err.message || 'Unable to fetch employee data',
        });
      } finally {
        setLoading(false);
      }
    };
    fetchEmployees();
  }, [token, toast]);

  useEffect(() => {
    console.log("Fetch effect triggered with:", {
      appliedDepartmentFilter,
      selectedPeriod,
      appliedStatusFilter
    });

    const fetchEmployeeRecords = async () => {
      try {
        setRefreshing(true);

        // Build query parameters
        const departmentFilterParam = appliedDepartmentFilter ? `&department_id=${appliedDepartmentFilter}` : '';
        const periodFilterParam = selectedPeriod ? `&appraisal_period=${selectedPeriod}` : '';

        // Fetch basic employee appraisal records without heavy computations - only active records
        const res = await fetch(`${BASE_URL}/hrm/employee-appraisals?is_active=true${departmentFilterParam}${periodFilterParam}`, {
          headers: { Authorization: `Token ${token}` },
          method: 'GET'
        });

        if (!res.ok) throw new Error('Failed to fetch employee records');

        const data = await res.json();
        console.log("Raw employee records:", data);

        // Process records without heavy computations
        const processedData = data.map((record: AppraisalViewModel) => {
          try {
            const employee = employees.find(emp => emp.employee_no === record.employeeid);
            if (employee) {
              const department = departments.find(dept => dept.id === employee.department_id);

              record.employee_name = `${employee.first_name} ${employee.last_name}` || 'Unknown Employee';
              record.department_name = department?.name || 'Unknown Department';
              record.department_id = employee.department_id;
            } else {
              record.employee_name = 'Unknown Employee';
              record.department_name = 'Unknown Department';
            }

            // For each record, derive the period_id from its own ID since they're now tied together
            record.period_id = record.id;

            // Set empty KPIs array - will be loaded on-demand when viewing details
            record.kpis = [];

            return record;
          } catch (error) {
            console.error(`Error processing record for employee ${record.employeeid}:`, error);
            return record; // Return the record even if processing fails
          }
        });

        // Set processed data directly
        const validProcessedData = processedData.filter(record => record !== null);

        setAppraisalRecords(validProcessedData);
        applyStatusFilter(validProcessedData, appliedStatusFilter);

      } catch (err: any) {
        setError(err.message || 'Unable to fetch employee records');
        toast({
          variant: 'destructive',
          title: 'Error',
          description: err.message || 'Unable to fetch employee records',
        });
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    };

    // Only fetch if we have employees and departments loaded
    if (employees.length > 0 && departments.length > 0) {
      fetchEmployeeRecords();
    }
  }, [token, appliedDepartmentFilter, selectedPeriod, employees, departments, appliedStatusFilter, toast]);

  // Filter records by status
  const applyStatusFilter = (records: AppraisalViewModel[], filterValue: string) => {
    console.log("Filtering records by status:", filterValue);
    console.log("Records before filtering:", records.length);
    if (filterValue === 'all') {
      console.log("Showing all records");
      setFilteredRecords(records);
      return;
    }

    let filtered = [...records];

    if (filterValue === 'open-appraisals') {
      console.log("Filtering for open-appraisals");
      filtered = filtered.filter(record => record.status === 'Open');
    } else if (filterValue === 'self-appraised') {
      console.log("Filtering for self-appraised");
      filtered = filtered.filter(record => record.status === 'Self Appraised');
    } else if (filterValue === 'employee-accepted') {
      console.log("Filtering for employee-accepted");
      filtered = filtered.filter(record => record.status === 'Employee Accepted');
    } else if (filterValue === 'supervisor-accepted') {
      console.log("Filtering for supervisor-accepted");
      filtered = filtered.filter(record => record.status === 'Supervisor Accepted');
    } else if (filterValue === 'in-review') {
      console.log("Filtering for in-review");
      filtered = filtered.filter(record => record.status === 'In Review');
    } else if (filterValue === 'supervisor-appraised') {
      console.log("Filtering for supervisor-appraised");
      filtered = filtered.filter(record => record.status === 'Supervisor Appraised');
    } else if (filterValue === 'hr-appraised') {
      console.log("Filtering for hr-appraised");
      filtered = filtered.filter(record => record.status === 'HR Appraised');
    } else if (filterValue === 'completed') {
      console.log("Filtering for completed");
      filtered = filtered.filter(record => record.status === 'Completed');
    }

    console.log("Records after filtering:", filtered.length);
    setFilteredRecords(filtered);
  };

  // Handle department filter change
  const handleDepartmentChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedDepartmentFilter(event.target.value);
  };

  // Handle status filter change
  const handleStatusFilterChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStatusFilter(event.target.value);
  };

  // Apply filters when button is clicked
  const applyFilters = () => {
    console.log("Applying filters:", {
      department: selectedDepartmentFilter,
      status: selectedStatusFilter
    });
    setAppliedDepartmentFilter(selectedDepartmentFilter);
    setAppliedStatusFilter(selectedStatusFilter);
    // Refresh summary data when filters are applied
    fetchAppraisalSummary();
  };

  // Reset filters
  const resetFilters = () => {
    setSelectedDepartmentFilter('');
    setSelectedStatusFilter('all');
    setAppliedDepartmentFilter('');
    setAppliedStatusFilter('all');
  };

  // Find the selected period name
  const getCurrentPeriodName = () => {
    if (!selectedPeriod) return "All Periods";

    // Find period in appraisal records
    const period = appraisalRecords.find(r => r.id.toString() === selectedPeriod);
    return period ? period.name : "Unknown Period";
  };

  // Calculate unique periods from our records
  const appraisalPeriods = getUniqueAppraisalPeriods(appraisalRecords);

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="/" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink href="/performance-dashboard" className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
            Performance
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage>Performance Dashboard</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  return (
    <div className="flex-1 space-y-6 p-2 md:p-2">
      <div className="flex items-center justify-between">
        {breadcrumb}
      </div>

      {selectedPeriod && (
        <Card className="bg-gradient-to-r from-green-50 to-indigo-50 border-green-200 dark:from-green-900/20 dark:to-indigo-900/20 dark:border-green-800">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <h2 className="text-lg font-semibold text-green-800 dark:text-green-300">
                  {getCurrentPeriodName()}
                </h2>
                {appraisalRecords.length > 0 && selectedPeriod && (
                  <p className="text-sm text-green-600 dark:text-green-400">
                    {appraisalRecords.find(p => p.id.toString() === selectedPeriod)?.cycle_type} Cycle |
                    {' '}{new Date(appraisalRecords.find(p => p.id.toString() === selectedPeriod)?.start_date || '').toLocaleDateString()} -
                    {' '}{new Date(appraisalRecords.find(p => p.id.toString() === selectedPeriod)?.end_date || '').toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Key Metrics Cards - Using real API data */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Key Metrics</h2>
        <Button
          onClick={fetchAppraisalSummary}
          variant="outline"
          size="sm"
          disabled={summaryLoading}
          className="flex items-center gap-2"
        >
          {summaryLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <TrendingUp className="h-4 w-4" />
          )}
          Refresh Data
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Users className="mr-2 h-5 w-5" />
              Total Appraisals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {summaryLoading ? (
                <Loader2 className="animate-spin h-8 w-8" />
              ) : (
                appraisalSummary['Total'] || 0
              )}
            </div>
            <p className="text-sm opacity-80">
              Total records in system
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <TrendingUp className="mr-2 h-5 w-5" />
              In Supervisor Review
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {summaryLoading ? (
                <Loader2 className="animate-spin h-8 w-8" />
              ) : (
                appraisalSummary['In Review'] || 0
              )}
            </div>
            <p className="text-sm opacity-80">Awaiting final approval</p>
          </CardContent>
        </Card>

          <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <CheckCircle2 className="mr-2 h-5 w-5" />
              Completed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {summaryLoading ? (
                <Loader2 className="animate-spin h-8 w-8" />
              ) : (
                appraisalSummary['Completed'] || 0
              )}
            </div>
            <p className="text-sm opacity-80">Fully processed appraisals</p>
          </CardContent>
        </Card>
      </div>

      {/* Appraisal Status Breakdown - Using real API data */}
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle>Appraisal Status Breakdown</CardTitle>
          <CardDescription>
            Overview of current appraisal status distribution
          </CardDescription>
        </CardHeader>
        <CardContent>
          {summaryLoading ? (
            <div className="flex justify-center p-8">
              <Loader2 className="animate-spin h-8 w-8 text-green-600" />
            </div>
          ) : (
            <div className="flex flex-wrap gap-1 md:gap-2">
              {[
                {
                  label: "Open Appraisals",
                  count: appraisalSummary['Open'] || 0,
                  color: "bg-gray-100 border-gray-300 text-gray-800 dark:bg-gray-900 dark:text-gray-200 dark:border-gray-800"
                },
                {
                  label: "Self Appraised",
                  count: appraisalSummary['Self Appraised'] || 0,
                  color: "bg-sky-100 border-sky-300 text-sky-800 dark:bg-sky-900 dark:text-sky-200 dark:border-sky-800"
                },
                {
                  label: "Employee Accepted",
                  count: appraisalSummary['Employee Accepted'] || 0,
                  color: "bg-emerald-100 border-emerald-300 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200 dark:border-emerald-800"
                },
                {
                  label: "Supervisor Accepted",
                  count: appraisalSummary['Supervisor Accepted'] || 0,
                  color: "bg-teal-100 border-teal-300 text-teal-800 dark:bg-teal-900 dark:text-teal-200 dark:border-teal-800"
                },
                {
                  label: "In Supervisor Review",
                  count: appraisalSummary['In Review'] || 0,
                  color: "bg-orange-100 border-orange-300 text-orange-800 dark:bg-orange-900 dark:text-orange-200 dark:border-orange-800"
                },
                {
                  label: "Supervisor Appraised",
                  count: appraisalSummary['Supervisor Appraised'] || 0,
                  color: "bg-amber-100 border-amber-300 text-amber-800 dark:bg-amber-900 dark:text-amber-200 dark:border-amber-800"
                },
                {
                  label: "HR Appraised",
                  count: appraisalSummary['HR Appraised'] || 0,
                  color: "bg-indigo-100 border-indigo-300 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 dark:border-indigo-800"
                },
                {
                  label: "Completed",
                  count: appraisalSummary['Completed'] || 0,
                  color: "bg-green-100 border-green-300 text-green-800 dark:bg-green-900 dark:text-green-200 dark:border-green-800"
                }
              ].map((item, index) => {
                return (
                  <div
                    key={index}
                    className={`flex-1 min-w-[150px] py-3 px-4 rounded-lg border ${item.color} transition-all hover:shadow-md`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="text-xl font-bold">{item.count}</div>
                    </div>
                    <h3 className="text-sm font-medium mt-1 truncate">{item.label}</h3>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Filters Section - Now with Apply button */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter appraisal records by department and status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            {/* Department Filter */}
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Department</label>
              <select
                onChange={handleDepartmentChange}
                value={selectedDepartmentFilter}
                className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
              >
                <option value="">All Departments</option>
                {departments.map((department) => (
                  <option key={department.id} value={department.id}>
                    {department.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">Status</label>
              <select
                onChange={handleStatusFilterChange}
                value={selectedStatusFilter}
                className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
              >
                <option value="all">All Status</option>
                <option value="open-appraisals">Open Appraisals</option>
                <option value="self-appraised">Self Appraised</option>
                <option value="employee-accepted">Employee Accepted</option>
                <option value="supervisor-accepted">Supervisor Accepted</option>
                <option value="in-review">In Review</option>
                <option value="supervisor-appraised">Supervisor Appraised</option>
                <option value="hr-appraised">HR Appraised</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>

          {/* Filter Action Buttons */}
          <div className="flex gap-2 justify-end">
            <Button
              onClick={resetFilters}
              variant="outline"
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              Reset Filters
            </Button>
            <Button
              onClick={applyFilters}
              className="bg-green-600 text-white hover:bg-green-700 transition-colors"
            >
              Apply Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      {loading ? (
        <div className="flex justify-center p-12">
          <Loader2 className="animate-spin h-10 w-10 text-green-600" />
        </div>
      ) : error ? (
        <Card className="border-red-200">
          <CardContent className="flex items-center justify-center p-6 text-red-500">
            <AlertCircle className="mr-2" size={24} />
            <p>{error}</p>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Appraisal Records</CardTitle>
            <CardDescription>
              {selectedPeriod
                ? `Showing ${filteredRecords.length} records for ${getCurrentPeriodName()}`
                : 'Detailed view of all performance appraisals'}
              {(appliedDepartmentFilter || appliedStatusFilter !== 'all') && ' (Filtered)'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HRAppraisalRecord
              appraisalRecords={filteredRecords}
              employees={employees}
              departments={departments}
              appraisalPeriods={appraisalPeriods}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default HRDashboard;