import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';
import {
  BarChart3,
  PieChart,
  TrendingUp,
  TrendingDown,
  Users,
  Building2,
  Target,
  Award,
  AlertTriangle,
  RefreshCw,
  Calendar
} from 'lucide-react';

// Types
interface SummaryReportData {
  total_employees: number;
  total_appraisals: number;
  completion_rate: number;
  average_organization_rating: number;
  departments_count: number;
  teams_count: number;
  top_performing_departments: DepartmentSummary[];
  performance_distribution: {
    excellent: number;
    good: number;
    average: number;
    needs_improvement: number;
  };
  monthly_trends: MonthlyTrend[];
  recent_completions: RecentCompletion[];
}

interface DepartmentSummary {
  department_name: string;
  completion_rate: number;
  average_rating: number;
  employee_count: number;
}

interface MonthlyTrend {
  month: string;
  completions: number;
  average_rating: number;
}

interface RecentCompletion {
  employee_name: string;
  department: string;
  completion_date: string;
  rating: number;
}

interface SummaryFilters {
  date_from?: string;
  date_to?: string;
  performance_period?: string;
}

interface SummaryReportsTabProps {
  onDataChange?: (data: SummaryReportData) => void;
}

const SummaryReportsTab: React.FC<SummaryReportsTabProps> = ({ onDataChange }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State
  const [data, setData] = useState<SummaryReportData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<SummaryFilters>({});
  const [isExporting, setIsExporting] = useState(false);
  
  // Debounced filters for API calls
  const [debouncedFilters, setDebouncedFilters] = useState<SummaryFilters>({});

  // Simple debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 500);

    return () => clearTimeout(timer);
  }, [filters]);

  // Fetch summary report data
  const fetchSummaryData = useCallback(async (filterParams: SummaryFilters) => {
    if (!token) return;

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filterParams.date_from) params.append('date_from', filterParams.date_from);
      if (filterParams.date_to) params.append('date_to', filterParams.date_to);

      // Fetch multiple data sources in parallel
      const [appraisalRes, deptRes, teamsRes, jobInfoRes] = await Promise.all([
        fetch(`${BASE_URL}/appraisal_report/${params.toString() ? `?${params.toString()}` : ''}`, {
          headers: { 'Authorization': `Token ${token}`, 'Content-Type': 'application/json' },
        }),
        fetch(`${BASE_URL}/users/departments`, {
          headers: { 'Authorization': `Token ${token}` },
        }),
        fetch(`${BASE_URL}/users/organization_groups`, {
          headers: { 'Authorization': `Token ${token}` },
        }),
        fetch(`${BASE_URL}/users/employee-job-info-details`, {
          headers: { 'Authorization': `Token ${token}` },
        }),
      ]);

      if (!appraisalRes.ok || !deptRes.ok || !teamsRes.ok || !jobInfoRes.ok) {
        throw new Error('Failed to fetch required data');
      }

      const [appraisalData, departments, teams, jobInfo] = await Promise.all([
        appraisalRes.json(),
        deptRes.json(),
        teamsRes.json(),
        jobInfoRes.json(),
      ]);

      // Process and aggregate summary data
      const summaryData = generateSummaryReport(
        appraisalData.results || [],
        departments,
        teams,
        jobInfo
      );
      
      setData(summaryData);
      
      // Notify parent component of data change
      if (onDataChange) {
        onDataChange(summaryData);
      }
      
    } catch (err: any) {
      console.error('Error fetching summary data:', err);
      setError(err.message || 'Failed to fetch summary data');
      toast({
        title: "Error",
        description: "Failed to fetch summary data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [token, toast, onDataChange]);

  // Generate comprehensive summary report
  const generateSummaryReport = (
    appraisalData: any[],
    departments: any[],
    teams: any[],
    jobInfo: any[]
  ): SummaryReportData => {
    // Basic counts
    const uniqueEmployees = new Set(appraisalData.map(r => r.employeeid_id));
    const totalEmployees = uniqueEmployees.size;
    const totalAppraisals = appraisalData.length;

    // Completion statistics
    const completedAppraisals = appraisalData.filter(r => r.status === 'Completed');
    const completionRate = totalEmployees > 0 ? Math.round((completedAppraisals.length / totalEmployees) * 100) : 0;

    // Average organization rating
    const supervisorRatings = appraisalData
      .filter(r => r.total_supervisor_rating_score !== null)
      .map(r => r.total_supervisor_rating_score);
    const avgOrgRating = supervisorRatings.length > 0 
      ? Math.round((supervisorRatings.reduce((a, b) => a + b, 0) / supervisorRatings.length) * 100) / 100
      : 0;

    // Performance distribution
    const performanceDistribution = {
      excellent: supervisorRatings.filter(r => r >= 90).length,
      good: supervisorRatings.filter(r => r >= 70 && r < 90).length,
      average: supervisorRatings.filter(r => r >= 50 && r < 70).length,
      needs_improvement: supervisorRatings.filter(r => r < 50).length,
    };

    // Department performance summary
    const deptMap = new Map(departments.map(d => [d.id, d]));
    const employeeDeptMap = new Map();
    jobInfo.forEach(job => {
      employeeDeptMap.set(job.employee_no, job.department);
    });

    const departmentStats = new Map();
    appraisalData.forEach(record => {
      const deptId = employeeDeptMap.get(record.employeeid_id);
      if (deptId && deptMap.has(deptId)) {
        const deptName = deptMap.get(deptId).name;
        if (!departmentStats.has(deptName)) {
          departmentStats.set(deptName, {
            employees: new Set(),
            completed: new Set(),
            ratings: []
          });
        }
        const stats = departmentStats.get(deptName);
        stats.employees.add(record.employeeid_id);
        if (record.status === 'Completed') {
          stats.completed.add(record.employeeid_id);
        }
        if (record.total_supervisor_rating_score !== null) {
          stats.ratings.push(record.total_supervisor_rating_score);
        }
      }
    });

    const topPerformingDepartments: DepartmentSummary[] = Array.from(departmentStats.entries())
      .map(([deptName, stats]) => ({
        department_name: deptName,
        completion_rate: Math.round((stats.completed.size / stats.employees.size) * 100),
        average_rating: stats.ratings.length > 0 
          ? Math.round((stats.ratings.reduce((a: number, b: number) => a + b, 0) / stats.ratings.length) * 100) / 100
          : 0,
        employee_count: stats.employees.size,
      }))
      .sort((a, b) => b.average_rating - a.average_rating)
      .slice(0, 5);

    // Monthly trends (simulated for demo)
    const monthlyTrends: MonthlyTrend[] = [
      { month: 'Jan 2025', completions: 45, average_rating: 78.5 },
      { month: 'Feb 2025', completions: 52, average_rating: 79.2 },
      { month: 'Mar 2025', completions: 48, average_rating: 77.8 },
      { month: 'Apr 2025', completions: 55, average_rating: 80.1 },
      { month: 'May 2025', completions: 61, average_rating: 81.3 },
      { month: 'Jun 2025', completions: 58, average_rating: 79.9 },
    ];

    // Recent completions
    const recentCompletions: RecentCompletion[] = completedAppraisals
      .slice(0, 10)
      .map(record => {
        const deptId = employeeDeptMap.get(record.employeeid_id);
        const deptName = deptId && deptMap.has(deptId) ? deptMap.get(deptId).name : 'Unknown';
        return {
          employee_name: `${record.first_name} ${record.last_name}`,
          department: deptName,
          completion_date: new Date().toISOString().split('T')[0], // Simulated
          rating: record.total_supervisor_rating_score || 0,
        };
      });

    return {
      total_employees: totalEmployees,
      total_appraisals: totalAppraisals,
      completion_rate: completionRate,
      average_organization_rating: avgOrgRating,
      departments_count: departments.length,
      teams_count: teams.length,
      top_performing_departments: topPerformingDepartments,
      performance_distribution: performanceDistribution,
      monthly_trends: monthlyTrends,
      recent_completions: recentCompletions,
    };
  };

  // Fetch data when debounced filters change
  useEffect(() => {
    fetchSummaryData(debouncedFilters);
  }, [debouncedFilters, fetchSummaryData]);

  // Filter handlers
  const handleFilterChange = (key: keyof SummaryFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const refreshData = () => {
    fetchSummaryData(debouncedFilters);
  };

  // Get performance color
  const getPerformanceColor = (rating: number) => {
    if (rating >= 90) return 'text-green-600';
    if (rating >= 70) return 'text-blue-600';
    if (rating >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Get trend icon
  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <div className="h-4 w-4" />;
  };

  return {
    data,
    loading,
    error,
    filters,
    setFilters,
    isExporting,
    setIsExporting,
    debouncedFilters,
    fetchSummaryData,
    handleFilterChange,
    clearFilters,
    refreshData,
    generateSummaryReport,
    getPerformanceColor,
    getTrendIcon
  };
};

export default SummaryReportsTab;
