"use client";

import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ArrowLeft } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { sendPasswordResetLink, resetPasswordStatus } from "@/redux/features/auth/authSlice";
import { RootState, AppDispatch } from "@/redux/store";
import bgImage from '../../assets/reception.webp'

const ForgotPassword: React.FC = () => {
  const { toast } = useToast();
  const [email, setEmail] = useState("");
  const dispatch = useDispatch<AppDispatch>();
  
  // Use Redux state for loading and success instead of local state
  const { loading, success, error } = useSelector(
    (state: RootState) => state.auth.resetStatus
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast({
        title: "Missing Email",
        description: "Please enter your email address.",
        variant: "destructive",
      });
      return;
    }
    
    try {
      // Use the Redux thunk instead of direct axios call
      const resultAction = await dispatch(sendPasswordResetLink(email));
      
      if (sendPasswordResetLink.fulfilled.match(resultAction)) {
        toast({
          title: "Success",
          description: "Password reset link has been sent to your email.",
          variant: "default",
        });
      } else if (sendPasswordResetLink.rejected.match(resultAction)) {
        const errorMessage = resultAction.payload as string || "Failed to send reset link";
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send reset link",
        variant: "destructive",
      });
    }
  };

  // Reset the password status when component unmounts
  React.useEffect(() => {
    return () => {
      dispatch(resetPasswordStatus());
    };
  }, [dispatch]);

  return (
    <div className="flex w-full min-h-screen">
      {/* Left side */}
      <div 
        className="hidden lg:flex lg:w-1/2 justify-center items-center relative"
        style={{
          backgroundImage: `url(${bgImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center"
        }}
      >
      </div>

      {/* Form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-4 bg-gray-50 dark:bg-gray-900">
        <div className="w-full max-w-md px-8 py-10 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
          <div className="flex justify-center mb-6">
            <img 
              src="https://workspace.optiven.co.ke/static/media/optiven-logo-full.f498da6255572ff1ab8a.png" 
              alt="Optiven Limited Logo" 
              className="h-16" 
            />
          </div>
          
          {!success ? (
            <>
              <h1 className="text-2xl font-bold text-center text-gray-900 dark:text-white mb-4">
                Forgot Password
              </h1>
              <p className="text-center text-gray-600 dark:text-gray-400 mb-8">
                Enter your email address and we'll send you a link to reset your password.
              </p>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    disabled={loading}
                  />
                </div>

                <Button
                  type="submit"
                  variant="default"
                  disabled={loading}
                  className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 text-white font-medium rounded-md transition-colors"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    "Send Reset Link"
                  )}
                </Button>
              </form>
            </>
          ) : (
            <div className="text-center space-y-4">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 text-green-600 mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Check Your Email
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                We've sent a password reset link to <span className="font-medium">{email}</span>
              </p>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Please note that the link will expire in 30 minutes.
              </p>
            </div>
          )}

          <div className="mt-8">
            <Link
              to="/login"
              className="flex items-center justify-center text-sm font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Login
            </Link>
          </div>

          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-500">
              © {new Date().getFullYear()} Optiven Limited. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;