"use client";

import React, { useEffect, useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectItem,
  SelectContent,
} from "@/components/ui/select";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

import {
  JobVacancy,
  InterviewLevel,
  InterviewResult,
  VacancyApplication,
  ExternalUserBioData,
} from "../types";
import { BASE_URL } from "@/config";

const InterviewResultsTab: React.FC = () => {
  const { user, token } = useSelector((state: RootState) => state.auth);
  const [vacancies, setVacancies] = useState<JobVacancy[]>([]);
  const [allLevels, setAllLevels] = useState<InterviewLevel[]>([]);
  const [allResults, setAllResults] = useState<InterviewResult[]>([]);
  const [applications, setApplications] = useState<VacancyApplication[]>([]);
  const [offerTemplates, setOfferTemplates] = useState<any[]>([]);
  const [bioData, setBioData] = useState<ExternalUserBioData[]>([]);
  const [candidateNameMap, setCandidateNameMap] = useState<Record<string, string>>({});

  const [selectedVacancyId, setSelectedVacancyId] = useState<number | null>(null);
  const [filteredLevels, setFilteredLevels] = useState<InterviewLevel[]>([]);
  const [selectedLevelId, setSelectedLevelId] = useState<number | null>(null);
  const [ranking, setRanking] = useState<
    Array<{ external_user_no: string; totalScore: number }>
  >([]);
  const [isFinalLevel, setIsFinalLevel] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [candidateActions, setCandidateActions] = useState<Record<string, string>>({});
  const [candidateToProceed, setCandidateToProceed] = useState<{
    external_user_no: string;
    totalScore: number;
  } | null>(null);
  const [candidateToDrop, setCandidateToDrop] = useState<{
    external_user_no: string;
    totalScore: number;
  } | null>(null);
  const [candidateToGrantOffer, setCandidateToGrantOffer] = useState<{
    external_user_no: string;
    totalScore: number;
  } | null>(null);
  const [showProceedDialog, setShowProceedDialog] = useState(false);
  const [showDropDialog, setShowDropDialog] = useState(false);
  const [showGrantDialog, setShowGrantDialog] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);

  // State for view modal
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedCandidateForView, setSelectedCandidateForView] = useState<{
    external_user_no: string;
    ratings: InterviewResult[];
  } | null>(null);

  // Fetch main data (vacancies, levels, results, applications, templates, bio)
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [vacRes, lvlRes, resRes, appRes, tmplRes, bioRes] = await Promise.all([
          fetch(`${BASE_URL}/hrm/vacancies`),
          fetch(`${BASE_URL}/recruitment/interview-levels`),
          fetch(`${BASE_URL}/recruitment/interview-results`),
          fetch(`${BASE_URL}/recruitment/vacancy-application`),
          fetch(`${BASE_URL}/recruitment/offer-letter-templates`),
          fetch(`${BASE_URL}/recruitment/external-user-bio-data`),
        ]);

        if (!vacRes.ok) throw new Error("Failed to fetch vacancies");
        if (!lvlRes.ok) throw new Error("Failed to fetch interview levels");
        if (!resRes.ok) throw new Error("Failed to fetch interview results");
        if (!appRes.ok) throw new Error("Failed to fetch applications");
        if (!bioRes.ok) throw new Error("Failed to fetch external user bio data");

        let tmplData: any[] = [];
        try {
          tmplData = await tmplRes.json();
        } catch {
          tmplData = [];
        }

        const vacData: JobVacancy[] = await vacRes.json();
        const lvlData: InterviewLevel[] = await lvlRes.json();
        const resultsData: InterviewResult[] = await resRes.json();
        const appData: VacancyApplication[] = await appRes.json();
        const bioDataJson: ExternalUserBioData[] = await bioRes.json();

        // Build a map from external_user_no to candidate full name
        const nameMap: Record<string, string> = {};
        bioDataJson.forEach((b) => {
          const fullName = `${b.first_name} ${b.middle_name ?? ""} ${b.last_name}`.trim();
          nameMap[b.external_user_no] = fullName;
        });

        setVacancies(vacData);
        setAllLevels(lvlData);
        setAllResults(resultsData);
        setApplications(appData);
        setOfferTemplates(tmplData || []);
        setBioData(bioDataJson);
        setCandidateNameMap(nameMap);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  // Filter interview levels by vacancy
  useEffect(() => {
    if (!selectedVacancyId) {
      setFilteredLevels([]);
      setSelectedLevelId(null);
      return;
    }
    const lvls = allLevels.filter((lvl) => lvl.vacancy === selectedVacancyId);
    console.log(`Filtered ${lvls.length} levels for vacancy ${selectedVacancyId}`);
    setFilteredLevels(lvls);
    setSelectedLevelId(null);
  }, [selectedVacancyId, allLevels]);

  // Compute ranking using average instead of sum
  useEffect(() => {
    if (!selectedVacancyId || !selectedLevelId) {
      setRanking([]);
      setIsFinalLevel(false);
      return;
    }
    const allLvlsForVac = allLevels.filter((l) => l.vacancy === selectedVacancyId);
    const maxLvlNo = Math.max(...allLvlsForVac.map((l) => l.interview_level_no));
    const selectedLvl = allLvlsForVac.find((l) => l.id === selectedLevelId);
    setIsFinalLevel(selectedLvl?.interview_level_no === maxLvlNo);

    const resultsForLevel = allResults.filter((r) => r.interview_level === selectedLevelId);
    const relevantResults = resultsForLevel.filter((r) => {
      const app = applications.find((a) => a.id === r.vacancy_application);
      return app && app.vacancy === selectedVacancyId;
    });

    const candidateMap: Record<string, { sum: number; count: number }> = {};
    for (const res of relevantResults) {
      if (res.question_score !== null && res.question_score !== undefined) {
        if (!candidateMap[res.external_user_no]) {
          candidateMap[res.external_user_no] = { sum: 0, count: 0 };
        }
        candidateMap[res.external_user_no].sum += res.question_score;
        candidateMap[res.external_user_no].count += 1;
      }
    }

    const newRanking = Object.entries(candidateMap).map(([extNo, data]) => ({
      external_user_no: extNo,
      totalScore: data.count > 0 ? data.sum / data.count : 0,
    }));

    const levelsForVacancy = allLevels.filter((lvl) => lvl.vacancy === selectedVacancyId);
    levelsForVacancy.sort((a, b) => a.id - b.id);
    const firstLevelId = levelsForVacancy.length ? levelsForVacancy[0].id : null;
    console.log(`First level for vacancy ${selectedVacancyId} is ${firstLevelId}`);

    if (selectedLevelId === firstLevelId) {
      const shortlistedCandidates = applications.filter(
        (a) =>
          a.vacancy === selectedVacancyId &&
          a.application_status === "shortlisted" &&
          !candidateMap.hasOwnProperty(a.external_user_no)
      );
       console.log(
        `Adding ${shortlistedCandidates.length} shortlisted candidate(s) for first level ${firstLevelId}`
      );
      shortlistedCandidates.forEach((cand) => {
        newRanking.push({
          external_user_no: cand.external_user_no,
          totalScore: 0,
        });
      });
    }
    newRanking.sort((a, b) => b.totalScore - a.totalScore);
    setRanking(newRanking);
  }, [selectedVacancyId, selectedLevelId, allResults, applications, allLevels]);

  /**
   * View Modal logic to display candidate ratings and comments
   */
  const handleOpenViewModal = (candidate: { external_user_no: string; totalScore: number }) => {
    const candidateRatings = allResults.filter(
      (r) => r.external_user_no === candidate.external_user_no && r.interview_level === selectedLevelId
    );
    setSelectedCandidateForView({ external_user_no: candidate.external_user_no, ratings: candidateRatings });
    setViewModalOpen(true);
  };

  /**
   * PROCEED / DROP / GRANT OFFER logic
   */
  const handleOpenProceedDialog = (candidate: { external_user_no: string; totalScore: number }) => {
    setCandidateToProceed(candidate);
    setShowProceedDialog(true);
  };

  const handleConfirmProceed = async () => {
    if (!selectedLevelId || !selectedVacancyId || !candidateToProceed) {
      setShowProceedDialog(false);
      return;
    }
    try {
      // Mark the current level as "Passed"
      await fetch(`${BASE_URL}/recruitment/interview-levels/${selectedLevelId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ level_status: "Passed" }),
      });
      // Insert record in next level, if any
      const currentLvl = allLevels.find((lvl) => lvl.id === selectedLevelId);
      if (currentLvl) {
        const nextLevelNo = currentLvl.interview_level_no + 1;
        const nextLvl = allLevels.find(
          (lvl) => lvl.vacancy === selectedVacancyId && lvl.interview_level_no === nextLevelNo
        );
        if (nextLvl) {
          const candidateApp = applications.find(
            (a) =>
              a.external_user_no === candidateToProceed.external_user_no &&
              a.vacancy === selectedVacancyId
          );
          if (candidateApp) {
            await fetch(`${BASE_URL}/recruitment/interview-results`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                question_score: null,
                total_level_score: null,
                external_user_no: candidateToProceed.external_user_no,
                vacancy_application: candidateApp.id,
                interview_level: nextLvl.id,
                interview_Questions: null,
                panelist_employee_no: user?.employee_no,
              }),
            });
          }
        }
      }
      setCandidateActions((prev) => ({
        ...prev,
        [candidateToProceed.external_user_no]: "Passed",
      }));
      setShowProceedDialog(false);
      setCandidateToProceed(null);
      await refreshData();
    } catch (err: any) {
      console.error("Error proceeding candidate:", err);
    }
  };

  const handleOpenDropDialog = (candidate: { external_user_no: string; totalScore: number }) => {
    setCandidateToDrop(candidate);
    setShowDropDialog(true);
  };

  const handleConfirmDrop = async () => {
    if (!selectedLevelId) {
      setShowDropDialog(false);
      return;
    }
    try {
      await fetch(`${BASE_URL}/recruitment/interview-levels/${selectedLevelId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ level_status: "Failed" }),
      });
      if (candidateToDrop) {
        setCandidateActions((prev) => ({
          ...prev,
          [candidateToDrop.external_user_no]: "Failed",
        }));
      }
      setShowDropDialog(false);
      setCandidateToDrop(null);
      await refreshData();
    } catch (err: any) {
      console.error("Error dropping candidate:", err);
    }
  };

  const handleOpenGrantOfferDialog = (candidate: { external_user_no: string; totalScore: number }) => {
    setCandidateToGrantOffer(candidate);
    setShowGrantDialog(true);
  };

  const handleConfirmGrantOffer = async () => {
    if (!selectedLevelId || !selectedVacancyId || !candidateToGrantOffer || !selectedTemplateId) {
      setShowGrantDialog(false);
      return;
    }
    try {
      // Mark final level as "Passed"
      await fetch(`${BASE_URL}/recruitment/interview-levels/${selectedLevelId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ level_status: "Passed" }),
      });
      // Find the candidate application record
      const candidateApp = applications.find(
        (a) =>
          a.external_user_no === candidateToGrantOffer.external_user_no &&
          a.vacancy === selectedVacancyId
      );
      if (!candidateApp) {
        console.error("Candidate application not found for candidate:", candidateToGrantOffer);
        setShowGrantDialog(false);
        return;
      }
      // Build the offer letter creation payload.
      // NOTE: We now send "offer_letter_id" instead of "template"
      const payload = {
        status: "Draft",
        external_user_no: candidateToGrantOffer.external_user_no,
        vacancy_application: candidateApp.id,
        offer_letter_id: selectedTemplateId, // using the selected template's id
        date_created: new Date().toISOString(),
      };
      console.log("Grant Offer Payload:", payload);
      const createResp = await fetch(`${BASE_URL}/recruitment/offer-letter`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });
      if (!createResp.ok) {
        const errorText = await createResp.text();
        console.error("Offer letter creation failed:", createResp.status, errorText);
        throw new Error("Failed to create offer letter.");
      }
      const newOfferLetter = await createResp.json();
      console.log("Created Offer Letter:", newOfferLetter);
      // Log the offer letter file URL for debugging
      if (newOfferLetter && newOfferLetter.offer_letter_file) {
        console.log("Offer letter file URL:", newOfferLetter.offer_letter_file);
        // Trigger download of the generated offer letter.
        const link = document.createElement("a");
        link.href = newOfferLetter.offer_letter_file;
        link.download = `OfferLetter_${candidateToGrantOffer.external_user_no}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        console.warn("No offer letter file found in response.");
      }
      setCandidateActions((prev) => ({
        ...prev,
        [candidateToGrantOffer.external_user_no]: "Offer Granted",
      }));
      setShowGrantDialog(false);
      setCandidateToGrantOffer(null);
      await refreshData();
    } catch (err: any) {
      console.error("Error granting offer letter:", err);
    }
  };

  // Refresh data after actions
  const refreshData = async () => {
    try {
      const [lvlRes, resRes] = await Promise.all([
        fetch(`${BASE_URL}/recruitment/interview-levels`),
        fetch(`${BASE_URL}/recruitment/interview-results`),
      ]);
      if (lvlRes.ok && resRes.ok) {
        const lvlData: InterviewLevel[] = await lvlRes.json();
        const resultsData: InterviewResult[] = await resRes.json();
        setAllLevels(lvlData);
        setAllResults(resultsData);
      }
    } catch (err) {
      console.error("Error refreshing data after action:", err);
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Interview Results</h2>
      {error && <p className="text-red-500">{error}</p>}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex-1">
          <Label>Select Vacancy</Label>
          <Select
            onValueChange={(val) => setSelectedVacancyId(Number(val))}
            value={selectedVacancyId ? selectedVacancyId.toString() : ""}
          >
            <SelectTrigger className="w-full mt-1">
              <SelectValue placeholder="Choose a Vacancy" />
            </SelectTrigger>
            <SelectContent>
              {vacancies.map((v) => (
                <SelectItem key={v.id} value={v.id.toString()}>
                  {v.job_details.job_title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex-1">
          <Label>Select Interview Level</Label>
          <Select
            onValueChange={(val) => setSelectedLevelId(Number(val))}
            value={selectedLevelId ? selectedLevelId.toString() : ""}
          >
            <SelectTrigger className="w-full mt-1">
              <SelectValue placeholder="Choose a Level" />
            </SelectTrigger>
            <SelectContent>
              {filteredLevels.map((lvl) => (
                <SelectItem key={lvl.id} value={lvl.id.toString()}>
                  {lvl.interview_level}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {loading ? (
        <p>Loading...</p>
      ) : ranking.length > 0 ? (
        <div className="mt-6">
          <h3 className="font-semibold mb-2">
            Ranking (Descending by Average Score) {isFinalLevel && "(Final Level)"}
          </h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>#</TableHead>
                <TableHead>Candidate</TableHead>
                <TableHead>Average Score</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {ranking.map((r, index) => {
                const displayName = candidateNameMap[r.external_user_no] || r.external_user_no;
                return (
                  <TableRow key={r.external_user_no}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{displayName}</TableCell>
                    <TableCell>{r.totalScore.toFixed(2)}</TableCell>
                    <TableCell className="text-right space-x-2">
                      <Button variant="secondary" size="sm" onClick={() => handleOpenViewModal(r)}>
                        View
                      </Button>
                      {candidateActions[r.external_user_no] ? (
                        <Badge variant="secondary">{candidateActions[r.external_user_no]}</Badge>
                      ) : isFinalLevel ? (
                        <>
                          <Button variant="default" size="sm" onClick={() => handleOpenGrantOfferDialog(r)}>
                            Grant Offer
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => handleOpenDropDialog(r)}>
                            Drop
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button variant="default" size="sm" onClick={() => handleOpenProceedDialog(r)}>
                            Proceed
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => handleOpenDropDialog(r)}>
                            Drop
                          </Button>
                        </>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      ) : (
        <p>No interview results to display.</p>
      )}

      {/* Proceed Dialog */}
      <AlertDialog open={showProceedDialog} onOpenChange={setShowProceedDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Proceed Candidate?</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to mark the current interview level as <b>Passed</b>. Are you sure?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setShowProceedDialog(false);
                setCandidateToProceed(null);
              }}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmProceed}>
              Proceed
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Drop Dialog */}
      <AlertDialog open={showDropDialog} onOpenChange={setShowDropDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Drop Candidate?</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to mark the current interview level as <b>Failed</b>. Are you sure?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setShowDropDialog(false);
                setCandidateToDrop(null);
              }}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDrop}>Drop</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Grant Offer Dialog */}
      <AlertDialog open={showGrantDialog} onOpenChange={setShowGrantDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Grant Offer Letter?</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to mark this final interview level as <b>Passed</b> and create an offer letter.
              The selected offer template will be used to generate the offer letter, which will then be sent
              to and downloaded by the candidate. Are you sure?
            </AlertDialogDescription>
          </AlertDialogHeader>
          {offerTemplates.length > 0 && (
            <div className="p-3 border rounded mt-2">
              <Label className="block mb-1">Select Offer Template</Label>
              <Select
                value={selectedTemplateId ? selectedTemplateId.toString() : ""}
                onValueChange={(val) => {
                  console.log("Selected template id:", val);
                  setSelectedTemplateId(Number(val));
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pick a template" />
                </SelectTrigger>
                <SelectContent>
                  {offerTemplates.map((t) => (
                    <SelectItem key={t.id} value={t.id.toString()}>
                      {t.template_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setShowGrantDialog(false);
                setCandidateToGrantOffer(null);
              }}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmGrantOffer}>
              Grant Offer
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Modal for Candidate Ratings */}
      <AlertDialog open={viewModalOpen} onOpenChange={setViewModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Candidate Ratings</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedCandidateForView &&
                `Ratings and comments for ${
                  candidateNameMap[selectedCandidateForView.external_user_no] || selectedCandidateForView.external_user_no
                }`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="mt-4">
            {selectedCandidateForView && selectedCandidateForView.ratings.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Panelist</TableHead>
                    <TableHead>Question Score</TableHead>
                    <TableHead>Comments</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {selectedCandidateForView.ratings.map((rating) => (
                    <TableRow key={rating.id}>
                      <TableCell>{rating.panelist_employee_no}</TableCell>
                      <TableCell>{rating.question_score}</TableCell>
                      <TableCell>{rating.comments}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p>No ratings available.</p>
            )}
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setViewModalOpen(false);
                setSelectedCandidateForView(null);
              }}
            >
              Close
            </AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default InterviewResultsTab;
