import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { useToast } from '@/hooks/use-toast';
import { Loader2, AlertCircle, Pencil } from 'lucide-react';

type KPI = {
  id: string;
  name: string;
  description: string;
  target: number;
  current_performance: number; // Additional field for performance tracking
};

type AppraisalPeriodDetails = {
  id: number;
  name: string;
  start_date: string;
  end_date: string;
  status: string;
  cycle_type: string;
  description: string;
  comments: string;
  department: string; // Department linked to the period
  kpis: KPI[]; // Linked KPIs
  employees: number; // Number of employees appraised in this period
};

export const AppraisalDetails: React.FC<{ id: number }> = ({ id }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  const [appraisalDetails, setAppraisalDetails] = useState<AppraisalPeriodDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchAppraisalDetails = async () => {
      try {
        // Fetch appraisal period details
        const res = await fetch(`${BASE_URL}/hrm/appraisal-periods/${id}`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!res.ok) throw new Error('Failed to fetch appraisal period details');
        const periodData = await res.json();

        // Fetch KPIs linked to the appraisal period
        const kpiRes = await fetch(`${BASE_URL}/hrm/kpi-to-employee?appraisal_period_id=${id}`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!kpiRes.ok) throw new Error('Failed to fetch KPIs for the period');
        const kpis = await kpiRes.json();

        setAppraisalDetails({
          ...periodData,
          kpis: kpis || [],
        });
      } catch (err: any) {
        setError(err.message || 'Unable to load appraisal period details');
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load appraisal period details.',
        });
      } finally {
        setLoading(false);
      }
    };
    fetchAppraisalDetails();
  }, [id, token, toast]);

  const calculateProgress = () => {
    if (!appraisalDetails) return 0;

    const start = new Date(appraisalDetails.start_date).getTime();
    const end = new Date(appraisalDetails.end_date).getTime();
    const now = Date.now();

    if (now < start) return 0;
    if (now > end) return 100;

    return ((now - start) / (end - start)) * 100;
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <h1 className="text-2xl font-bold text-gray-800">Appraisal Period Details</h1>

      {loading ? (
        <div className="flex items-center justify-center">
          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
          <span>Loading appraisal period details...</span>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center text-red-500">
          <AlertCircle className="mb-2" size={32} />
          <p>{error}</p>
        </div>
      ) : !appraisalDetails ? (
        <div className="flex flex-col items-center text-gray-500">
          <AlertCircle className="mb-2" size={32} />
          <p>No details available for this appraisal period.</p>
        </div>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">{appraisalDetails.name}</h2>
            <div className="text-sm text-gray-500">
              <p><strong>Department:</strong> {appraisalDetails.department}</p>
              <p><strong>Employees Appraised:</strong> {appraisalDetails.employees}</p>
            </div>
          </div>

          <div className="mb-4 text-sm text-gray-600">
            <p><strong>Start Date:</strong> {appraisalDetails.start_date}</p>
            <p><strong>End Date:</strong> {appraisalDetails.end_date}</p>
            <p><strong>Status:</strong> {appraisalDetails.status}</p>
            <p className="mt-4"><strong>Description:</strong> {appraisalDetails.description}</p>
            <p className="mt-4"><strong>Comments:</strong> {appraisalDetails.comments || 'No comments available.'}</p>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold text-gray-800">Linked KPIs</h3>
            <ul className="space-y-4 mt-4">
              {appraisalDetails.kpis.length === 0 ? (
                <p className="text-sm text-gray-500">No KPIs linked to this period yet.</p>
              ) : (
                appraisalDetails.kpis.map((kpi) => (
                  <li key={kpi.id} className="border-b border-gray-200 pb-4">
                    <h4 className="font-medium">{kpi.name}</h4>
                    <p className="text-sm text-gray-600">{kpi.description}</p>
                    <p className="text-sm text-gray-600">Target: {kpi.target}</p>
                    <p className="text-sm text-gray-600">Current Performance: {kpi.current_performance || 'N/A'}</p>
                  </li>
                ))
              )}
            </ul>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold text-gray-800">Appraisal Progress</h3>
            <div className="w-full h-2 bg-gray-300 rounded-full overflow-hidden mb-2">
              <div
                className="h-full bg-green-500"
                style={{ width: `${calculateProgress()}%` }}
              />
            </div>
            <p className="text-sm text-gray-500">Progress: {Math.round(calculateProgress())}%</p>
          </div>

          <button className="mt-4 inline-flex items-center gap-2 rounded bg-green-600 px-4 py-2 text-white hover:bg-green-700">
            <Pencil className="h-5 w-5" />
            Edit Period
          </button>
        </div>
      )}
    </div>
  );
};
