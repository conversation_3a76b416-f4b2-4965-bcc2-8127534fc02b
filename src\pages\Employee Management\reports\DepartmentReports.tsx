"use client";

import { useEffect, useState, use<PERSON><PERSON>back } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useNavigate } from "react-router-dom";
import { BASE_URL } from "@/config";
import { useDebounce } from "@/hooks/use-debounce";

// Icons
import {
  Search,
  Filter,
  X,
  Download,
  RefreshCw,
  User,
  Briefcase,
  Building,
  FileText,
  Users
} from "lucide-react";

// Shadcn/UI components
import { Screen } from "@/app-components/layout/screen";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>rum<PERSON>,
  <PERSON><PERSON>crumb<PERSON><PERSON>,
  <PERSON>readcrumbItem,
  BreadcrumbLink,
  <PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";

interface Department {
  id: number;
  name: string;
  description: string;
  dep_head: string;
  dep_head_assistant: string;
  dep_hr: string;
  department_status_active: boolean;
  organisation: number;
  parent_department: number | null;
}

interface EmployeeBio {
  id: number;
  first_name: string;
  middle_name: string | null;
  last_name: string;
  employee_no: string;
  gender: string;
  marital_status: string;
  date_of_birth: string;
}

interface EmployeeJobInfo {
  id: number;
  employee_no: string;
  job_title: string;
  category: string;
  directorate: string;
  work_location: string;
  business_unit: string;
  department: number;
}

interface DepartmentReportData {
  employee_no: string;
  full_name: string;
  job_title: string;
  category: string;
  directorate: string;
  work_location: string;
  business_unit: string;
  department_name: string;
  department_head: string;
  department_assistant: string;
  gender: string;
  marital_status: string;
}

interface DepartmentReportsFilters {
  department?: string;
  search?: string;
  job_title?: string;
  category?: string;
  directorate?: string;
  work_location?: string;
  ordering?: string;
}

// Badge variants for filter pills
const BADGE_VARIANTS = [
  "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100",
  "bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100",
  "bg-yellow-100 text-yellow-900 dark:bg-yellow-900 dark:text-yellow-100",
  "bg-red-100 text-red-900 dark:bg-red-900 dark:text-red-100",
  "bg-purple-100 text-purple-900 dark:bg-purple-900 dark:text-purple-100",
  "bg-pink-100 text-pink-900 dark:bg-pink-900 dark:text-pink-100",
];

export default function DepartmentReports() {
  const { token } = useSelector((s: RootState) => s.auth);
  const navigate = useNavigate();

  // State
  const [filters, setFilters] = useState<DepartmentReportsFilters>({});
  const [departments, setDepartments] = useState<Department[]>([]);
  const [reportData, setReportData] = useState<DepartmentReportData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);

  // Debounce the filters to prevent excessive API calls
  const debouncedFilters = useDebounce(filters, 500);

  // Fetch departments on component mount
  useEffect(() => {
    const fetchDepartments = async () => {
      if (!token) return;
      try {
        const res = await fetch(`${BASE_URL}/users/departments`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        });
        if (res.ok) {
          const data = await res.json();
          setDepartments(data);
        }
      } catch (e) {
        // Error fetching departments - silently handled
      }
    };
    fetchDepartments();
  }, [token]);

  // Fetch department report data
  const fetchDepartmentReport = useCallback(async (filterParams: DepartmentReportsFilters) => {
    if (!token) return setError("Missing token. Please log in.");
    setLoading(true);
    setError(null);
    try {
      // Fetch employee bio details and job info in parallel
      const [bioRes, jobRes] = await Promise.all([
        fetch(`${BASE_URL}/users/employee-bio-details${filterParams.search ? `?search=${encodeURIComponent(filterParams.search)}` : ""}`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        }),
        fetch(`${BASE_URL}/users/employee-job-info-details`, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Token ${token}`,
          },
        }),
      ]);

      if (!bioRes.ok || !jobRes.ok) {
        throw new Error("Failed to fetch employee data");
      }

      const [bioData, jobData]: [EmployeeBio[], EmployeeJobInfo[]] = await Promise.all([
        bioRes.json(),
        jobRes.json(),
      ]);

      // Create a map of departments for quick lookup
      const deptMap = new Map(departments.map(d => [d.id, d]));

      // Combine bio and job data
      const combinedData: DepartmentReportData[] = bioData
        .map(bio => {
          const jobInfo = jobData.find(job => job.employee_no === bio.employee_no);
          if (!jobInfo) return null;

          const dept = deptMap.get(jobInfo.department);
          if (!dept) return null;

          return {
            employee_no: bio.employee_no,
            full_name: `${bio.first_name}${bio.middle_name ? ` ${bio.middle_name}` : ""} ${bio.last_name}`,
            job_title: jobInfo.job_title || "",
            category: jobInfo.category || "",
            directorate: jobInfo.directorate || "",
            work_location: jobInfo.work_location || "",
            business_unit: jobInfo.business_unit || "",
            department_name: dept.name,
            department_head: dept.dep_head || "",
            department_assistant: dept.dep_head_assistant || "",
            gender: bio.gender || "",
            marital_status: bio.marital_status || "",
          };
        })
        .filter(Boolean) as DepartmentReportData[];

      // Apply filters
      let filteredData = combinedData;

      if (filterParams.department && filterParams.department !== "all") {
        filteredData = filteredData.filter(item => item.department_name === filterParams.department);
      }

      if (filterParams.job_title) {
        filteredData = filteredData.filter(item => 
          item.job_title.toLowerCase().includes(filterParams.job_title!.toLowerCase())
        );
      }

      if (filterParams.category) {
        filteredData = filteredData.filter(item => 
          item.category.toLowerCase().includes(filterParams.category!.toLowerCase())
        );
      }

      if (filterParams.directorate) {
        filteredData = filteredData.filter(item => 
          item.directorate.toLowerCase().includes(filterParams.directorate!.toLowerCase())
        );
      }

      if (filterParams.work_location) {
        filteredData = filteredData.filter(item => 
          item.work_location.toLowerCase().includes(filterParams.work_location!.toLowerCase())
        );
      }

      setReportData(filteredData);
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  }, [token, departments]);

  // Auto-fetch when debounced filters change
  useEffect(() => {
    if (Object.keys(debouncedFilters).length > 0 && departments.length > 0) {
      fetchDepartmentReport(debouncedFilters);
    }
  }, [debouncedFilters, fetchDepartmentReport, departments]);

  // Filter management functions
  const setFilterValue = useCallback((k: keyof DepartmentReportsFilters, v?: string) => {
    setFilters((prev) => ({ ...prev, [k]: v || "" }));
  }, []);

  const removeFilter = useCallback((k: keyof DepartmentReportsFilters) => {
    setFilters((prev) => {
      const copy = { ...prev };
      delete copy[k];
      return copy;
    });
  }, []);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setFilters({});
    setReportData([]);
    setError(null);
  }, []);

  // Export data to CSV (mock function)
  const handleExportData = useCallback(() => {
    if (reportData.length === 0) return;

    setIsExporting(true);
    setTimeout(() => {
      setIsExporting(false);
      // In a real implementation, you would generate and download a CSV here
      alert("Export functionality would download a CSV file in a real implementation");
    }, 1000);
  }, [reportData]);

  return (
    <Screen>
      {/* Header with breadcrumb and title */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <Breadcrumb className="mb-2">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/employees-list">Employees</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Department Reports</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <h1 className="text-2xl font-bold tracking-tight">Department Reports</h1>
          <p className="text-muted-foreground">View department members and their roles</p>
        </div>

        <div className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                  className="h-9"
                >
                  <RefreshCw size={16} className="mr-1" />
                  Reset
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Clear all filters</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  onClick={handleExportData}
                  disabled={reportData.length === 0 || isExporting}
                  className="h-9"
                >
                  {isExporting ? (
                    <RefreshCw size={16} className="mr-1 animate-spin" />
                  ) : (
                    <Download size={16} className="mr-1" />
                  )}
                  Export
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export data to CSV</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            onClick={() => navigate("/employees-list")}
            className="h-9"
          >
            Back to Employees
          </Button>
        </div>
      </div>

      {/* Main content area */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters panel - 1 column on mobile, 1/4 on large screens */}
        <Card className="lg:col-span-1 h-fit">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Filters</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="h-8 px-2 text-muted-foreground"
              >
                <X size={16} className="mr-1" />
                Clear all
              </Button>
            </div>
            <CardDescription>
              Filters apply automatically
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-5">
            {/* Active filters */}
            {Object.entries(filters).filter(([_, v]) => !!v).length > 0 && (
              <div className="space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Active Filters</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(filters)
                    .filter(([_, v]) => !!v)
                    .map(([k, v], i) => (
                      <Badge
                        key={k}
                        className={`${BADGE_VARIANTS[i % BADGE_VARIANTS.length]} cursor-pointer transition-all hover:opacity-80`}
                        onClick={() => removeFilter(k as any)}
                      >
                        {k.replace(/_/g, ' ')}: {v}
                        <X size={12} className="ml-1" />
                      </Badge>
                    ))}
                </div>
              </div>
            )}

            {/* Search */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by employee name..."
                  value={filters.search || ""}
                  onChange={(e) => setFilterValue("search", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Department filter */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Department</Label>
              <Select
                value={filters.department || "all"}
                onValueChange={(value: string) => setFilterValue("department", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All departments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All departments</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.name}>{dept.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Job Title */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Job Title</Label>
              <div className="relative">
                <Briefcase className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Filter by job title"
                  value={filters.job_title || ""}
                  onChange={(e) => setFilterValue("job_title", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Category</Label>
              <div className="relative">
                <Filter className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Filter by category"
                  value={filters.category || ""}
                  onChange={(e) => setFilterValue("category", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Directorate */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Directorate</Label>
              <div className="relative">
                <Building className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Filter by directorate"
                  value={filters.directorate || ""}
                  onChange={(e) => setFilterValue("directorate", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Work Location */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Work Location</Label>
              <div className="relative">
                <Building className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Filter by location"
                  value={filters.work_location || ""}
                  onChange={(e) => setFilterValue("work_location", e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results panel - 1 column on mobile, 3/4 on large screens */}
        <Card className="lg:col-span-3">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">Department Report</CardTitle>
              <Badge variant="outline" className="font-normal">
                {loading ? (
                  <span className="flex items-center">
                    <RefreshCw size={12} className="mr-1 animate-spin" /> Loading...
                  </span>
                ) : reportData.length > 0 ? (
                  `${reportData.length} employees found`
                ) : (
                  "No results"
                )}
              </Badge>
            </div>
            <CardDescription>
              {Object.keys(filters).length > 0
                ? "Showing filtered results"
                : "Apply filters to see department data"}
            </CardDescription>
          </CardHeader>

          <CardContent>
            {error && (
              <div className="mb-4 p-3 bg-destructive/10 text-destructive rounded-md">
                <p className="text-sm">{error}</p>
              </div>
            )}

            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="font-medium">Employee ID</TableHead>
                      <TableHead className="font-medium">Name</TableHead>
                      <TableHead className="font-medium">Department</TableHead>
                      <TableHead className="font-medium">Job Title</TableHead>
                      <TableHead className="font-medium">Category</TableHead>
                      <TableHead className="font-medium">Directorate</TableHead>
                      <TableHead className="font-medium">Location</TableHead>
                      <TableHead className="font-medium">Dept. Head</TableHead>
                      <TableHead className="font-medium">Dept. Assistant</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      // Loading skeleton
                      Array(5).fill(0).map((_, i) => (
                        <TableRow key={i}>
                          {Array(9).fill(0).map((_, j) => (
                            <TableCell key={j}>
                              <Skeleton className="h-4 w-full" />
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : reportData.length > 0 ? (
                      // Department data
                      reportData.map((emp) => (
                        <TableRow key={emp.employee_no} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{emp.employee_no}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <User size={16} className="mr-2 text-muted-foreground" />
                              {emp.full_name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Building size={14} className="mr-1 text-muted-foreground" />
                              {emp.department_name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Briefcase size={14} className="mr-1 text-muted-foreground" />
                              {emp.job_title || "-"}
                            </div>
                          </TableCell>
                          <TableCell>{emp.category || "-"}</TableCell>
                          <TableCell>{emp.directorate || "-"}</TableCell>
                          <TableCell>{emp.work_location || "-"}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users size={14} className="mr-1 text-muted-foreground" />
                              {emp.department_head || "-"}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users size={14} className="mr-1 text-muted-foreground" />
                              {emp.department_assistant || "-"}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      // No results
                      <TableRow>
                        <TableCell colSpan={9} className="h-24 text-center">
                          <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <FileText size={24} className="mb-2" />
                            <p>No department records found</p>
                            <p className="text-sm">Try adjusting your filters</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between border-t p-4">
            <p className="text-sm text-muted-foreground">
              {reportData.length > 0 && "Showing all available results"}
            </p>
          </CardFooter>
        </Card>
      </div>
    </Screen>
  );
}
