import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from "../../redux/store";
import { BASE_URL } from "../../config";
import { useToast } from  "../../hooks/use-toast";
import {
  AlertCircle,
  Loader2,
  Check,
} from 'lucide-react';

// Data models for HR
interface EmployeeAppraisalRecord {
  id: number;
  status: string;
  employee_acceptance: boolean;
  supervisor_acceptance: boolean;
  total_emp_self_rating_score: number;
  total_supervisor_rating_score: number;
  final_hr_comments: string;
  way_forward: string;
  AppraisalPeriod: number;
  employeeid: string;
  supervisor: string;
}

export const HRAppraisalSection: React.FC = () => {
  const { token, user } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  const [employees, setEmployees] = useState<EmployeeAppraisalRecord[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<EmployeeAppraisalRecord | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch all employees for the HR department
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setLoading(true);
        setError('');
        const res = await fetch(`${BASE_URL}/hrm/appraisals/department/${user?.userOrgData?.department_id}`, {
          headers: { Authorization: `Token ${token}` },
        });
        if (!res.ok) throw new Error('Failed to fetch appraisals');
        const data = await res.json();
        setEmployees(data);
      } catch (err: any) {
        setError(err.message || 'Could not load employee appraisals.');
      } finally {
        setLoading(false);
      }
    };
    fetchEmployees();
  }, [token, user]);

  // Update HR comments and way forward
  const handleWayForwardChange = async (appraisalId: number, wayForward: string) => {
    try {
      const payload = {
        way_forward: wayForward,
      };

      const res = await fetch(`${BASE_URL}/hrm/employee-appraisals/${appraisalId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Token ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!res.ok) {
        throw new Error('Failed to update HR comments');
      }

      const updatedRecord = await res.json();
      setEmployees((prev) =>
        prev.map((employee) =>
          employee.id === appraisalId ? { ...employee, way_forward: updatedRecord.way_forward } : employee
        )
      );
      toast({
        variant: 'default',
        title: 'Success',
        description: 'Way forward updated successfully!',
      });
    } catch (err: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Could not update the way forward.',
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <h1 className="mb-4 text-2xl font-bold text-gray-800">HR Appraisal Dashboard</h1>

      {loading ? (
        <div className="flex items-center gap-2 text-gray-500">
          <Loader2 className="h-5 w-5 animate-spin" />
          Loading appraisals...
        </div>
      ) : error ? (
        <div className="mt-2 flex flex-col items-center text-red-500">
          <AlertCircle className="mb-1" />
          <p>{error}</p>
        </div>
      ) : (
        <div className="space-y-4">
          {employees.map((appraisal) => (
            <div key={appraisal.id} className="rounded border bg-white p-4 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-700">
                {appraisal.employeeid} - {appraisal.supervisor} - Status: {appraisal.status}
              </h3>
              <div className="mt-2 flex justify-between">
                <p>
                  Supervisor Rating: {appraisal.total_supervisor_rating_score} / 100
                </p>
                <p>
                  Employee Rating: {appraisal.total_emp_self_rating_score} / 100
                </p>
              </div>

              {/* Way forward and HR Comments */}
              <div className="mt-4">
                <textarea
                  value={appraisal.way_forward}
                  onChange={(e) => handleWayForwardChange(appraisal.id, e.target.value)}
                  className="w-full rounded border p-2 text-sm"
                  rows={4}
                  placeholder="Enter way forward or PIP comments..."
                />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};


