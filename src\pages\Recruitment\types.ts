// types.ts
export type VacancyApplication = {
    id: number;
    application_status: string;
    external_user_no: string;
    vacancy: number;
    applicant_academic: number;
    applicant_professional_certifications: number;
    applicant_referees: number;
    applicant_experience: number;
  };
  
  export type JobVacancy = {
    id: number;
    date_created: string;
    vacancy_start_date: string | null;
    vacancy_end_date: string | null;
    vacanc_status: string;
    created_at: string;
    publish?: string;
    custom_requirements?: string;
    job: string;
    created_by: string;
    job_details: {
      id: number;
      job_code: string;
      job_title: string;
      job_description: string;
      job_responsibilities: string;
      job_requirements: string;
      job_qualifications: string;
      job_experience: string;
      job_skills: string;
      job_min_salary: number;
      job_max_salary: number;
      posission_status: string;
      no_of_employees: number;
      required_proffesional_body: string;
      accademic_qualification: string;
      professional_requirements: string;
      organogram_level: string;
      organisation: number;
      department: number;
      created_by: string;
    };
  };
  
  export type ExternalUserBioData = {
    id: number;
    first_name: string;
    middle_name: string;
    last_name: string;
    email: string;
    gender?: string;
    marital_status?: string;
    date_of_birth?: string;
    passport_number?: string;
    id_number?: string;
    home_phone_number?: string;
    residential_address?: string;
    city?: string;
    county?: string;
    external_user_no: string;
  };
  
  export type UserAcademic = {
    id: number;
    education_level?: string;
    course_name: string;
    grade_attained?: string;
    start_date: string;
    end_date: string;
    institution: string;
    attachement?: string;
    external_user_no: string;
  };
  
  export type ProfessionalCertification = {
    id: number;
    certification_name: string;
    certification_body: string;
    start_date: string;
    end_date: string;
    attachement?: string;
    external_user_no: string;
  };
  
  export type ApplicantExperience = {
    id: number;
    company_name: string;
    start_date: string;
    end_date: string;
    responsibilities?: string;
    external_user_no: string;
  };
  
  export type ApplicantReferee = {
    id: number;
    name: string;
    contact_number?: string;
    personal_email?: string;
    designation: string;
    external_user_no: string;
  };
  
  export type InterviewLevel = {
    id: number;
    interview_level_no: number;
    interview_level: string;
    interview_total_score: number;
    level_status: string;
    vacancy: number;
  };
  
  export type InterviewQuestion = {
    id: number;
    question: string;
    created_at?: string;
    employee_no: string;
    vacancy: number;
    interview_level: number;
  };
  
  export type InterviewResult = {
    id: number;
    question_score: number | null;
    total_level_score: number | null;
    external_user_no: string;
    vacancy_application: number;
    interview_level: number;
    interview_Questions: number | null;
  };

  export type TalentPoolEntry = {
    id: number;
    date_added: string;     
    date_updated: string;
    status: boolean;
    external_user_no: string;
    job_vacancy: number;
  };
  