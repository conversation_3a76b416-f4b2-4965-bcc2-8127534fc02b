"use client";

import React, { useEffect, useState } from "react";
import { ResponsiveContainer, BarChart, Bar, CartesianGrid, XAxis, YAxis, Tooltip, Cell } from "recharts";

import { VacancyApplication, InterviewLevel, InterviewResult } from "../types";
import { BASE_URL } from "@/config";

interface PipelineBarChartProps {
  vacancyId: number;
}

const PipelineBarChart: React.FC<PipelineBarChartProps> = ({ vacancyId }) => {
  const [applications, setApplications] = useState<VacancyApplication[]>([]);
  const [levels, setLevels] = useState<InterviewLevel[]>([]);
  const [results, setResults] = useState<InterviewResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPipelineData = async () => {
      setLoading(true);
      setError(null);
      try {
        const [appsRes, allLevelsRes, allResultsRes] = await Promise.all([
          fetch(`${BASE_URL}/recruitment/vacancy-application?vacancy=${vacancyId}`, {
            headers: { "Content-Type": "application/json" },
          }),
          fetch(`${BASE_URL}/recruitment/interview-levels`, {
            headers: { "Content-Type": "application/json" },
          }),
          fetch(`${BASE_URL}/recruitment/interview-results`, {
            headers: { "Content-Type": "application/json" },
          }),
        ]);

        if (!appsRes.ok) {
          throw new Error("Failed to fetch applications");
        }
        if (!allLevelsRes.ok) {
          throw new Error("Failed to fetch interview levels");
        }
        if (!allResultsRes.ok) {
          throw new Error("Failed to fetch interview results");
        }

        const appsForVac: VacancyApplication[] = await appsRes.json();
        const allLevels: InterviewLevel[] = await allLevelsRes.json();
        const allResults: InterviewResult[] = await allResultsRes.json();

        const levelsForVac = allLevels.filter((lvl) => lvl.vacancy === vacancyId);

        const resultsForVacancy = allResults.filter((r) => {
          const matchingApp = appsForVac.find((app) => app.id === r.vacancy_application);
          return matchingApp && matchingApp.vacancy === vacancyId;
        });

        setApplications(appsForVac);
        setLevels(levelsForVac);
        setResults(resultsForVacancy);
      } catch (err: any) {
        setError(err.message || "Error fetching pipeline data");
      } finally {
        setLoading(false);
      }
    };

    fetchPipelineData();
  }, [vacancyId]);

  if (loading) return <p>Loading pipeline data...</p>;
  if (error) return <p className="text-red-500">{error}</p>;

  const appliedCount = applications.length;
  const shortlistedCount = applications.filter(
    (app) => app.application_status.toLowerCase() === "shortlisted"
  ).length;

  const sortedLevels = [...levels].sort((a, b) => a.interview_level_no - b.interview_level_no);

  const pipelineData = [
    {
      stage: "Applied",
      value: appliedCount,
      color: "#FFA726",
    },
    {
      stage: "Shortlisted",
      value: shortlistedCount,
      color: "#4CAF50",
    },
  ];

  sortedLevels.forEach((lvl) => {
    const countAtThisLevel = results.filter((r) => r.interview_level === lvl.id).length;
    pipelineData.push({
      stage: lvl.interview_level,
      value: countAtThisLevel,
      color: "#1976D2",
    });
  });

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded shadow">
      <h2 className="text-lg font-semibold mb-4">Pipeline Bar Chart</h2>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={pipelineData}
          layout="vertical"
          margin={{ top: 10, right: 10, left: 40, bottom: 10 }}
        >
          <XAxis type="number" hide />
          <YAxis
            dataKey="stage"
            type="category"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
          />
          <Tooltip cursor={{ fill: "rgba(0,0,0,0.1)" }} />
          <Bar dataKey="value" layout="vertical" radius={5}>
            {pipelineData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PipelineBarChart;
