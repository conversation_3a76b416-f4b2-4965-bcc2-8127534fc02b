"use client";

import React, { useState, useEffect } from "react";
import axios from "axios";
import {
  ArrowLeft,
  UserRound,
  Briefcase,
} from "lucide-react";
import clsx from "clsx";
import toast from "react-hot-toast";
import { RefreshProvider } from "./context/RefreshContext";

// ShadCN/UI components
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>ialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";

// Tab Components
import BioDataTab from "./components/tabs/BioDataTab";
import ContactInfoTab from "./components/tabs/ContactInfoTab";
import ContractInfoTab from "./components/tabs/ContractInfoTab";
import EducationTab from "./components/tabs/EducationTab";
import ImportantDatesTab from "./components/tabs/ImportantDatesTab";
import JobInfoTab from "./components/tabs/JobInfoTab";
import PaymentTab from "./components/tabs/PaymentTab";
import NextOfKinTab from "./components/tabs/NextOfKinTab";
import GuardiansTab from "./components/tabs/GuardiansTab";

import { BASE_URL } from "@/config";

interface EmployeeRecord {
  employee_no: string;
  first_name: string;
  last_name: string;
  gender?: string;
  department_id?: number;
  group_id?: number;
}

interface EmployeeBio {
  gender?: string;
  first_name?: string;
  last_name?: string;
}

interface EmployeeDates {
  date_of_leaveing?: string | null;
}

interface EmployeeJobInfo {
  work_location?: string;
  business_unit?: string;
  department?: number;
}

interface Department {
  id: number;
  name: string;
}

interface Group {
  id: number;
  name: string;
}

interface Props {
  employeeNo: string;
  setView: (val: string | null) => void;
  token: string;
}

export default function EmployeeDetails({ employeeNo, setView, token }: Props) {
  const [activeTab, setActiveTab] = useState("Bio Data");

  // Function to navigate to the next tab after completion
  const navigateToNextTab = (currentTab: string) => {
    const tabOrder = [
      "Bio Data",
      "Contact Info",
      "Contract Info",
      "Education",
      "Important Dates",
      "Job Info",
      "Payment",
      "Next of Kin",
      "Guardians"
    ];

    const currentIndex = tabOrder.indexOf(currentTab);
    if (currentIndex !== -1 && currentIndex < tabOrder.length - 1) {
      const nextTab = tabOrder[currentIndex + 1];
      setActiveTab(nextTab);
      toast.success(`Moving to ${nextTab} tab`);
    }
  };

  // We'll define the tabs inside the return statement to use the refresh context

  // — Header pills data
  const [employee, setEmployee] = useState<EmployeeRecord | null>(null);
  const [employeeBio, setEmployeeBio] = useState<EmployeeBio | null>(null);
  const [employeeDates, setEmployeeDates] = useState<EmployeeDates | null>(null);
  const [employeeJobInfo, setEmployeeJobInfo] = useState<EmployeeJobInfo | null>(null);
  const [departmentsMap, setDepartmentsMap] = useState<Record<number, string>>({});
  const [groupsMap, setGroupsMap] = useState<Record<number, string>>({});
  const [headerLoading, setHeaderLoading] = useState<boolean>(true);

  useEffect(() => {
    if (!token) return;

    async function fetchData() {
      setHeaderLoading(true);
      console.log("🛈 Starting header fetch for", employeeNo);
      try {
        // 1) Top‐level employee
        const empRes = await axios.get(`${BASE_URL}/users/all-employees`, {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        });
        console.log("🛈 all-employees response:", empRes.data);
        if (Array.isArray(empRes.data) && empRes.data.length) {
          setEmployee(empRes.data[0]);
        }

        // 2) Bio
        const bioRes = await axios.get(`${BASE_URL}/users/employee-bio-details`, {
          headers: { Authorization: `Token ${token}` },
          params: { employee_no: employeeNo },
        });
        console.log("🛈 bio response:", bioRes.data);
        if (Array.isArray(bioRes.data) && bioRes.data.length) {
          setEmployeeBio(bioRes.data[0]);
        }

        // 3) Important Dates
        const datesRes = await axios.get(
          `${BASE_URL}/users/employee-important-dates-details`,
          {
            headers: { Authorization: `Token ${token}` },
            params: { employee_no: employeeNo },
          }
        );
        console.log("🛈 dates response:", datesRes.data);
        if (Array.isArray(datesRes.data)) {
          const rec = datesRes.data.find((r: any) => r.employee_no === employeeNo);
          console.log("🛈 found date record:", rec);
          if (rec) setEmployeeDates(rec);
        }

        // 4) Job Info
        const jobRes = await axios.get(
          `${BASE_URL}/users/employee-job-info-details`,
          {
            headers: { Authorization: `Token ${token}` },
            params: { employee_no: employeeNo },
          }
        );
        console.log("🛈 job-info response:", jobRes.data);
        if (Array.isArray(jobRes.data)) {
          const rec = jobRes.data.find((r: any) => r.employee_no === employeeNo);
          console.log("🛈 found job record:", rec);
          if (rec) setEmployeeJobInfo(rec);
        }

        // 5) Departments
        const deptRes = await axios.get(`${BASE_URL}/users/departments`, {
          headers: { Authorization: `Token ${token}` },
        });
        console.log("🛈 departments:", deptRes.data);
        if (Array.isArray(deptRes.data)) {
          const map: Record<number, string> = {};
          deptRes.data.forEach((d: Department) => (map[d.id] = d.name));
          setDepartmentsMap(map);
        }

        // 6) Groups
        try {
          const groupsRes = await axios.get(`${BASE_URL}/users/groups`, {
            headers: { Authorization: `Token ${token}` },
          });
          console.log("🛈 groups:", groupsRes.data);
          if (Array.isArray(groupsRes.data)) {
            const map: Record<number, string> = {};
            groupsRes.data.forEach((g: Group) => (map[g.id] = g.name));
            setGroupsMap(map);
          }
        } catch (groupsErr) {
          console.log("🛈 Groups API not available, skipping:", groupsErr);
          // Set an empty map to avoid errors
          setGroupsMap({});
        }
      } catch (err) {
        console.error("⚠️ Error fetching header data:", err);
        // Don't show error toast since we have some data to display
        console.log("🛈 Some data was fetched successfully, continuing with partial data");
      } finally {
        setHeaderLoading(false);
        console.log("🛈 Header loading complete.");
      }
    }

    fetchData();
  }, [employeeNo, token]);

  // — Derived values
  const isArchived =
    employeeDates?.date_of_leaveing != null &&
    employeeDates.date_of_leaveing !== "";
  const status = isArchived ? "Inactive" : "Active";

  const departmentId = employeeJobInfo?.department ?? employee?.department_id;
  const departmentName = departmentId
    ? departmentsMap[departmentId] || "N/A"
    : "N/A";

  const genderVal = employeeBio?.gender || "N/A";

  const headingName =
    employeeBio?.first_name && employeeBio?.last_name
      ? `${employeeBio.first_name} ${employeeBio.last_name}`
      : employeeNo;

  // — Archive/Unarchive handler
  const toggleArchive = async () => {
    if (!token) {
      toast.error("Authentication token is missing");
      return;
    }

    const newDate = isArchived ? null : new Date().toISOString().split("T")[0];

    try {
      // First, get the existing record to update
      const headers = { Authorization: `Token ${token}` };
      const params = { employee_no: employeeNo };

      console.log("🛈 Fetching important dates for archive/unarchive:", employeeNo);
      const getRes = await axios.get(
        `${BASE_URL}/users/employee-important-dates-details`,
        { headers, params }
      );

      let recordId;
      let existingData = {};

      if (Array.isArray(getRes.data) && getRes.data.length > 0) {
        // Find the record for this employee
        const record = getRes.data.find((r: any) => r.employee_no === employeeNo);
        if (record) {
          recordId = record.id;
          existingData = { ...record };
          console.log("🛈 Found existing date record to update:", recordId);
        }
      }

      // Prepare the payload with all existing data plus the updated date_of_leaveing
      const payload = {
        ...existingData,
        employee_no: employeeNo,
        date_of_leaveing: newDate,
      };

      console.log("🛈 toggleArchive payload:", payload);

      let res;
      if (recordId) {
        // Update existing record
        res = await axios.patch(
          `${BASE_URL}/users/employee-important-dates-details/${recordId}`,
          payload,
          { headers }
        );
      } else {
        // Create new record
        res = await axios.post(
          `${BASE_URL}/users/employee-important-dates-details`,
          payload,
          { headers }
        );
      }

      console.log("🛈 toggleArchive response:", res.data);

      // Update local state
      setEmployeeDates(prev => ({ ...prev, date_of_leaveing: newDate }));
      toast.success(isArchived ? "Employee unarchived" : "Employee archived");
    } catch (err: any) {
      console.error("⚠️ toggleArchive error:", err.response?.status, err.response?.data);
      toast.error(
        `Archive failed${err.response?.status ? ` (${err.response.status})` : ""}`
      );
    }
  };

  // — Debug logs for status pills
  console.log("🛈 headerLoading:", headerLoading);
  console.log("🛈 employeeDates:", employeeDates);
  console.log("🛈 employeeBio:", employeeBio);
  console.log("🛈 employeeJobInfo:", employeeJobInfo);
  console.log("🛈 employee:", employee);
  console.log("🛈 isArchived:", isArchived, "=> status:", status);
  console.log("🛈 status pills:", {
    status,
    genderVal,
    departmentName,
    departmentId,
    departmentsMap
  });

  return (
    <RefreshProvider>
      {({ refreshTriggers, triggerRefresh }) => {
        // Define tabs with refresh triggers and navigation
        const tabs = [
          {
            label: "Bio Data",
            component: <BioDataTab
              employeeNo={employeeNo}
              key={`bio-${employeeNo}-${refreshTriggers['Bio Data']}`}
              onSaveSuccess={() => {
                triggerRefresh('Bio Data');
                navigateToNextTab('Bio Data');
              }}
            />
          },
          {
            label: "Contact Info",
            component: <ContactInfoTab
              employeeNo={employeeNo}
              key={`contact-${employeeNo}-${refreshTriggers['Contact Info']}`}
              onSaveSuccess={() => {
                triggerRefresh('Contact Info');
                navigateToNextTab('Contact Info');
              }}
            />
          },
          {
            label: "Contract Info",
            component: <ContractInfoTab
              employeeNo={employeeNo}
              key={`contract-${employeeNo}-${refreshTriggers['Contract Info']}`}
              onSaveSuccess={() => {
                triggerRefresh('Contract Info');
                navigateToNextTab('Contract Info');
              }}
            />
          },
          {
            label: "Education",
            component: <EducationTab
              employeeNo={employeeNo}
              key={`education-${employeeNo}-${refreshTriggers['Education']}`}
              onSaveSuccess={() => {
                triggerRefresh('Education');
                navigateToNextTab('Education');
              }}
            />
          },
          {
            label: "Important Dates",
            component: <ImportantDatesTab
              employeeNo={employeeNo}
              key={`dates-${employeeNo}-${refreshTriggers['Important Dates']}`}
              onSaveSuccess={() => {
                triggerRefresh('Important Dates');
                navigateToNextTab('Important Dates');
              }}
            />
          },
          {
            label: "Job Info",
            component: <JobInfoTab
              employeeNo={employeeNo}
              key={`job-${employeeNo}-${refreshTriggers['Job Info']}`}
              onSaveSuccess={() => {
                triggerRefresh('Job Info');
                // Also refresh the header data when job info changes (department, etc.)
                setTimeout(() => {
                  fetchData();
                }, 500);
                navigateToNextTab('Job Info');
              }}
            />
          },
          {
            label: "Payment",
            component: <PaymentTab
              employeeNo={employeeNo}
              key={`payment-${employeeNo}-${refreshTriggers['Payment']}`}
              onSaveSuccess={() => {
                triggerRefresh('Payment');
                navigateToNextTab('Payment');
              }}
            />
          },
          {
            label: "Next of Kin",
            component: <NextOfKinTab
              employeeNo={employeeNo}
              key={`kin-${employeeNo}-${refreshTriggers['Next of Kin']}`}
              onSaveSuccess={() => {
                triggerRefresh('Next of Kin');
                navigateToNextTab('Next of Kin');
              }}
            />
          },
          {
            label: "Guardians",
            component: <GuardiansTab
              employeeNo={employeeNo}
              key={`guardians-${employeeNo}-${refreshTriggers['Guardians']}`}
              onSaveSuccess={() => {
                triggerRefresh('Guardians');
                navigateToNextTab('Guardians');
              }}
            />
          },
        ];

        const activeContent = tabs.find((t) => t.label === activeTab)?.component;

        return (
          <div className="min-h-screen p-4 sm:p-6 bg-background">
            <div className="max-w-5xl mx-auto">
              {/* Header Card */}
              <div className="bg-card rounded-lg shadow-sm border border-border/40 p-4 sm:p-6 mb-6 transition-all duration-200 hover:shadow-md">
                {/* Top Section with Back Button and Title */}
                <div className="flex items-center mb-6">
                  <button
                    onClick={() => setView(null)}
                    className="mr-3 text-muted-foreground hover:text-foreground transition-colors p-2 rounded-full hover:bg-accent"
                  >
                    <ArrowLeft size={20} />
                  </button>
                  <h1 className="text-2xl font-semibold flex-1 text-foreground">{headingName}</h1>

                  {/* Archive / Unarchive */}
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        className={clsx(
                          "flex items-center",
                          isArchived
                            ? "bg-green-600 hover:bg-green-700"
                            : "bg-red-600 hover:bg-red-700"
                        )}
                      >
                        {isArchived ? "Unarchive Employee" : "Archive Employee"}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          {isArchived ? "Confirm Unarchive" : "Confirm Archive"}
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          {isArchived
                            ? "This will mark the employee as active again."
                            : "This will mark the employee as inactive."}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="flex justify-end space-x-2 mt-4">
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={toggleArchive}>
                          {isArchived ? "Unarchive" : "Archive"}
                        </AlertDialogAction>
                      </div>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>

                {/* Status Pills */}
                {headerLoading ? (
                  <div className="flex flex-wrap gap-2 mb-4">
                    <div className="h-6 w-16 bg-muted animate-pulse rounded-full"></div>
                    <div className="h-6 w-24 bg-muted animate-pulse rounded-full"></div>
                    <div className="h-6 w-32 bg-muted animate-pulse rounded-full"></div>
                  </div>
                ) : employeeBio && employeeJobInfo ? (
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span
                      className={clsx(
                        "text-xs px-3 py-1 rounded-full flex items-center font-medium",
                        status === "Active"
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                      )}
                    >
                      {status}
                    </span>
                    <span className="bg-accent text-accent-foreground text-xs px-3 py-1 rounded-full flex items-center">
                      <UserRound size={14} className="mr-1.5" />
                      {genderVal}
                    </span>
                    <span className="bg-accent text-accent-foreground text-xs px-3 py-1 rounded-full flex items-center">
                      <Briefcase size={14} className="mr-1.5" />
                      {departmentName}
                    </span>
                  </div>
                ) : (
                  <div className="flex flex-wrap gap-2 mb-4">
                    <div className="h-6 w-16 bg-muted animate-pulse rounded-full"></div>
                    <div className="h-6 w-24 bg-muted animate-pulse rounded-full"></div>
                    <div className="h-6 w-32 bg-muted animate-pulse rounded-full"></div>
                  </div>
                )}

                {/* Tabs Navigation */}
                <div className="border-b border-border">
                  <nav className="flex overflow-x-auto pb-px hide-scrollbar">
                    <div className="flex space-x-1 sm:space-x-2">
                      {tabs.map((tab) => (
                        <button
                          key={tab.label}
                          onClick={() => setActiveTab(tab.label)}
                          className={clsx(
                            "px-3 py-2 text-sm whitespace-nowrap rounded-t-lg transition-all",
                            activeTab === tab.label
                              ? "border-b-2 border-primary font-medium text-primary bg-primary/5"
                              : "border-b-2 border-transparent text-muted-foreground hover:text-foreground hover:bg-accent/50"
                          )}
                        >
                          {tab.label}
                        </button>
                      ))}
                    </div>
                  </nav>
                </div>
              </div>

              {/* Active Tab Content */}
              <div className="animate-in fade-in duration-300">
                {activeContent}
              </div>
            </div>
          </div>
        );
      }}
    </RefreshProvider>
  );
}
