import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle2,
  Target,
  ArrowRight,
  Timer,
  Award
} from 'lucide-react';

interface PerformanceTimelineProps {
  employee: any;
  appraisalHistory: any[];
}

interface TimelineEvent {
  id: string;
  date: string;
  type: 'appraisal' | 'pip_start' | 'probation_start' | 'promotion' | 'milestone';
  title: string;
  description: string;
  score?: number;
  status: 'completed' | 'in_progress' | 'upcoming' | 'overdue';
  icon: React.ReactNode;
  color: string;
}

const PerformanceTimeline: React.FC<PerformanceTimelineProps> = ({ 
  employee, 
  appraisalHistory 
}) => {
  const [timelineEvents, setTimelineEvents] = useState<TimelineEvent[]>([]);
  const [pipProgress, setPipProgress] = useState<number>(0);
  const [probationProgress, setProbationProgress] = useState<number>(0);
  const [nextMilestone, setNextMilestone] = useState<any>(null);

  useEffect(() => {
    if (employee && appraisalHistory.length > 0) {
      generateTimelineEvents();
      calculateProgress();
    }
  }, [employee, appraisalHistory]);

  const generateTimelineEvents = () => {
    const events: TimelineEvent[] = [];
    
    // Sort appraisal history by date
    const sortedHistory = [...appraisalHistory].sort((a, b) => 
      new Date(a.start_date).getTime() - new Date(b.start_date).getTime()
    );

    sortedHistory.forEach((appraisal, index) => {
      // Safely handle null/undefined values
      const periodName = appraisal.period_name || 'Performance Review';
      const score = appraisal.total_supervisor_rating_score || 0;
      const cycleType = appraisal.cycle_type || 'QUARTERLY';
      const closedDate = appraisal.closed_date || appraisal.end_date || new Date().toISOString();

      // Add appraisal event
      events.push({
        id: `appraisal-${appraisal.id}`,
        date: closedDate,
        type: 'appraisal',
        title: `${periodName} Appraisal`,
        description: `Score: ${score}% - ${cycleType}`,
        score: score,
        status: 'completed',
        icon: <Target className="h-4 w-4" />,
        color: score >= 75 ? 'green' :
               score >= 60 ? 'yellow' : 'red'
      });

      // Check for PIP/Probation starts
      if (cycleType.toLowerCase().includes('pip')) {
        const startDate = appraisal.start_date || closedDate;
        events.push({
          id: `pip-${appraisal.id}`,
          date: startDate,
          type: 'pip_start',
          title: 'Performance Improvement Plan Started',
          description: `PIP initiated due to performance concerns`,
          status: 'completed',
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'red'
        });
      }

      if (cycleType.toLowerCase().includes('probation')) {
        const startDate = appraisal.start_date || closedDate;
        events.push({
          id: `probation-${appraisal.id}`,
          date: startDate,
          type: 'probation_start',
          title: 'Probation Period Started',
          description: `Employee placed on probation`,
          status: 'completed',
          icon: <Clock className="h-4 w-4" />,
          color: 'orange'
        });
      }

      // Check for performance improvements
      if (index > 0) {
        const prevScore = sortedHistory[index - 1].total_supervisor_rating_score || 0;
        const currentScore = score;

        if (currentScore > prevScore + 10) {
          events.push({
            id: `improvement-${appraisal.id}`,
            date: closedDate,
            type: 'milestone',
            title: 'Significant Performance Improvement',
            description: `Score improved from ${prevScore}% to ${currentScore}%`,
            status: 'completed',
            icon: <TrendingUp className="h-4 w-4" />,
            color: 'green'
          });
        }
      }
    });

    // Add upcoming milestones
    const latestAppraisal = sortedHistory[sortedHistory.length - 1];
    if (latestAppraisal) {
      const endDate = latestAppraisal.end_date || latestAppraisal.closed_date || new Date().toISOString();
      const nextReviewDate = new Date(endDate);

      // Handle invalid dates
      if (isNaN(nextReviewDate.getTime())) {
        nextReviewDate.setTime(Date.now());
      }

      nextReviewDate.setMonth(nextReviewDate.getMonth() + 6); // Assuming 6-month cycles

      events.push({
        id: 'next-review',
        date: nextReviewDate.toISOString(),
        type: 'appraisal',
        title: 'Next Performance Review',
        description: 'Scheduled performance appraisal',
        status: 'upcoming',
        icon: <Calendar className="h-4 w-4" />,
        color: 'blue'
      });
    }

    // Sort events by date
    events.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    setTimelineEvents(events);
  };

  const calculateProgress = () => {
    // Calculate PIP progress (assuming 90-day PIP)
    const pipAppraisals = appraisalHistory.filter(a => 
      a.cycle_type?.toLowerCase().includes('pip')
    );
    
    if (pipAppraisals.length > 0) {
      const latestPip = pipAppraisals[pipAppraisals.length - 1];
      const pipStart = new Date(latestPip.start_date);
      const pipEnd = new Date(latestPip.end_date);
      const now = new Date();
      
      const totalDuration = pipEnd.getTime() - pipStart.getTime();
      const elapsed = now.getTime() - pipStart.getTime();
      const progress = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
      
      setPipProgress(progress);
    }

    // Calculate probation progress (assuming 6-month probation)
    const probationAppraisals = appraisalHistory.filter(a => 
      a.cycle_type?.toLowerCase().includes('probation')
    );
    
    if (probationAppraisals.length > 0) {
      const latestProbation = probationAppraisals[probationAppraisals.length - 1];
      const probationStart = new Date(latestProbation.start_date);
      const probationEnd = new Date(latestProbation.end_date);
      const now = new Date();
      
      const totalDuration = probationEnd.getTime() - probationStart.getTime();
      const elapsed = now.getTime() - probationStart.getTime();
      const progress = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
      
      setProbationProgress(progress);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'in_progress': return 'bg-blue-500';
      case 'upcoming': return 'bg-gray-400';
      case 'overdue': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const getEventColor = (color: string) => {
    switch (color) {
      case 'green': return 'border-green-500 bg-green-50';
      case 'red': return 'border-red-500 bg-red-50';
      case 'orange': return 'border-orange-500 bg-orange-50';
      case 'blue': return 'border-blue-500 bg-blue-50';
      case 'yellow': return 'border-yellow-500 bg-yellow-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress Indicators */}
      {(employee.pip_status || employee.probation_status) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {employee.pip_status && (
            <Card className="border-red-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center text-red-700">
                  <AlertTriangle className="mr-2 h-5 w-5" />
                  PIP Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(pipProgress)}%</span>
                  </div>
                  <Progress value={pipProgress} className="h-3" />
                  <p className="text-xs text-gray-600">
                    {pipProgress < 100 ? `${Math.round(100 - pipProgress)}% remaining` : 'PIP completed'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {employee.probation_status && (
            <Card className="border-orange-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center text-orange-700">
                  <Timer className="mr-2 h-5 w-5" />
                  Probation Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(probationProgress)}%</span>
                  </div>
                  <Progress value={probationProgress} className="h-3" />
                  <p className="text-xs text-gray-600">
                    {probationProgress < 100 ? `${Math.round(100 - probationProgress)}% remaining` : 'Probation completed'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Performance Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Performance Timeline
          </CardTitle>
          <CardDescription>
            Complete performance journey for {employee.employee_name}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
            
            {/* Timeline events */}
            <div className="space-y-6">
              {timelineEvents.map((event, index) => (
                <div key={event.id} className="relative flex items-start">
                  {/* Timeline dot */}
                  <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-2 ${getStatusColor(event.status)} border-white shadow-lg`}>
                    <div className="text-white">
                      {event.icon}
                    </div>
                  </div>
                  
                  {/* Event content */}
                  <div className={`ml-4 flex-1 p-4 rounded-lg border-l-4 ${getEventColor(event.color)}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">{event.title}</h4>
                        <p className="text-sm text-gray-600">{event.description}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {(() => {
                            const date = new Date(event.date);
                            return isNaN(date.getTime()) ? 'Date not available' : date.toLocaleDateString();
                          })()}
                        </p>
                      </div>
                      
                      {event.score && (
                        <Badge variant={
                          event.score >= 90 ? 'default' :
                          event.score >= 75 ? 'secondary' :
                          event.score >= 60 ? 'outline' : 'destructive'
                        }>
                          {event.score}%
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceTimeline;
