import React, { ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface TabLayoutProps {
  title: string;
  description?: string;
  employeeNo: string;
  loading: boolean;
  children: ReactNode;
  actions?: ReactNode;
}

/**
 * A consistent layout component for tab content
 */
export function TabLayout({
  title,
  description,
  employeeNo,
  loading,
  children,
  actions
}: TabLayoutProps) {
  return (
    <Card className="border border-border/40 shadow-sm transition-all duration-200 hover:shadow-md">
      <CardHeader className="pb-3">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
          <div>
            <CardTitle className="text-xl font-semibold">{title}</CardTitle>
            {description && (
              <CardDescription className="text-muted-foreground mt-1">
                {description}
              </CardDescription>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              Employee ID: <span className="font-mono">{employeeNo}</span>
            </p>
          </div>
          {actions && (
            <div className="flex justify-end mt-2 sm:mt-0">
              {actions}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-3">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-8 w-2/3" />
          </div>
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );
}

/**
 * A grid layout for form fields with responsive behavior
 */
export function FormGrid({ children, columns = 2 }: { children: ReactNode, columns?: 1 | 2 | 3 }) {
  const gridClass = {
    1: "grid grid-cols-1 gap-4",
    2: "grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6",
    3: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6"
  }[columns];
  
  return (
    <div className={gridClass}>
      {children}
    </div>
  );
}

/**
 * A form field wrapper with consistent styling
 */
export function FormField({ children, className = "" }: { children: ReactNode, className?: string }) {
  return (
    <div className={`space-y-2 ${className}`}>
      {children}
    </div>
  );
}
