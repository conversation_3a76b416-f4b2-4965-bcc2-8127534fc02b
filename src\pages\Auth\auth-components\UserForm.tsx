// src/components/auth-components/UserForm.tsx
"use client";

import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import { createUser } from "@/redux/features/setup/setupSlice";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface UserFormProps {
  onFinish: () => void;
}

const UserForm: React.FC<UserFormProps> = ({ onFinish }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { organization, userLoading, userError } = useSelector((state: RootState) => state.setup);
  const { toast } = useToast();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [departmentId, setDepartmentId] = useState<number | undefined>();
  const [groupId, setGroupId] = useState<number | undefined>();

  const [alertOpen, setAlertOpen] = useState(false);
  const [alertTitle, setAlertTitle] = useState("");
  const [alertDescription, setAlertDescription] = useState("");

  useEffect(() => {
    if (userError) {
      setAlertTitle("Error");
      setAlertDescription(userError);
      setAlertOpen(true);
    }
  }, [userError]);

  const handleCreateUser = (e: React.FormEvent) => {
    e.preventDefault();
    if (!organization) {
      toast({
        title: "No Organization",
        description: "Create an organization first.",
        variant: "destructive",
      });
      return;
    }

    dispatch(
      createUser({
        email,
        password,
        organisation_id: organization.id,
        department_id: departmentId,
        group_id: groupId,
      })
    ).then((res) => {
      if (res.meta.requestStatus === "fulfilled") {
        setAlertTitle("User Created");
        setAlertDescription(`User ${email} has been successfully created.`);
        setAlertOpen(true);
      }
    });
  };

  const handleAlertClose = () => {
    setAlertOpen(false);
    onFinish();
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Create a User</h2>
      <form onSubmit={handleCreateUser} className="space-y-4">
        <div>
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="********"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="deptId">Department ID (optional)</Label>
          <Input
            id="deptId"
            type="number"
            value={departmentId ?? ""}
            onChange={(e) => setDepartmentId(Number(e.target.value))}
          />
        </div>
        <div>
          <Label htmlFor="groupId">Group ID (optional)</Label>
          <Input
            id="groupId"
            type="number"
            value={groupId ?? ""}
            onChange={(e) => setGroupId(Number(e.target.value))}
          />
        </div>
        <Button type="submit" disabled={userLoading}>
          {userLoading ? "Creating..." : "Create User"}
        </Button>
      </form>
      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{alertTitle}</AlertDialogTitle>
            <AlertDialogDescription>{alertDescription}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleAlertClose}>OK</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default UserForm;
