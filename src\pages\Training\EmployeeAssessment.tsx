"use client";

import { Screen } from "@/app-components/layout/screen";
import { TrainingAssessment } from "../../app-components/training/TrainingAssessment";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  <PERSON>readcrumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { BASE_URL } from "@/config";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

// Optional: If you need an ID for the specific training or employee, pass it down as a prop
interface EmployeeAssessmentPageProps {
  employeeTrainingId?: number;
}

export default function EmployeeAssessmentPage({
  employeeTrainingId,
}: EmployeeAssessmentPageProps) {
  const handleAssessmentSubmit = async (
    file: File | null,
    comments: string
  ) => {
    try {
      const response = await fetch(
        `${BASE_URL}/hrm/employee-training-details`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            // e.g., employee: <employeeId>,
            // content: <trainingId>,
            // status: "completed" or "in_progress",
            // progress_percentage: ...
            // If your backend supports "comments", include it
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to submit assessment");
      }
      const data = await response.json();
      console.log("Submitted Employee Assessment:", data);

      // If you need to upload the file, switch to multipart form or do a separate upload
      // after the record is created.
    } catch (error) {
      console.error(error);
    }
  };

  const token = useSelector((state: RootState) => state.auth.token);

  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink
            href="/"
            className="text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white"
          >
            Optiven HRMS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block text-gray-300 dark:text-gray-500" />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-700 dark:text-gray-200">
            Employee Evaluation
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  return (
    <Screen headerContent={breadcrumb}>
      <div>
        <TrainingAssessment
          templateLink={`${BASE_URL}/api/evaluation-template.pdf`}
          onSubmit={handleAssessmentSubmit}
        />
      </div>
    </Screen>
  );
}
