// src/components/auth-components/DepartmentsForm.tsx
"use client";

import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import { createDepartment } from "@/redux/features/setup/setupSlice";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface DepartmentForm {
  id: number;
  name: string;
  description: string;
  dep_head: string;
  dep_head_assistant: string;
  dep_hr: string;
  department_status_active: boolean;
  parent_department: number | null;
}

interface DepartmentsFormProps {
  onNext: () => void;
}

const DepartmentsForm: React.FC<DepartmentsFormProps> = ({ onNext }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { organization, depLoading, depError, departments } = useSelector(
    (state: RootState) => state.setup
  );
  const { toast } = useToast();

  const [departmentForms, setDepartmentForms] = useState<DepartmentForm[]>([
    {
      id: Date.now(),
      name: "",
      description: "",
      dep_head: "",
      dep_head_assistant: "",
      dep_hr: "",
      department_status_active: true,
      parent_department: null,
    },
  ]);

  const [alertOpen, setAlertOpen] = useState(false);
  const [alertTitle, setAlertTitle] = useState("");
  const [alertDescription, setAlertDescription] = useState("");

  useEffect(() => {
    if (depError) {
      setAlertTitle("Error");
      setAlertDescription(depError);
      setAlertOpen(true);
    }
  }, [depError]);

  // Optionally, when departments are added, show a success alert.
  useEffect(() => {
    if (departments.length > 0) {
      setAlertTitle("Department Added");
      setAlertDescription(`Total departments: ${departments.length}`);
      setAlertOpen(true);
    }
  }, [departments]);

  const handleAddRow = () => {
    setDepartmentForms((prev) => [
      ...prev,
      {
        id: Date.now(),
        name: "",
        description: "",
        dep_head: "",
        dep_head_assistant: "",
        dep_hr: "",
        department_status_active: true,
        parent_department: null,
      },
    ]);
  };

  const handleRemoveRow = (id: number) => {
    setDepartmentForms((prev) => prev.filter((row) => row.id !== id));
  };

  const handleRowChange = (
    id: number,
    field: keyof Omit<DepartmentForm, "id">,
    value: string | boolean | number | null
  ) => {
    setDepartmentForms((prev) =>
      prev.map((row) => (row.id === id ? { ...row, [field]: value } : row))
    );
  };

  const handleSaveAll = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!organization) {
      toast({
        title: "Organization not found",
        description: "Please create an organization first",
        variant: "destructive",
      });
      return;
    }
    let savedCount = 0;
    for (const dept of departmentForms) {
      // Validate required fields.
      if (
        dept.name.trim() !== "" &&
        dept.description.trim() !== "" &&
        dept.dep_head.trim() !== "" &&
        dept.dep_head_assistant.trim() !== "" &&
        dept.dep_hr.trim() !== ""
      ) {
        const result = await dispatch(
          createDepartment({
            name: dept.name,
            description: dept.description,
            dep_head: dept.dep_head,
            dep_head_assistant: dept.dep_head_assistant,
            dep_hr: dept.dep_hr,
            department_status_active: dept.department_status_active,
            organisation: organization.id,
            parent_department: dept.parent_department,
          })
        );
        if (result.meta.requestStatus === "fulfilled") savedCount++;
      }
    }
    setAlertTitle("Departments Saved");
    setAlertDescription(`Total departments added: ${savedCount}`);
    setAlertOpen(true);
    // Optionally, reset form rows after saving.
    setDepartmentForms([
      {
        id: Date.now(),
        name: "",
        description: "",
        dep_head: "",
        dep_head_assistant: "",
        dep_hr: "",
        department_status_active: true,
        parent_department: null,
      },
    ]);
  };

  const handleAlertClose = () => {
    setAlertOpen(false);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Add Departments</h2>
      <form onSubmit={handleSaveAll} className="space-y-4">
        {departmentForms.map((dept) => (
          <div key={dept.id} className="flex flex-col gap-2 border p-2 rounded">
            <div>
              <Label>Department Name</Label>
              <Input
                value={dept.name}
                onChange={(e) => handleRowChange(dept.id, "name", e.target.value)}
                placeholder="e.g. HR"
                required
              />
            </div>
            <div>
              <Label>Description</Label>
              <Input
                value={dept.description}
                onChange={(e) => handleRowChange(dept.id, "description", e.target.value)}
                placeholder="Short description"
                required
              />
            </div>
            <div>
              <Label>Dep Head</Label>
              <Input
                value={dept.dep_head}
                onChange={(e) => handleRowChange(dept.id, "dep_head", e.target.value)}
                placeholder="Department Head"
                required
              />
            </div>
            <div>
              <Label>Dep Head Assistant</Label>
              <Input
                value={dept.dep_head_assistant}
                onChange={(e) => handleRowChange(dept.id, "dep_head_assistant", e.target.value)}
                placeholder="Department Head Assistant"
                required
              />
            </div>
            <div>
              <Label>Dep HR</Label>
              <Input
                value={dept.dep_hr}
                onChange={(e) => handleRowChange(dept.id, "dep_hr", e.target.value)}
                placeholder="Department HR"
                required
              />
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor={`status-${dept.id}`}>Active?</Label>
              <Input
                id={`status-${dept.id}`}
                type="checkbox"
                checked={dept.department_status_active}
                onChange={(e) => handleRowChange(dept.id, "department_status_active", e.target.checked)}
              />
            </div>
            <div>
              <Label>Parent Department</Label>
              <select
                value={dept.parent_department ?? ""}
                onChange={(e) =>
                  handleRowChange(
                    dept.id,
                    "parent_department",
                    e.target.value === "" ? null : Number(e.target.value)
                  )
                }
                className="border p-2 rounded w-full"
              >
                <option value="">None</option>
                {departments.map((d) => (
                  <option key={d.id} value={d.id}>
                    {d.name}
                  </option>
                ))}
              </select>
            </div>
            {departmentForms.length > 1 && (
              <Button variant="secondary" onClick={() => handleRemoveRow(dept.id)}>
                Remove
              </Button>
            )}
          </div>
        ))}
        <div className="flex gap-2">
          <Button type="button" onClick={handleAddRow}>
            + Add Another
          </Button>
          <Button type="submit" disabled={depLoading}>
            {depLoading ? "Saving..." : "Save All Departments"}
          </Button>
        </div>
      </form>
      <div className="mt-4">
        <h3 className="font-medium">Existing Departments</h3>
        <ul className="list-disc pl-6">
          {departments.map((dept) => (
            <li key={dept.id}>{dept.name}</li>
          ))}
        </ul>
      </div>
      <Button onClick={onNext} disabled={departments.length === 0}>
        Next: Groups (Optional)
      </Button>
      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{alertTitle}</AlertDialogTitle>
            <AlertDialogDescription>{alertDescription}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleAlertClose}>OK</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default DepartmentsForm;
