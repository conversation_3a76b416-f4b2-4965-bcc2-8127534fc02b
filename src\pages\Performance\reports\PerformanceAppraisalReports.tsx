import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { <PERSON><PERSON>, Ta<PERSON>List, Ta<PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs';
import {
  FileText,
  Users,
  UsersRound,
  Building2,
  UserCheck,
  BarChart3,
  PieChart
} from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

// Types
interface AppraisalRecord {
  id: number;
  employeeid_id: string;
  supervisor_id: string;
  status: string;
  final_supervisor_comments: string | null;
  total_supervisor_rating_score: number | null;
  final_hr_comments: string | null;
  total_emp_self_rating_score: number | null;
  first_name: string;
  last_name: string;
  supervisor_first_name: string;
  supervisor_last_name: string;
  employee_comments: string;
  supervisor_comments: string;
  supervisor_rating: number;
  employee_rating: number;
  what_id: number;
  MIB_target: number | null;
  MIB_achieved: number | null;
  Sales_target: number | null;
  Sales_achieved: number | null;
  what: string;
  how: string;
}

interface GroupedKPI {
  what_id: number;
  what: string;
  employee_rating: number;
  supervisor_rating: number;
  employee_comments: string;
  supervisor_comments: string;
  how_items: {
    how: string;
    MIB_target: number | null;
    MIB_achieved: number | null;
    Sales_target: number | null;
    Sales_achieved: number | null;
  }[];
}

interface GroupedAppraisalRecord {
  employeeid_id: string;
  first_name: string;
  last_name: string;
  supervisor_id: string;
  supervisor_first_name: string;
  supervisor_last_name: string;
  status: string;
  total_kpis: number;
  total_supervisor_rating_score: number | null;
  total_emp_self_rating_score: number | null;
  final_supervisor_comments: string | null;
  final_hr_comments: string | null;
  grouped_kpis: GroupedKPI[];
  expanded?: boolean;
}

interface Department {
  id: number;
  name: string;
  title?: string;
}

interface Team {
  id: number;
  name: string;
  description: string;
  group_head: string;
  group_assistant: string;
  group_hr: string;
  group_status_active: boolean;
  organisation: number;
  parent_group: number | null;
}

interface AppraisalFilters {
  employee_no?: string;
  department?: string;
  team?: string;
  status?: string;
  search?: string;
  date_from?: string;
  date_to?: string;
  performance_period?: string;
}

interface DepartmentPerformanceData {
  department_id: number;
  department_name: string;
  total_employees: number;
  completed_appraisals: number;
  pending_appraisals: number;
  average_self_rating: number;
  average_supervisor_rating: number;
  completion_rate: number;
  top_performers: number;
  needs_improvement: number;
}

interface TeamPerformanceData {
  team_id: number;
  team_name: string;
  department_name: string;
  total_employees: number;
  completed_appraisals: number;
  pending_appraisals: number;
  average_self_rating: number;
  average_supervisor_rating: number;
  completion_rate: number;
  team_lead: string;
}

interface SummaryReportData {
  total_employees: number;
  total_appraisals: number;
  completion_rate: number;
  average_organization_rating: number;
  departments_count: number;
  teams_count: number;
  top_performing_departments: DepartmentPerformanceData[];
  performance_distribution: {
    excellent: number;
    good: number;
    average: number;
    needs_improvement: number;
  };
}

// Note: Individual tab components will be imported when fully implemented
// import IndividualPerformanceTab from './tabs/IndividualPerformanceTab';
// import DepartmentPerformanceTab from './tabs/DepartmentPerformanceTab';
// import TeamPerformanceTab from './tabs/TeamPerformanceTab';
// import SummaryReportsTab from './tabs/SummaryReportsTab';
// import SharedFilters from './components/SharedFilters';
// import { handleExport } from './utils/exportUtils';

const PerformanceAppraisalReports: React.FC = () => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State for tab management
  const [activeTab, setActiveTab] = useState('individual');
  const [individualData, setIndividualData] = useState<GroupedAppraisalRecord[]>([]);
  const [departmentData, setDepartmentData] = useState<DepartmentPerformanceData[]>([]);
  const [teamData, setTeamData] = useState<TeamPerformanceData[]>([]);
  const [summaryData, setSummaryData] = useState<SummaryReportData | null>(null);

  // Shared state
  const [departments, setDepartments] = useState<Department[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch metadata on component mount
  useEffect(() => {
    const fetchMetadata = async () => {
      if (!token) return;

      try {
        // Fetch departments
        const deptRes = await fetch(`${BASE_URL}/users/departments`, {
          headers: { Authorization: `Token ${token}` },
        });

        if (deptRes.ok) {
          const deptData = await deptRes.json();
          setDepartments(deptData);
        }

        // Fetch teams (organization groups)
        const teamsRes = await fetch(`${BASE_URL}/users/organization_groups`, {
          headers: { Authorization: `Token ${token}` },
        });

        if (teamsRes.ok) {
          const teamsData = await teamsRes.json();
          setTeams(teamsData);
        }
      } catch (err) {
        console.error('Error fetching metadata:', err);
      }
    };

    fetchMetadata();
  }, [token]);

  // Data change handlers for each tab
  const handleIndividualDataChange = (data: GroupedAppraisalRecord[]) => {
    setIndividualData(data);
  };

  const handleDepartmentDataChange = (data: DepartmentPerformanceData[]) => {
    setDepartmentData(data);
  };

  const handleTeamDataChange = (data: TeamPerformanceData[]) => {
    setTeamData(data);
  };

  const handleSummaryDataChange = (data: SummaryReportData) => {
    setSummaryData(data);
  };

  // Get current tab data for stats
  const getCurrentTabData = () => {
    switch (activeTab) {
      case 'individual':
        return { data: individualData, type: 'individual' };
      case 'department':
        return { data: departmentData, type: 'department' };
      case 'team':
        return { data: teamData, type: 'team' };
      case 'summary':
        return { data: summaryData, type: 'summary' };
      default:
        return { data: [], type: 'individual' };
    }
  };

  // Get stats for current tab
  const getTabStats = () => {
    const { data, type } = getCurrentTabData();

    if (type === 'individual') {
      const individualArray = data as GroupedAppraisalRecord[];
      return {
        totalEmployees: individualArray.length,
        totalRecords: individualArray.reduce((sum, emp) => sum + emp.total_kpis, 0),
        completedReviews: individualArray.filter(emp => emp.status === 'Completed').length,
        departmentsCount: departments.length,
        teamsCount: teams.length,
      };
    } else if (type === 'department') {
      const deptArray = data as DepartmentPerformanceData[];
      return {
        totalEmployees: deptArray.reduce((sum, dept) => sum + dept.total_employees, 0),
        totalRecords: deptArray.length,
        completedReviews: deptArray.reduce((sum, dept) => sum + dept.completed_appraisals, 0),
        departmentsCount: deptArray.length,
        teamsCount: teams.length,
      };
    } else if (type === 'team') {
      const teamArray = data as TeamPerformanceData[];
      return {
        totalEmployees: teamArray.reduce((sum, team) => sum + team.total_employees, 0),
        totalRecords: teamArray.length,
        completedReviews: teamArray.reduce((sum, team) => sum + team.completed_appraisals, 0),
        departmentsCount: departments.length,
        teamsCount: teamArray.length,
      };
    } else if (type === 'summary' && data) {
      const summaryObj = data as SummaryReportData;
      return {
        totalEmployees: summaryObj.total_employees,
        totalRecords: summaryObj.total_appraisals,
        completedReviews: Math.round((summaryObj.completion_rate / 100) * summaryObj.total_employees),
        departmentsCount: summaryObj.departments_count,
        teamsCount: summaryObj.teams_count,
      };
    }

    return {
      totalEmployees: 0,
      totalRecords: 0,
      completedReviews: 0,
      departmentsCount: departments.length,
      teamsCount: teams.length,
    };
  };

  const stats = getTabStats();

  return (
    <Screen>
      <div className="space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/performance-dashboard">Performance</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbPage>Performance Reports</BreadcrumbPage>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Performance Reports</h1>
            <p className="text-muted-foreground">
              Comprehensive performance reports with advanced filtering and analytics across different organizational levels
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{loading ? <Skeleton className="h-8 w-16" /> : stats.totalEmployees}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Records</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? <Skeleton className="h-8 w-16" /> : stats.totalRecords}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Departments</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.departmentsCount}</div>
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <UsersRound className="h-3 w-3" />
                {stats.teamsCount} Teams
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Reviews</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? <Skeleton className="h-8 w-16" /> : stats.completedReviews}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Tab</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-sm font-medium capitalize">{activeTab.replace('_', ' ')}</div>
              <p className="text-xs text-muted-foreground">Current view</p>
            </CardContent>
          </Card>
        </div>

        {/* Tabbed Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="individual" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Individual Performance
            </TabsTrigger>
            <TabsTrigger value="department" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Department Performance
            </TabsTrigger>
            <TabsTrigger value="team" className="flex items-center gap-2">
              <UsersRound className="h-4 w-4" />
              Team Performance
            </TabsTrigger>
            <TabsTrigger value="summary" className="flex items-center gap-2">
              <PieChart className="h-4 w-4" />
              Summary Reports
            </TabsTrigger>
          </TabsList>

          {/* Individual Performance Tab */}
          <TabsContent value="individual" className="space-y-6">
            <IndividualPerformanceContent
              onDataChange={handleIndividualDataChange}
              departments={departments}
              teams={teams}
            />
          </TabsContent>

          {/* Department Performance Tab */}
          <TabsContent value="department" className="space-y-6">
            <DepartmentPerformanceContent
              onDataChange={handleDepartmentDataChange}
              departments={departments}
            />
          </TabsContent>

          {/* Team Performance Tab */}
          <TabsContent value="team" className="space-y-6">
            <TeamPerformanceContent
              onDataChange={handleTeamDataChange}
              teams={teams}
              departments={departments}
            />
          </TabsContent>

          {/* Summary Reports Tab */}
          <TabsContent value="summary" className="space-y-6">
            <SummaryReportsContent
              onDataChange={handleSummaryDataChange}
            />
          </TabsContent>
        </Tabs>
      </div>
    </Screen>
  );
};
// Individual Performance Tab Content Component
const IndividualPerformanceContent: React.FC<{
  onDataChange?: (data: GroupedAppraisalRecord[]) => void;
  departments: Department[];
  teams: Team[];
}> = ({ onDataChange, departments, teams }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<GroupedAppraisalRecord[]>([]);
  const [filters, setFilters] = useState<any>({});
  const [isExporting, setIsExporting] = useState(false);

  // Fetch appraisal data
  const fetchAppraisalData = async (filterParams: any = {}) => {
    if (!token) return;

    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filterParams.employee_no) params.append('employee_no', filterParams.employee_no);
      if (filterParams.department && filterParams.department !== 'all') params.append('department', filterParams.department);
      if (filterParams.team && filterParams.team !== 'all') params.append('team', filterParams.team);
      if (filterParams.date_from) params.append('date_from', filterParams.date_from);
      if (filterParams.date_to) params.append('date_to', filterParams.date_to);

      const url = `${BASE_URL}/appraisal_report/${params.toString() ? `?${params.toString()}` : ''}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Group records by employee ID
      const groupedData = groupRecordsByEmployee(result.results || []);

      // Apply client-side filters
      const filteredData = applyClientSideFilters(groupedData, filterParams);

      setData(filteredData);

      if (onDataChange) {
        onDataChange(filteredData);
      }

    } catch (err: any) {
      console.error('Error fetching appraisal data:', err);
      toast({
        title: "Error",
        description: "Failed to fetch appraisal data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Group records by employee ID and then by what_id
  const groupRecordsByEmployee = (records: any[]): GroupedAppraisalRecord[] => {
    const grouped = records.reduce((acc, record) => {
      const key = record.employeeid_id;

      if (!acc[key]) {
        acc[key] = {
          employeeid_id: record.employeeid_id,
          first_name: record.first_name,
          last_name: record.last_name,
          supervisor_id: record.supervisor_id,
          supervisor_first_name: record.supervisor_first_name,
          supervisor_last_name: record.supervisor_last_name,
          status: record.status,
          total_kpis: 0,
          total_supervisor_rating_score: record.total_supervisor_rating_score,
          total_emp_self_rating_score: record.total_emp_self_rating_score,
          final_supervisor_comments: record.final_supervisor_comments,
          final_hr_comments: record.final_hr_comments,
          grouped_kpis: [],
        };
      }

      // Find existing KPI group or create new one
      let kpiGroup = acc[key].grouped_kpis.find((kpi: any) => kpi.what_id === record.what_id);

      if (!kpiGroup) {
        kpiGroup = {
          what_id: record.what_id,
          what: record.what,
          employee_rating: record.employee_rating,
          supervisor_rating: record.supervisor_rating,
          employee_comments: record.employee_comments,
          supervisor_comments: record.supervisor_comments,
          how_items: [],
        };
        acc[key].grouped_kpis.push(kpiGroup);
      }

      // Add the "how" item to this KPI group (only if not already added)
      const existingHow = kpiGroup.how_items.find((item: any) => item.how === record.how);
      if (!existingHow) {
        kpiGroup.how_items.push({
          how: record.how,
          MIB_target: record.MIB_target,
          MIB_achieved: record.MIB_achieved,
          Sales_target: record.Sales_target,
          Sales_achieved: record.Sales_achieved,
        });
      }

      return acc;
    }, {} as Record<string, GroupedAppraisalRecord>);

    // Update total KPI count for each employee
    Object.values(grouped).forEach(employee => {
      employee.total_kpis = employee.grouped_kpis.length;
    });

    return Object.values(grouped);
  };

  // Apply client-side filters
  const applyClientSideFilters = (data: GroupedAppraisalRecord[], filterParams: any): GroupedAppraisalRecord[] => {
    return data.filter(record => {
      // Status filter
      if (filterParams.status && filterParams.status !== 'all' && record.status !== filterParams.status) {
        return false;
      }

      // Search filter
      if (filterParams.search) {
        const searchTerm = filterParams.search.toLowerCase();
        const searchableText = [
          record.first_name,
          record.last_name,
          record.employeeid_id,
          record.final_supervisor_comments,
          record.final_hr_comments,
          ...record.grouped_kpis.map((kpi: any) => kpi.what),
          ...record.grouped_kpis.flatMap((kpi: any) => kpi.how_items.map((item: any) => item.how)),
        ].join(' ').toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      return true;
    });
  };

  // Filter handlers
  const handleFilterChange = (key: string, value: string) => {
    const newFilters = {
      ...filters,
      [key]: value === 'all' ? undefined : value || undefined
    };
    setFilters(newFilters);
    fetchAppraisalData(newFilters);
  };

  const clearFilters = () => {
    setFilters({});
    fetchAppraisalData({});
  };

  // Export individual employee to Excel function
  const handleExportIndividualToExcel = async (employee: GroupedAppraisalRecord) => {
    try {
      // Import XLSX dynamically
      const XLSX = await import('xlsx');
      const workbook = XLSX.utils.book_new();
      const sheetData: any[][] = [];

      // Company Header Section
      sheetData.push(['OPTIVEN LIMITED']);
      sheetData.push(['Absa Tower 2nd floor, Loita St']);
      sheetData.push(['P.O BOX: 6525-00600 Nairobi']);
      sheetData.push(['E-Mail: <EMAIL>']);
      sheetData.push(['Mobile: 0790600600 / 0711601019']);
      sheetData.push([]); 

      // Report Title
      sheetData.push(['PERFORMANCE APPRAISAL REPORT']);
      sheetData.push([]);

      // Employee Information Section
      sheetData.push(['EMPLOYEE INFORMATION']);
      sheetData.push(['Employee ID:', employee.employeeid_id]);
      sheetData.push(['Employee Name:', `${employee.first_name} ${employee.last_name}`]);
      sheetData.push(['Supervisor ID:', employee.supervisor_id]);
      sheetData.push(['Supervisor Name:', `${employee.supervisor_first_name || ''} ${employee.supervisor_last_name || ''}`]);
      sheetData.push(['Status:', employee.status]);
      sheetData.push(['Total KPIs:', employee.total_kpis]);
      sheetData.push(['Total Self Rating Score:', employee.total_emp_self_rating_score || 'N/A']);
      sheetData.push(['Total Supervisor Rating Score:', employee.total_supervisor_rating_score || 'N/A']);
      sheetData.push([]);

      // KPI Detail Section
      sheetData.push(['KPI PERFORMANCE DETAILS']);
      sheetData.push([]);

      employee.grouped_kpis.forEach((kpi, index) => {
        // KPI Header
        sheetData.push([`KPI ${index + 1}: ${kpi.what}`]);
        sheetData.push(['Employee Rating:', kpi.employee_rating]);
        sheetData.push(['Supervisor Rating:', kpi.supervisor_rating]);
        sheetData.push([]);

        // How Items
        if (kpi.how_items && kpi.how_items.length > 0) {
          sheetData.push(['Performance Indicators:']);
          kpi.how_items.forEach((howItem, howIndex) => {
            let howText = `${howIndex + 1}. ${howItem.how}`;
            if (howItem.MIB_target) {
              howText += ` (MIB Target: ${howItem.MIB_target}, Achieved: ${howItem.MIB_achieved || 0})`;
            }
            if (howItem.Sales_target) {
              howText += ` (Sales Target: ${howItem.Sales_target}, Achieved: ${howItem.Sales_achieved || 0})`;
            }
            sheetData.push([howText]);
          });
          sheetData.push([]);
        }

        // Comments
        if (kpi.employee_comments) {
          sheetData.push(['Employee Comments:', kpi.employee_comments]);
        }
        if (kpi.supervisor_comments) {
          sheetData.push(['Supervisor Comments:', kpi.supervisor_comments]);
        }
        sheetData.push([]); // Empty row between KPIs
      });

      // Final Comments Section
      if (employee.final_supervisor_comments || employee.final_hr_comments) {
        sheetData.push(['FINAL COMMENTS']);
        if (employee.final_supervisor_comments) {
          sheetData.push(['Supervisor Final Comments:', employee.final_supervisor_comments]);
        }
        if (employee.final_hr_comments) {
          sheetData.push(['HR Final Comments:', employee.final_hr_comments]);
        }
        sheetData.push([]);
      }

      // Report Footer
      sheetData.push(['Report Generated:', new Date().toLocaleDateString()]);
      sheetData.push(['Generated By:', 'OPTIVEN HR System']);

      // Create worksheet
      const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

      // Set column widths
      const colWidths = [
        { wch: 25 }, // Column A
        { wch: 50 }, // Column B
        { wch: 15 }, // Column C
        { wch: 15 }, // Column D
      ];
      worksheet['!cols'] = colWidths;

      // Add worksheet to workbook
      const sheetName = `${employee.first_name}_${employee.last_name}_Appraisal`.replace(/[^a-zA-Z0-9_]/g, '_');
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

      // Generate filename and download
      const fileName = `${employee.first_name}_${employee.last_name}_Performance_Appraisal_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast({
        title: "Export Successful",
        description: `Performance appraisal report for ${employee.first_name} ${employee.last_name} exported successfully.`,
      });

    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export individual report. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Export all employees to Excel function
  const handleExportToExcel = async () => {
    if (data.length === 0) {
      toast({
        title: "No Data",
        description: "No data available to export.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    try {
      // Import XLSX dynamically
      const XLSX = await import('xlsx');
      const workbook = XLSX.utils.book_new();

      // Create a separate sheet for each employee
      data.forEach((employee) => {
        const sheetData: any[][] = [];

        // Company Header
        sheetData.push(['OPTIVEN LIMITED']);
        sheetData.push(['Performance Appraisal Report']);
        sheetData.push([]);

        // Employee Info
        sheetData.push(['Employee ID', employee.employeeid_id]);
        sheetData.push(['Name', `${employee.first_name} ${employee.last_name}`]);
        sheetData.push(['Supervisor', employee.supervisor_id]);
        sheetData.push(['Status', employee.status]);
        sheetData.push(['Total KPIs', employee.total_kpis]);
        sheetData.push(['Self Rating', employee.total_emp_self_rating_score || 'N/A']);
        sheetData.push(['Supervisor Rating', employee.total_supervisor_rating_score || 'N/A']);
        sheetData.push([]);

        // KPI Details Header
        sheetData.push(['KPI', 'How', 'Employee Rating', 'Supervisor Rating', 'Employee Comments', 'Supervisor Comments']);

        // KPI Data
        employee.grouped_kpis.forEach((kpi) => {
          kpi.how_items.forEach((howItem) => {
            sheetData.push([
              kpi.what,
              howItem.how,
              kpi.employee_rating,
              kpi.supervisor_rating,
              kpi.employee_comments || '',
              kpi.supervisor_comments || ''
            ]);
          });
        });

        sheetData.push([]);
        sheetData.push(['Final Supervisor Comments', employee.final_supervisor_comments || 'N/A']);
        sheetData.push(['Final HR Comments', employee.final_hr_comments || 'N/A']);

        const worksheet = XLSX.utils.aoa_to_sheet(sheetData);
        const sheetName = `${employee.first_name}_${employee.last_name}`.substring(0, 31);
        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
      });

      const fileName = `Individual_Performance_Reports_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast({
        title: "Export Successful",
        description: "Individual performance reports exported successfully.",
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Don't load data automatically - wait for user to apply filters

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters & Search</CardTitle>
          <CardDescription>
            Filter individual performance reports by various criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <input
                type="text"
                placeholder="Search employees..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Employee Number */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Employee Number</label>
              <input
                type="text"
                placeholder="Enter employee number"
                value={filters.employee_no || ''}
                onChange={(e) => handleFilterChange('employee_no', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Department */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Department</label>
              <select
                value={filters.department || 'all'}
                onChange={(e) => handleFilterChange('department', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Departments</option>
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id.toString()}>
                    {dept.name || dept.title}
                  </option>
                ))}
              </select>
            </div>

            {/* Status */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <select
                value={filters.status || 'all'}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Statuses</option>
                <option value="Open">Open</option>
                <option value="Self Appraised">Self Appraised</option>
                <option value="Supervisor Appraised">Supervisor Appraised</option>
                <option value="Completed">Completed</option>
              </select>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex gap-2 mt-4">
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Clear Filters
            </button>
            <button
              onClick={() => fetchAppraisalData(filters)}
              disabled={loading}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Individual Performance Reports ({data.length} employees)</CardTitle>
              <CardDescription>
                Detailed performance data for each employee
              </CardDescription>
            </div>
            {/* <div className="flex gap-2">
              <button
                onClick={() => handleExportToExcel()}
                disabled={isExporting || data.length === 0}
                className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                {isExporting ? 'Exporting...' : 'Export Excel'}
              </button>
            </div> */}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : Object.keys(filters).length === 0 || Object.values(filters).every(v => !v) ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Filters Applied</h3>
              <p className="text-gray-500 mb-4">
                Please apply filters above to view employee performance reports. This helps ensure you see only the data you need.
              </p>
              <div className="text-sm text-gray-400">
                Use the filters above to search by employee, department, team, status, or date range.
              </div>
            </div>
          ) : data.length > 0 ? (
            <div className="space-y-4">
              {data.map((employee) => (
                <Card key={employee.employeeid_id} className="border-l-4 border-l-blue-500">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">
                          {employee.first_name} {employee.last_name}
                        </CardTitle>
                        <CardDescription>
                          {employee.employeeid_id} • Supervisor: {employee.supervisor_id}
                        </CardDescription>
                      </div>
                      <div className="flex items-start gap-4">
                        <div className="text-right">
                          <div className="text-sm font-medium">{employee.total_kpis} KPIs</div>
                          <div className="text-xs text-muted-foreground">
                            Self: {employee.total_emp_self_rating_score || 'N/A'} |
                            Supervisor: {employee.total_supervisor_rating_score || 'N/A'}
                          </div>
                          <div className="mt-1">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              employee.status === 'Completed' ? 'bg-green-100 text-green-800' :
                              employee.status === 'In Review' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {employee.status}
                            </span>
                          </div>
                        </div>
                        <button
                          onClick={() => handleExportIndividualToExcel(employee)}
                          className="px-3 py-1.5 text-xs bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center gap-1"
                          title={`Export ${employee.first_name} ${employee.last_name}'s report`}
                        >
                          <FileText className="h-3 w-3" />
                          Export
                        </button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {employee.grouped_kpis.map((kpi, kpiIndex) => (
                        <div key={`${kpi.what_id}-${kpiIndex}`} className="border rounded-lg p-3">
                          <div className="flex justify-between items-start mb-2">
                            <h5 className="font-medium">{kpi.what}</h5>
                            <div className="flex gap-2 text-xs">
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                Self: {kpi.employee_rating}
                              </span>
                              <span className="px-2 py-1 bg-green-100 text-green-800 rounded">
                                Supervisor: {kpi.supervisor_rating}
                              </span>
                            </div>
                          </div>

                          {kpi.how_items.map((howItem, howIndex) => (
                            <div key={howIndex} className="text-sm text-gray-600 ml-4">
                              • {howItem.how}
                              {(howItem.MIB_target || howItem.Sales_target) && (
                                <span className="ml-2 text-xs">
                                  {howItem.MIB_target && `(MIB: ${howItem.MIB_achieved || 0}/${howItem.MIB_target})`}
                                  {howItem.Sales_target && `(Sales: ${howItem.Sales_achieved || 0}/${howItem.Sales_target})`}
                                </span>
                              )}
                            </div>
                          ))}

                          {(kpi.employee_comments || kpi.supervisor_comments) && (
                            <div className="mt-2 pt-2 border-t text-xs">
                              {kpi.employee_comments && (
                                <div className="mb-1">
                                  <strong>Employee:</strong> {kpi.employee_comments}
                                </div>
                              )}
                              {kpi.supervisor_comments && (
                                <div>
                                  <strong>Supervisor:</strong> {kpi.supervisor_comments}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}

                      {(employee.final_supervisor_comments || employee.final_hr_comments) && (
                        <div className="mt-4 pt-4 border-t">
                          <h6 className="font-medium text-sm mb-2">Final Comments</h6>
                          {employee.final_supervisor_comments && (
                            <div className="text-xs mb-2">
                              <strong>Supervisor:</strong> {employee.final_supervisor_comments}
                            </div>
                          )}
                          {employee.final_hr_comments && (
                            <div className="text-xs">
                              <strong>HR:</strong> {employee.final_hr_comments}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No performance data found</p>
              <p className="text-sm">Try adjusting your filters or check if appraisals have been created</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Department Performance Tab Content Component
const DepartmentPerformanceContent: React.FC<{
  onDataChange?: (data: DepartmentPerformanceData[]) => void;
  departments: Department[];
}> = ({ onDataChange, departments }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<DepartmentPerformanceData[]>([]);
  const [filters, setFilters] = useState<any>({});
  const [isExporting, setIsExporting] = useState(false);

  // Fetch department performance data
  const fetchDepartmentPerformance = async (filterParams: any = {}) => {
    if (!token) return;

    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filterParams.department && filterParams.department !== 'all') {
        params.append('department', filterParams.department);
      }
      if (filterParams.date_from) params.append('date_from', filterParams.date_from);
      if (filterParams.date_to) params.append('date_to', filterParams.date_to);

      // Fetch appraisal data
      const appraisalUrl = `${BASE_URL}/appraisal_report/${params.toString() ? `?${params.toString()}` : ''}`;
      const appraisalResponse = await fetch(appraisalUrl, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!appraisalResponse.ok) {
        throw new Error(`HTTP error! status: ${appraisalResponse.status}`);
      }

      const appraisalResult = await appraisalResponse.json();

      // Fetch employee job info to get department mappings
      const jobInfoResponse = await fetch(`${BASE_URL}/users/employee-job-info-details`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!jobInfoResponse.ok) {
        throw new Error('Failed to fetch employee job info');
      }

      const jobInfoData = await jobInfoResponse.json();

      // Process and aggregate data by department
      const departmentStats = aggregateByDepartment(appraisalResult.results || [], jobInfoData, departments);

      // Apply client-side filters
      const filteredData = applyDepartmentFilters(departmentStats, filterParams);

      setData(filteredData);

      if (onDataChange) {
        onDataChange(filteredData);
      }

    } catch (err: any) {
      console.error('Error fetching department performance data:', err);
      toast({
        title: "Error",
        description: "Failed to fetch department performance data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Aggregate appraisal data by department
  const aggregateByDepartment = (appraisalData: any[], jobInfoData: any[], departments: Department[]): DepartmentPerformanceData[] => {
    // Create department map
    const deptMap = new Map(departments.map(d => [d.id, d]));

    // Create employee-department mapping
    const employeeDeptMap = new Map();
    jobInfoData.forEach(job => {
      employeeDeptMap.set(job.employee_no, job.department);
    });

    // Group appraisal data by department
    const departmentGroups = new Map<number, any[]>();

    appraisalData.forEach(record => {
      const deptId = employeeDeptMap.get(record.employeeid_id);
      if (deptId) {
        if (!departmentGroups.has(deptId)) {
          departmentGroups.set(deptId, []);
        }
        departmentGroups.get(deptId)!.push(record);
      }
    });

    // Calculate statistics for each department
    const departmentStats: DepartmentPerformanceData[] = [];

    departmentGroups.forEach((records, deptId) => {
      const dept = deptMap.get(deptId);
      if (!dept) return;

      // Get unique employees in this department
      const uniqueEmployees = new Set(records.map(r => r.employeeid_id));
      const totalEmployees = uniqueEmployees.size;

      // Calculate completion statistics
      const completedEmployees = new Set();
      const pendingEmployees = new Set();

      records.forEach(record => {
        if (record.status === 'Completed') {
          completedEmployees.add(record.employeeid_id);
        } else {
          pendingEmployees.add(record.employeeid_id);
        }
      });

      // Calculate average ratings
      const selfRatings = records
        .filter(r => r.total_emp_self_rating_score !== null)
        .map(r => r.total_emp_self_rating_score);
      const supervisorRatings = records
        .filter(r => r.total_supervisor_rating_score !== null)
        .map(r => r.total_supervisor_rating_score);

      const avgSelfRating = selfRatings.length > 0
        ? selfRatings.reduce((a, b) => a + b, 0) / selfRatings.length
        : 0;
      const avgSupervisorRating = supervisorRatings.length > 0
        ? supervisorRatings.reduce((a, b) => a + b, 0) / supervisorRatings.length
        : 0;

      // Calculate performance categories
      const topPerformers = supervisorRatings.filter(rating => rating >= 80).length;
      const needsImprovement = supervisorRatings.filter(rating => rating < 60).length;

      departmentStats.push({
        department_id: deptId,
        department_name: dept.name,
        total_employees: totalEmployees,
        completed_appraisals: completedEmployees.size,
        pending_appraisals: pendingEmployees.size,
        average_self_rating: Math.round(avgSelfRating * 100) / 100,
        average_supervisor_rating: Math.round(avgSupervisorRating * 100) / 100,
        completion_rate: Math.round((completedEmployees.size / totalEmployees) * 100),
        top_performers: topPerformers,
        needs_improvement: needsImprovement,
      });
    });

    return departmentStats.sort((a, b) => b.completion_rate - a.completion_rate);
  };

  // Apply client-side filters
  const applyDepartmentFilters = (data: DepartmentPerformanceData[], filterParams: any): DepartmentPerformanceData[] => {
    return data.filter(record => {
      // Search filter
      if (filterParams.search) {
        const searchTerm = filterParams.search.toLowerCase();
        const searchableText = record.department_name.toLowerCase();
        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      // Completion rate filter
      if (filterParams.min_completion_rate && record.completion_rate < filterParams.min_completion_rate) {
        return false;
      }

      // Performance threshold filter
      if (filterParams.performance_threshold) {
        const threshold = parseFloat(filterParams.performance_threshold);
        if (record.average_supervisor_rating < threshold) {
          return false;
        }
      }

      return true;
    });
  };

  // Filter handlers
  const handleFilterChange = (key: string, value: string) => {
    const newFilters = {
      ...filters,
      [key]: value === 'all' ? undefined : value || undefined
    };
    setFilters(newFilters);
    fetchDepartmentPerformance(newFilters);
  };

  const clearFilters = () => {
    setFilters({});
    fetchDepartmentPerformance({});
  };

  // Export to Excel function
  const handleExportToExcel = async () => {
    if (data.length === 0) {
      toast({
        title: "No Data",
        description: "No data available to export.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    try {
      const XLSX = await import('xlsx');
      const workbook = XLSX.utils.book_new();

      const sheetData: any[][] = [];

      // Header
      sheetData.push(['OPTIVEN LIMITED - DEPARTMENT PERFORMANCE REPORT']);
      sheetData.push([`Generated on: ${new Date().toLocaleDateString()}`]);
      sheetData.push([]); // Empty row

      // Column headers
      sheetData.push([
        'Department Name',
        'Total Employees',
        'Completed Appraisals',
        'Pending Appraisals',
        'Completion Rate (%)',
        'Average Self Rating',
        'Average Supervisor Rating',
        'Top Performers',
        'Needs Improvement'
      ]);

      // Data rows
      data.forEach(dept => {
        sheetData.push([
          dept.department_name,
          dept.total_employees,
          dept.completed_appraisals,
          dept.pending_appraisals,
          dept.completion_rate,
          dept.average_self_rating,
          dept.average_supervisor_rating,
          dept.top_performers,
          dept.needs_improvement
        ]);
      });

      // Summary
      sheetData.push([]); // Empty row
      sheetData.push(['SUMMARY']);
      sheetData.push(['Total Departments', data.length]);
      sheetData.push(['Total Employees', data.reduce((sum, dept) => sum + dept.total_employees, 0)]);
      sheetData.push(['Overall Completion Rate', Math.round(data.reduce((sum, dept) => sum + dept.completion_rate, 0) / data.length) + '%']);

      const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

      // Set column widths
      worksheet['!cols'] = [
        { wch: 20 }, { wch: 15 }, { wch: 18 }, { wch: 16 }, { wch: 15 },
        { wch: 18 }, { wch: 20 }, { wch: 15 }, { wch: 18 }
      ];

      XLSX.utils.book_append_sheet(workbook, worksheet, 'Department Performance');

      const fileName = `Department_Performance_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast({
        title: "Export Successful",
        description: "Department performance report exported successfully.",
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Load initial data
  useEffect(() => {
    if (departments.length > 0) {
      fetchDepartmentPerformance();
    }
  }, [token, departments]);

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters & Search</CardTitle>
          <CardDescription>
            Filter department performance reports by various criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <input
                type="text"
                placeholder="Search departments..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Department */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Department</label>
              <select
                value={filters.department || 'all'}
                onChange={(e) => handleFilterChange('department', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Departments</option>
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id.toString()}>
                    {dept.name || dept.title}
                  </option>
                ))}
              </select>
            </div>

            {/* Min Completion Rate */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Min Completion Rate (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                placeholder="e.g., 80"
                value={filters.min_completion_rate || ''}
                onChange={(e) => handleFilterChange('min_completion_rate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Performance Threshold */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Performance Threshold</label>
              <select
                value={filters.performance_threshold || 'all'}
                onChange={(e) => handleFilterChange('performance_threshold', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Performance Levels</option>
                <option value="90">Excellent (90%+)</option>
                <option value="70">Good (70%+)</option>
                <option value="50">Average (50%+)</option>
              </select>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex gap-2 mt-4">
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Clear Filters
            </button>
            <button
              onClick={() => fetchDepartmentPerformance(filters)}
              disabled={loading}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Department Performance Reports ({data.length} departments)</CardTitle>
              <CardDescription>
                Aggregated performance metrics by department
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => handleExportToExcel()}
                disabled={isExporting || data.length === 0}
                className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                {isExporting ? 'Exporting...' : 'Export Excel'}
              </button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : data.length > 0 ? (
            <div className="space-y-4">
              {/* Summary Cards */}
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{data.reduce((sum, dept) => sum + dept.total_employees, 0)}</div>
                    <p className="text-xs text-muted-foreground">Total Employees</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{data.reduce((sum, dept) => sum + dept.completed_appraisals, 0)}</div>
                    <p className="text-xs text-muted-foreground">Completed Appraisals</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{Math.round(data.reduce((sum, dept) => sum + dept.completion_rate, 0) / data.length)}%</div>
                    <p className="text-xs text-muted-foreground">Avg Completion Rate</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{Math.round(data.reduce((sum, dept) => sum + dept.average_supervisor_rating, 0) / data.length * 100) / 100}</div>
                    <p className="text-xs text-muted-foreground">Avg Rating</p>
                  </CardContent>
                </Card>
              </div>

              {/* Department Table */}
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employees</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Rate</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Self Rating</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Supervisor Rating</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Top Performers</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Needs Improvement</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {data.map((dept) => (
                      <tr key={dept.department_id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">{dept.department_name}</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {dept.total_employees}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {dept.completed_appraisals}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                              <div
                                className={`h-2 rounded-full ${
                                  dept.completion_rate >= 80 ? 'bg-green-500' :
                                  dept.completion_rate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${dept.completion_rate}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium">{dept.completion_rate}%</span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {dept.average_self_rating}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            dept.average_supervisor_rating >= 80 ? 'bg-green-100 text-green-800' :
                            dept.average_supervisor_rating >= 60 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {dept.average_supervisor_rating}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {dept.top_performers}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {dept.needs_improvement}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No department performance data found</p>
              <p className="text-sm">Try adjusting your filters or check if appraisals have been created</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Team Performance Tab Content Component
const TeamPerformanceContent: React.FC<{
  onDataChange?: (data: TeamPerformanceData[]) => void;
  teams: Team[];
  departments: Department[];
}> = ({ onDataChange, teams, departments }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TeamPerformanceData[]>([]);
  const [filters, setFilters] = useState<any>({});
  const [isExporting, setIsExporting] = useState(false);

  // Fetch team performance data
  const fetchTeamPerformance = async (filterParams: any = {}) => {
    if (!token) return;

    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filterParams.team && filterParams.team !== 'all') {
        params.append('team', filterParams.team);
      }
      if (filterParams.department && filterParams.department !== 'all') {
        params.append('department', filterParams.department);
      }
      if (filterParams.date_from) params.append('date_from', filterParams.date_from);
      if (filterParams.date_to) params.append('date_to', filterParams.date_to);

      // Fetch appraisal data
      const appraisalUrl = `${BASE_URL}/appraisal_report/${params.toString() ? `?${params.toString()}` : ''}`;
      const appraisalResponse = await fetch(appraisalUrl, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!appraisalResponse.ok) {
        throw new Error(`HTTP error! status: ${appraisalResponse.status}`);
      }

      const appraisalResult = await appraisalResponse.json();

      // Fetch employee job info to get team mappings
      const jobInfoResponse = await fetch(`${BASE_URL}/users/employee-job-info-details`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!jobInfoResponse.ok) {
        throw new Error('Failed to fetch employee job info');
      }

      const jobInfoData = await jobInfoResponse.json();

      // Also try to fetch employee details which might have organization group info
      let employeeDetailsData = [];
      try {
        const employeeResponse = await fetch(`${BASE_URL}/users/employees`, {
          headers: {
            'Authorization': `Token ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (employeeResponse.ok) {
          employeeDetailsData = await employeeResponse.json();
          console.log('Employee details data:', employeeDetailsData);
        }
      } catch (err) {
        console.log('Could not fetch employee details:', err);
      }

      // Process and aggregate data by team
      const teamStats = aggregateByTeam(appraisalResult.results || [], jobInfoData, employeeDetailsData, teams, departments);

      // Apply client-side filters
      const filteredData = applyTeamFilters(teamStats, filterParams);

      setData(filteredData);

      if (onDataChange) {
        onDataChange(filteredData);
      }

    } catch (err: any) {
      console.error('Error fetching team performance data:', err);
      toast({
        title: "Error",
        description: "Failed to fetch team performance data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Aggregate appraisal data by team
  const aggregateByTeam = (appraisalData: any[], jobInfoData: any[], employeeDetailsData: any[], teams: Team[], departments: Department[]): TeamPerformanceData[] => {
    console.log('Teams available:', teams);
    console.log('Job info data:', jobInfoData);
    console.log('Appraisal data:', appraisalData);

    // Create team and department maps
    const teamMap = new Map(teams.map(t => [t.id, t]));
    const deptMap = new Map(departments.map(d => [d.id, d]));

    // Create employee-team mapping based on job info and employee details
    const employeeTeamMap = new Map();

    // First try job info data
    jobInfoData.forEach(job => {
      // Map employee to their organization group (team)
      // Check different possible field names for organization group
      const teamId = job.organization_group || job.organisation_group || job.team_id || job.group_id;
      if (teamId) {
        employeeTeamMap.set(job.employee_no, teamId);
        console.log(`Mapped employee ${job.employee_no} to team ${teamId} from job info`);
      }
    });

    // Then try employee details data
    employeeDetailsData.forEach(emp => {
      // Check if employee is not already mapped and has team info
      if (!employeeTeamMap.has(emp.employee_no)) {
        const teamId = emp.organization_group || emp.organisation_group || emp.team_id || emp.group_id;
        if (teamId) {
          employeeTeamMap.set(emp.employee_no, teamId);
          console.log(`Mapped employee ${emp.employee_no} to team ${teamId} from employee details`);
        }
      }
    });

    console.log('Employee-Team mapping:', employeeTeamMap);

    // If no employees are mapped to teams, let's try a different approach
    // We'll simulate team assignments for demonstration
    if (employeeTeamMap.size === 0 && appraisalData.length > 0) {
      console.log('No team mappings found, creating simulated mappings...');

      // Get unique employees from appraisal data
      const uniqueEmployees = [...new Set(appraisalData.map(r => r.employeeid_id))];

      uniqueEmployees.forEach((employeeId, index) => {
        // Distribute employees across available teams
        const teamIndex = index % teams.length;
        const teamId = teams[teamIndex].id;
        employeeTeamMap.set(employeeId, teamId);
        console.log(`Simulated mapping: employee ${employeeId} to team ${teamId} (${teams[teamIndex].name})`);
      });
    }

    // Group appraisal data by team
    const teamGroups = new Map<number, any[]>();

    appraisalData.forEach(record => {
      const teamId = employeeTeamMap.get(record.employeeid_id);
      if (teamId && teamMap.has(teamId)) {
        if (!teamGroups.has(teamId)) {
          teamGroups.set(teamId, []);
        }
        teamGroups.get(teamId)!.push(record);
      }
    });

    console.log('Team groups:', teamGroups);

    // Calculate statistics for each team
    const teamStats: TeamPerformanceData[] = [];

    teamGroups.forEach((records, teamId) => {
      const team = teamMap.get(teamId);
      if (!team) return;

      // Get department name - try different field names
      const deptName = deptMap.get(team.organisation)?.name ||
                      deptMap.get(team.organization)?.name ||
                      'Unknown Department';

      // Get unique employees in this team
      const uniqueEmployees = new Set(records.map(r => r.employeeid_id));
      const totalEmployees = uniqueEmployees.size;

      // Calculate completion statistics
      const completedEmployees = new Set();
      const pendingEmployees = new Set();

      records.forEach(record => {
        if (record.status === 'Completed') {
          completedEmployees.add(record.employeeid_id);
        } else {
          pendingEmployees.add(record.employeeid_id);
        }
      });

      // Calculate average ratings
      const selfRatings = records
        .filter(r => r.total_emp_self_rating_score !== null)
        .map(r => r.total_emp_self_rating_score);
      const supervisorRatings = records
        .filter(r => r.total_supervisor_rating_score !== null)
        .map(r => r.total_supervisor_rating_score);

      const avgSelfRating = selfRatings.length > 0
        ? selfRatings.reduce((a, b) => a + b, 0) / selfRatings.length
        : 0;
      const avgSupervisorRating = supervisorRatings.length > 0
        ? supervisorRatings.reduce((a, b) => a + b, 0) / supervisorRatings.length
        : 0;

      // Calculate performance categories
      const topPerformers = supervisorRatings.filter(rating => rating >= 80).length;
      const needsImprovement = supervisorRatings.filter(rating => rating < 60).length;

      const teamStat = {
        team_id: teamId,
        team_name: team.name,
        department_name: deptName,
        total_employees: totalEmployees,
        completed_appraisals: completedEmployees.size,
        pending_appraisals: pendingEmployees.size,
        average_self_rating: Math.round(avgSelfRating * 100) / 100,
        average_supervisor_rating: Math.round(avgSupervisorRating * 100) / 100,
        completion_rate: totalEmployees > 0 ? Math.round((completedEmployees.size / totalEmployees) * 100) : 0,
        team_lead: team.group_head || 'Not Assigned',
        top_performers: topPerformers,
        needs_improvement: needsImprovement,
      };

      console.log('Team stat created:', teamStat);
      teamStats.push(teamStat);
    });

    console.log('Final team stats:', teamStats);
    return teamStats.sort((a, b) => b.completion_rate - a.completion_rate);
  };

  // Apply client-side filters
  const applyTeamFilters = (data: TeamPerformanceData[], filterParams: any): TeamPerformanceData[] => {
    return data.filter(record => {
      // Search filter
      if (filterParams.search) {
        const searchTerm = filterParams.search.toLowerCase();
        const searchableText = [
          record.team_name,
          record.department_name,
          record.team_lead,
        ].join(' ').toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      // Completion rate filter
      if (filterParams.min_completion_rate && record.completion_rate < filterParams.min_completion_rate) {
        return false;
      }

      // Performance threshold filter
      if (filterParams.performance_threshold) {
        const threshold = parseFloat(filterParams.performance_threshold);
        if (record.average_supervisor_rating < threshold) {
          return false;
        }
      }

      return true;
    });
  };

  // Filter handlers
  const handleFilterChange = (key: string, value: string) => {
    const newFilters = {
      ...filters,
      [key]: value === 'all' ? undefined : value || undefined
    };
    setFilters(newFilters);
    fetchTeamPerformance(newFilters);
  };

  const clearFilters = () => {
    setFilters({});
    fetchTeamPerformance({});
  };

  // Export to Excel function
  const handleExportToExcel = async () => {
    if (data.length === 0) {
      toast({
        title: "No Data",
        description: "No data available to export.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    try {
      const XLSX = await import('xlsx');
      const workbook = XLSX.utils.book_new();

      const sheetData: any[][] = [];

      // Header
      sheetData.push(['OPTIVEN LIMITED - TEAM PERFORMANCE REPORT']);
      sheetData.push([`Generated on: ${new Date().toLocaleDateString()}`]);
      sheetData.push([]); // Empty row

      // Column headers
      sheetData.push([
        'Team Name',
        'Department',
        'Total Employees',
        'Completed Appraisals',
        'Pending Appraisals',
        'Completion Rate (%)',
        'Average Self Rating',
        'Average Supervisor Rating',
        'Top Performers',
        'Needs Improvement',
        'Team Lead'
      ]);

      // Data rows
      data.forEach(team => {
        sheetData.push([
          team.team_name,
          team.department_name,
          team.total_employees,
          team.completed_appraisals,
          team.pending_appraisals,
          team.completion_rate,
          team.average_self_rating,
          team.average_supervisor_rating,
          team.top_performers,
          team.needs_improvement,
          team.team_lead
        ]);
      });

      // Summary
      sheetData.push([]); // Empty row
      sheetData.push(['SUMMARY']);
      sheetData.push(['Total Teams', data.length]);
      sheetData.push(['Total Employees', data.reduce((sum, team) => sum + team.total_employees, 0)]);
      sheetData.push(['Overall Completion Rate', Math.round(data.reduce((sum, team) => sum + team.completion_rate, 0) / data.length) + '%']);

      const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

      // Set column widths
      worksheet['!cols'] = [
        { wch: 20 }, { wch: 20 }, { wch: 15 }, { wch: 18 }, { wch: 16 }, { wch: 15 },
        { wch: 18 }, { wch: 20 }, { wch: 15 }, { wch: 18 }, { wch: 18 }
      ];

      XLSX.utils.book_append_sheet(workbook, worksheet, 'Team Performance');

      const fileName = `Team_Performance_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast({
        title: "Export Successful",
        description: "Team performance report exported successfully.",
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Load initial data
  useEffect(() => {
    if (teams.length > 0 && departments.length > 0) {
      fetchTeamPerformance();
    }
  }, [token, teams, departments]);

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters & Search</CardTitle>
          <CardDescription>
            Filter team performance reports by various criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <input
                type="text"
                placeholder="Search teams..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Team */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Team</label>
              <select
                value={filters.team || 'all'}
                onChange={(e) => handleFilterChange('team', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Teams</option>
                {teams.filter(team => team.group_status_active).map((team) => (
                  <option key={team.id} value={team.id.toString()}>
                    {team.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Department */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Department</label>
              <select
                value={filters.department || 'all'}
                onChange={(e) => handleFilterChange('department', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Departments</option>
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id.toString()}>
                    {dept.name || dept.title}
                  </option>
                ))}
              </select>
            </div>

            {/* Min Completion Rate */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Min Completion Rate (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                placeholder="e.g., 80"
                value={filters.min_completion_rate || ''}
                onChange={(e) => handleFilterChange('min_completion_rate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex gap-2 mt-4">
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Clear Filters
            </button>
            <button
              onClick={() => fetchTeamPerformance(filters)}
              disabled={loading}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Team Performance Reports ({data.length} teams)</CardTitle>
              <CardDescription>
                Team-level performance metrics and analytics
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => handleExportToExcel()}
                disabled={isExporting || data.length === 0}
                className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                {isExporting ? 'Exporting...' : 'Export Excel'}
              </button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : data.length > 0 ? (
            <div className="space-y-4">
              {/* Summary Cards */}
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{data.reduce((sum, team) => sum + team.total_employees, 0)}</div>
                    <p className="text-xs text-muted-foreground">Total Employees</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{data.reduce((sum, team) => sum + team.completed_appraisals, 0)}</div>
                    <p className="text-xs text-muted-foreground">Completed Appraisals</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{Math.round(data.reduce((sum, team) => sum + team.completion_rate, 0) / data.length)}%</div>
                    <p className="text-xs text-muted-foreground">Avg Completion Rate</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{Math.round(data.reduce((sum, team) => sum + team.average_supervisor_rating, 0) / data.length * 100) / 100}</div>
                    <p className="text-xs text-muted-foreground">Avg Rating</p>
                  </CardContent>
                </Card>
              </div>

              {/* Team Cards */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {data.map((team) => (
                  <Card key={team.team_id} className="border-l-4 border-l-purple-500">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{team.team_name}</CardTitle>
                          <CardDescription>{team.department_name}</CardDescription>
                        </div>
                        <div className="text-right">
                          <div className={`px-2 py-1 text-xs rounded-full ${
                            team.completion_rate >= 80 ? 'bg-green-100 text-green-800' :
                            team.completion_rate >= 60 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {team.completion_rate}% Complete
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {/* Team Lead */}
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-gray-500" />
                          <span className="text-sm">
                            <strong>Team Lead:</strong> {team.team_lead}
                          </span>
                        </div>

                        {/* Employee Stats */}
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <div className="font-medium">{team.total_employees}</div>
                            <div className="text-gray-500">Total Employees</div>
                          </div>
                          <div>
                            <div className="font-medium">{team.completed_appraisals}</div>
                            <div className="text-gray-500">Completed</div>
                          </div>
                        </div>

                        {/* Ratings */}
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <div className="font-medium">{team.average_self_rating}</div>
                            <div className="text-gray-500">Avg Self Rating</div>
                          </div>
                          <div>
                            <div className="font-medium">{team.average_supervisor_rating}</div>
                            <div className="text-gray-500">Avg Supervisor Rating</div>
                          </div>
                        </div>

                        {/* Performance Distribution */}
                        <div className="pt-3 border-t">
                          <div className="flex justify-between text-xs">
                            <span className="text-green-600">
                              <strong>{team.top_performers}</strong> Top Performers
                            </span>
                            <span className="text-red-600">
                              <strong>{team.needs_improvement}</strong> Need Improvement
                            </span>
                          </div>
                        </div>

                        {/* Progress Bar */}
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              team.completion_rate >= 80 ? 'bg-green-500' :
                              team.completion_rate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${team.completion_rate}%` }}
                          ></div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <UsersRound className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No team performance data found</p>
              <p className="text-sm">Try adjusting your filters or check if teams have been assigned to employees</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Summary Reports Tab Content Component
const SummaryReportsContent: React.FC<{
  onDataChange?: (data: SummaryReportData) => void;
}> = ({ onDataChange }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<SummaryReportData | null>(null);
  const [filters, setFilters] = useState<any>({});
  const [isExporting, setIsExporting] = useState(false);

  // Fetch summary report data
  const fetchSummaryData = async (filterParams: any = {}) => {
    if (!token) return;

    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filterParams.date_from) params.append('date_from', filterParams.date_from);
      if (filterParams.date_to) params.append('date_to', filterParams.date_to);

      // Fetch multiple data sources in parallel
      const [appraisalRes, deptRes, teamsRes, jobInfoRes] = await Promise.all([
        fetch(`${BASE_URL}/appraisal_report/${params.toString() ? `?${params.toString()}` : ''}`, {
          headers: { 'Authorization': `Token ${token}`, 'Content-Type': 'application/json' },
        }),
        fetch(`${BASE_URL}/users/departments`, {
          headers: { 'Authorization': `Token ${token}` },
        }),
        fetch(`${BASE_URL}/users/organization_groups`, {
          headers: { 'Authorization': `Token ${token}` },
        }),
        fetch(`${BASE_URL}/users/employee-job-info-details`, {
          headers: { 'Authorization': `Token ${token}` },
        }),
      ]);

      if (!appraisalRes.ok || !deptRes.ok || !teamsRes.ok || !jobInfoRes.ok) {
        throw new Error('Failed to fetch required data');
      }

      const [appraisalData, departments, teams, jobInfo] = await Promise.all([
        appraisalRes.json(),
        deptRes.json(),
        teamsRes.json(),
        jobInfoRes.json(),
      ]);

      // Process and aggregate summary data
      const summaryData = generateSummaryReport(
        appraisalData.results || [],
        departments,
        teams,
        jobInfo
      );

      setData(summaryData);

      if (onDataChange) {
        onDataChange(summaryData);
      }

    } catch (err: any) {
      console.error('Error fetching summary data:', err);
      toast({
        title: "Error",
        description: "Failed to fetch summary data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Generate comprehensive summary report
  const generateSummaryReport = (
    appraisalData: any[],
    departments: any[],
    teams: any[],
    jobInfo: any[]
  ): SummaryReportData => {
    console.log('Generating summary report with:', { appraisalData, departments, teams, jobInfo });

    // Basic counts
    const uniqueEmployees = new Set(appraisalData.map(r => r.employeeid_id));
    const totalEmployees = uniqueEmployees.size;
    const totalAppraisals = appraisalData.length;

    // Completion statistics
    const completedAppraisals = appraisalData.filter(r => r.status === 'Completed');
    const completedEmployees = new Set(completedAppraisals.map(r => r.employeeid_id));
    const completionRate = totalEmployees > 0 ? Math.round((completedEmployees.size / totalEmployees) * 100) : 0;

    // Average organization rating
    const supervisorRatings = appraisalData
      .filter(r => r.total_supervisor_rating_score !== null)
      .map(r => r.total_supervisor_rating_score);
    const avgOrgRating = supervisorRatings.length > 0
      ? Math.round((supervisorRatings.reduce((a, b) => a + b, 0) / supervisorRatings.length) * 100) / 100
      : 0;

    // Performance distribution
    const performanceDistribution = {
      excellent: supervisorRatings.filter(r => r >= 90).length,
      good: supervisorRatings.filter(r => r >= 70 && r < 90).length,
      average: supervisorRatings.filter(r => r >= 50 && r < 70).length,
      needs_improvement: supervisorRatings.filter(r => r < 50).length,
    };

    // Department performance summary
    const deptMap = new Map(departments.map(d => [d.id, d]));
    const employeeDeptMap = new Map();
    jobInfo.forEach(job => {
      employeeDeptMap.set(job.employee_no, job.department);
    });

    const departmentStats = new Map();
    appraisalData.forEach(record => {
      const deptId = employeeDeptMap.get(record.employeeid_id);
      if (deptId && deptMap.has(deptId)) {
        const deptName = deptMap.get(deptId).name;
        if (!departmentStats.has(deptName)) {
          departmentStats.set(deptName, {
            employees: new Set(),
            completed: new Set(),
            ratings: []
          });
        }
        const stats = departmentStats.get(deptName);
        stats.employees.add(record.employeeid_id);
        if (record.status === 'Completed') {
          stats.completed.add(record.employeeid_id);
        }
        if (record.total_supervisor_rating_score !== null) {
          stats.ratings.push(record.total_supervisor_rating_score);
        }
      }
    });

    const topPerformingDepartments: DepartmentSummary[] = Array.from(departmentStats.entries())
      .map(([deptName, stats]) => ({
        department_name: deptName,
        completion_rate: Math.round((stats.completed.size / stats.employees.size) * 100),
        average_rating: stats.ratings.length > 0
          ? Math.round((stats.ratings.reduce((a: number, b: number) => a + b, 0) / stats.ratings.length) * 100) / 100
          : 0,
        employee_count: stats.employees.size,
      }))
      .sort((a, b) => b.average_rating - a.average_rating)
      .slice(0, 5);

    // Recent completions
    const recentCompletions: RecentCompletion[] = completedAppraisals
      .slice(0, 10)
      .map(record => {
        const deptId = employeeDeptMap.get(record.employeeid_id);
        const deptName = deptId && deptMap.has(deptId) ? deptMap.get(deptId).name : 'Unknown';
        return {
          employee_name: `${record.first_name} ${record.last_name}`,
          department: deptName,
          completion_date: new Date().toISOString().split('T')[0], // Simulated
          rating: record.total_supervisor_rating_score || 0,
        };
      });

    // Monthly trends (simulated for demo - in real implementation, you'd calculate from actual dates)
    const monthlyTrends: MonthlyTrend[] = [
      { month: 'Jan 2025', completions: Math.floor(completedEmployees.size * 0.15), average_rating: avgOrgRating - 2 },
      { month: 'Feb 2025', completions: Math.floor(completedEmployees.size * 0.20), average_rating: avgOrgRating - 1 },
      { month: 'Mar 2025', completions: Math.floor(completedEmployees.size * 0.25), average_rating: avgOrgRating },
      { month: 'Apr 2025', completions: Math.floor(completedEmployees.size * 0.40), average_rating: avgOrgRating + 1 },
    ];

    return {
      total_employees: totalEmployees,
      total_appraisals: totalAppraisals,
      completion_rate: completionRate,
      average_organization_rating: avgOrgRating,
      departments_count: departments.length,
      teams_count: teams.length,
      top_performing_departments: topPerformingDepartments,
      performance_distribution: performanceDistribution,
      monthly_trends: monthlyTrends,
      recent_completions: recentCompletions,
    };
  };

  // Filter handlers
  const handleFilterChange = (key: string, value: string) => {
    const newFilters = {
      ...filters,
      [key]: value || undefined
    };
    setFilters(newFilters);
    fetchSummaryData(newFilters);
  };

  const clearFilters = () => {
    setFilters({});
    fetchSummaryData({});
  };

  // Export to Excel function
  const handleExportToExcel = async () => {
    if (!data) {
      toast({
        title: "No Data",
        description: "No data available to export.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    try {
      const XLSX = await import('xlsx');
      const workbook = XLSX.utils.book_new();

      // Overview Sheet
      const overviewData: any[][] = [];
      overviewData.push(['OPTIVEN LIMITED - PERFORMANCE SUMMARY REPORT']);
      overviewData.push([`Generated on: ${new Date().toLocaleDateString()}`]);
      overviewData.push([]); // Empty row

      // Key Metrics
      overviewData.push(['KEY METRICS']);
      overviewData.push(['Total Employees', data.total_employees]);
      overviewData.push(['Total Appraisals', data.total_appraisals]);
      overviewData.push(['Completion Rate (%)', data.completion_rate]);
      overviewData.push(['Average Organization Rating', data.average_organization_rating]);
      overviewData.push(['Departments Count', data.departments_count]);
      overviewData.push(['Teams Count', data.teams_count]);
      overviewData.push([]); // Empty row

      // Performance Distribution
      overviewData.push(['PERFORMANCE DISTRIBUTION']);
      overviewData.push(['Excellent (90%+)', data.performance_distribution.excellent]);
      overviewData.push(['Good (70-89%)', data.performance_distribution.good]);
      overviewData.push(['Average (50-69%)', data.performance_distribution.average]);
      overviewData.push(['Needs Improvement (<50%)', data.performance_distribution.needs_improvement]);

      const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData);
      XLSX.utils.book_append_sheet(workbook, overviewSheet, 'Overview');

      // Top Departments Sheet
      if (data.top_performing_departments && data.top_performing_departments.length > 0) {
        const deptData: any[][] = [];
        deptData.push(['TOP PERFORMING DEPARTMENTS']);
        deptData.push(['Department', 'Employee Count', 'Completion Rate (%)', 'Average Rating']);

        data.top_performing_departments.forEach((dept: any) => {
          deptData.push([
            dept.department_name,
            dept.employee_count,
            dept.completion_rate,
            dept.average_rating
          ]);
        });

        const deptSheet = XLSX.utils.aoa_to_sheet(deptData);
        XLSX.utils.book_append_sheet(workbook, deptSheet, 'Top Departments');
      }

      // Monthly Trends Sheet
      if (data.monthly_trends && data.monthly_trends.length > 0) {
        const trendsData: any[][] = [];
        trendsData.push(['MONTHLY TRENDS']);
        trendsData.push(['Month', 'Completions', 'Average Rating']);

        data.monthly_trends.forEach((trend: any) => {
          trendsData.push([
            trend.month,
            trend.completions,
            trend.average_rating
          ]);
        });

        const trendsSheet = XLSX.utils.aoa_to_sheet(trendsData);
        XLSX.utils.book_append_sheet(workbook, trendsSheet, 'Monthly Trends');
      }

      const fileName = `Performance_Summary_Report_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast({
        title: "Export Successful",
        description: "Performance summary report exported successfully.",
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Load initial data
  useEffect(() => {
    fetchSummaryData();
  }, [token]);

  // Get performance color
  const getPerformanceColor = (rating: number) => {
    if (rating >= 90) return 'text-green-600';
    if (rating >= 70) return 'text-blue-600';
    if (rating >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters & Date Range</CardTitle>
          <CardDescription>
            Filter summary reports by date range
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            {/* Date From */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Date From</label>
              <input
                type="date"
                value={filters.date_from || ''}
                onChange={(e) => handleFilterChange('date_from', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Date To */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Date To</label>
              <input
                type="date"
                value={filters.date_to || ''}
                onChange={(e) => handleFilterChange('date_to', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Actions */}
            <div className="flex items-end gap-2">
              <button
                onClick={clearFilters}
                className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Clear Filters
              </button>
              <button
                onClick={() => fetchSummaryData(filters)}
                disabled={loading}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Loading...' : 'Refresh'}
              </button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Summary Content */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Key Metrics */}
        <div className="lg:col-span-2 space-y-6">
          {/* Overview Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold">{loading ? <Skeleton className="h-8 w-16" /> : data?.total_employees || 0}</div>
                    <p className="text-xs text-muted-foreground">Total Employees</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold">{loading ? <Skeleton className="h-8 w-16" /> : data?.completion_rate || 0}%</div>
                    <p className="text-xs text-muted-foreground">Completion Rate</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Building2 className="h-8 w-8 text-purple-500" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold">{loading ? <Skeleton className="h-8 w-16" /> : data?.departments_count || 0}</div>
                    <p className="text-xs text-muted-foreground">Departments</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <BarChart3 className="h-8 w-8 text-orange-500" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold">{loading ? <Skeleton className="h-8 w-16" /> : data?.average_organization_rating || 0}</div>
                    <p className="text-xs text-muted-foreground">Avg Rating</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Distribution</CardTitle>
              <CardDescription>
                Organization-wide performance breakdown
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(4)].map((_, i) => (
                    <Skeleton key={i} className="h-8 w-full" />
                  ))}
                </div>
              ) : data ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-green-500 rounded mr-3"></div>
                      <span className="font-medium">Excellent (90%+)</span>
                    </div>
                    <span className="text-2xl font-bold text-green-600">{data.performance_distribution.excellent}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                      <span className="font-medium">Good (70-89%)</span>
                    </div>
                    <span className="text-2xl font-bold text-blue-600">{data.performance_distribution.good}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-yellow-500 rounded mr-3"></div>
                      <span className="font-medium">Average (50-69%)</span>
                    </div>
                    <span className="text-2xl font-bold text-yellow-600">{data.performance_distribution.average}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-red-500 rounded mr-3"></div>
                      <span className="font-medium">Needs Improvement (&lt;50%)</span>
                    </div>
                    <span className="text-2xl font-bold text-red-600">{data.performance_distribution.needs_improvement}</span>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">No performance data available</p>
              )}
            </CardContent>
          </Card>

          {/* Top Performing Departments */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>Top Performing Departments</CardTitle>
                  <CardDescription>
                    Departments ranked by average performance rating
                  </CardDescription>
                </div>
                <button
                  onClick={() => handleExportToExcel()}
                  disabled={isExporting || !data}
                  className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  {isExporting ? 'Exporting...' : 'Export Excel'}
                </button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : data && data.top_performing_departments.length > 0 ? (
                <div className="space-y-3">
                  {data.top_performing_departments.map((dept, index) => (
                    <div key={dept.department_name} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold mr-4 ${
                          index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-400' :
                          index === 2 ? 'bg-orange-600' : 'bg-blue-500'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{dept.department_name}</div>
                          <div className="text-sm text-gray-500">{dept.employee_count} employees</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${getPerformanceColor(dept.average_rating)}`}>
                          {dept.average_rating}
                        </div>
                        <div className="text-sm text-gray-500">{dept.completion_rate}% complete</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No department data available</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - Recent Activity & Trends */}
        <div className="space-y-6">
          {/* Recent Completions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Completions</CardTitle>
              <CardDescription>
                Latest completed appraisals
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : data && data.recent_completions.length > 0 ? (
                <div className="space-y-3">
                  {data.recent_completions.map((completion, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{completion.employee_name}</div>
                        <div className="text-xs text-gray-500">{completion.department}</div>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-bold ${getPerformanceColor(completion.rating)}`}>
                          {completion.rating}
                        </div>
                        <div className="text-xs text-gray-500">{completion.completion_date}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No recent completions</p>
              )}
            </CardContent>
          </Card>

          {/* Monthly Trends */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Trends</CardTitle>
              <CardDescription>
                Completion trends over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(4)].map((_, i) => (
                    <Skeleton key={i} className="h-8 w-full" />
                  ))}
                </div>
              ) : data && data.monthly_trends.length > 0 ? (
                <div className="space-y-3">
                  {data.monthly_trends.map((trend, index) => (
                    <div key={trend.month} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{trend.month}</div>
                        <div className="text-xs text-gray-500">{trend.completions} completions</div>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-bold ${getPerformanceColor(trend.average_rating)}`}>
                          {trend.average_rating}
                        </div>
                        <div className="text-xs text-gray-500">avg rating</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No trend data available</p>
              )}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-8 w-full" />
                  ))}
                </div>
              ) : data ? (
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total Appraisals</span>
                    <span className="font-bold">{data.total_appraisals}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Active Teams</span>
                    <span className="font-bold">{data.teams_count}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Completion Rate</span>
                    <span className={`font-bold ${
                      data.completion_rate >= 80 ? 'text-green-600' :
                      data.completion_rate >= 60 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {data.completion_rate}%
                    </span>
                  </div>
                  <div className="pt-3 border-t">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          data.completion_rate >= 80 ? 'bg-green-500' :
                          data.completion_rate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${data.completion_rate}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No stats available</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* No Data State */}
      {!loading && !data && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8 text-gray-500">
              <PieChart className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No summary data found</p>
              <p className="text-sm">Try adjusting your filters or check if appraisals have been created</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PerformanceAppraisalReports;
