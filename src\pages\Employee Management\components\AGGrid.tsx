import { useMemo, useRef, useState } from 'react'
import { AgGridReact } from 'ag-grid-react';
import { AllCommunityModule, RowSelectionOptions, ColDef, colorSchemeDarkgreen,colorSchemeLight, ModuleRegistry } from 'ag-grid-community';
import { themeQuartz } from 'ag-grid-community';
import styles from '@/components/styles/clients.module.css'


const myTheme = themeQuartz
  .withPart(colorSchemeLight) // Changed from colorSchemeDarkgreen to colorSchemeLight
  .withParams({
    spacing: 12,
    backgroundColor: '#ffffff', // Changed from dark to white
    textColor: '#333333', // Changed from white to dark text
    borderColor: '#afb8c4', // Lightened border color
    checkboxCheckedShapeColor: '#4f46e5', // Changed to a purple/indigo color
    checkboxCheckedBackgroundColor: '#ffffff', // Kept white
    checkboxUncheckedBackgroundColor: '#ffffff', // Kept white
    fontSize: '13px',
    headerBackgroundColor: '#f8fafc', // Light gray header background
  });

  const myDarkTheme = themeQuartz
  .withPart(colorSchemeDarkgreen)
  .withParams({
    spacing: 12,
    backgroundColor: '#0a0a0a',
    textColor: 'white',
    borderColor: '#1e293b',
    checkboxCheckedShapeColor: '#1e293b',
    checkboxCheckedBackgroundColor: 'white',
    checkboxUncheckedBackgroundColor: 'white',
    fontSize:'13px',
    headerBackgroundColor:'#0a0a0a',
    
  });

interface A {
  make: string,
  model: string,
  price: number,
  electric: boolean,
}
interface AG_GridProps {
  rowData: any[];
  columnDefs: ColDef<any>[];
}

export default function AG_Grid({ rowData, columnDefs }: AG_GridProps){
    ModuleRegistry.registerModules([AllCommunityModule]);

    const gridRef = useRef<AgGridReact<any>>(null);

    const [selectedRow,setSelectedRow]=useState({})
  
    const rowSelection: RowSelectionOptions = useMemo(() => {
      return {
        mode: 'multiRow',
        pinned: 'left',
        selectAll: 'filtered',
        enableClickSelection: true,
      };
    }, []);
  
    const onSelectionChanged = () => {
      const selectedNodes = gridRef.current?.api.getSelectedNodes();
      const selectedData = selectedNodes?.map(node => node.data);
      setSelectedRow(selectedData as any)
      // console.log('Selected Rows:', selectedData);
    };
  
  
    return (
  
      <AgGridReact
        rowData={rowData}
        columnDefs={columnDefs}
        theme={myTheme}
        pagination={true}
        paginationPageSize={10}
        paginationPageSizeSelector={[10, 25, 50]}
        rowSelection={rowSelection}
        onSelectionChanged={onSelectionChanged}
        ref={gridRef}
        
      />
  
    )
}