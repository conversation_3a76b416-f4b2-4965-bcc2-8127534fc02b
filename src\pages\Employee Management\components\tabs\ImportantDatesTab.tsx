import { useEffect, useState } from "react";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { EmployeeImportantDates } from "../../types/EmployeeTypes";
import { updateOrCreateImportantDatesRecord } from "../../utils/EmployeeRecords";
import { TabLayout, FormGrid, FormField } from "../TabLayout";
import { Calendar } from "lucide-react";

import { BASE_URL } from "@/config";

interface ImportantDatesTabProps {
  employeeNo: string;
  onSaveSuccess?: () => void;
}

export default function ImportantDatesTab({ employeeNo, onSaveSuccess }: ImportantDatesTabProps) {
  const token = useSelector((state: RootState) => state.auth.token);
  const [datesData, setDatesData] = useState<EmployeeImportantDates | null>(null);
  const [loading, setLoading] = useState(true);

  // Function to fetch important dates data
  async function fetchImportantDatesData() {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }

    setLoading(true);
    try {
      const headers = { Authorization: `Token ${token}` };
      const params = { employee_no: employeeNo };
      console.log("Fetching Important Dates for employee:", employeeNo);
      const res = await axios.get(
        `${BASE_URL}/users/employee-important-dates-details`,
        { headers, params }
      );
      console.log("Response from Important Dates API:", res.data);

      if (Array.isArray(res.data)) {
        // Find all records for this employee
        const records = res.data.filter(
          (item: EmployeeImportantDates) => item.employee_no === employeeNo
        );

        console.log(`Found ${records.length} important dates records for employee ${employeeNo}:`, records);

        if (records.length > 0) {
          // Always use the record with the highest ID (most recent)
          const record = records.reduce((prev, current) =>
            (prev.id && current.id && prev.id > current.id) ? prev : current
          );

          console.log("Selected important dates record:", record);

          // Ensure proper data structure with all required fields
          const datesRecord: EmployeeImportantDates = {
            id: record.id,
            employee_no: employeeNo,
            date_of_current_appointment: record.date_of_current_appointment || "",
            date_of_leaveing: record.date_of_leaveing || null,
          };

          setDatesData(datesRecord);
          return true;
        } else {
          console.log("No important dates record found, creating empty record");
          setDatesData({
            employee_no: employeeNo,
            date_of_current_appointment: "",
            date_of_leaveing: null,
          });
        }
      }
    } catch (err: any) {
      console.error("Error fetching Important Dates:", err);
      toast.error("Failed to fetch Important Dates");
      setDatesData({
        employee_no: employeeNo,
        date_of_current_appointment: "",
        date_of_leaveing: null,
      });
    } finally {
      setLoading(false);
    }
    return false;
  }

  useEffect(() => {
    if (!token || !employeeNo) {
      console.error("Token or employeeNo is not available");
      return;
    }

    fetchImportantDatesData();
  }, [token, employeeNo]);

  // Add a separate effect to handle refresh when component remounts
  useEffect(() => {
    console.log("ImportantDatesTab component mounted/refreshed for employee:", employeeNo);
    if (token && employeeNo) {
      fetchImportantDatesData();
    }
  }, []); // Empty dependency array means this runs on mount

  async function handleSave() {
    if (!datesData || !token || !employeeNo) return;

    // Validate required fields
    if (!datesData.date_of_current_appointment) {
      toast.error("Date of current appointment is required");
      return;
    }

    try {
      // Prepare data for API
      const dataToSend = {
        ...datesData,
        employee_no: employeeNo,
        // Handle empty date fields - convert empty strings to null
        date_of_leaveing: datesData.date_of_leaveing && datesData.date_of_leaveing.trim() !== ''
          ? datesData.date_of_leaveing
          : null
      };

      console.log("Sending important dates data from component:", dataToSend);

      const updated = await updateOrCreateImportantDatesRecord<EmployeeImportantDates>(
        `${BASE_URL}/users/employee-important-dates-details`,
        token,
        dataToSend
      );

      if (updated) {
        console.log("Important dates data updated successfully:", updated);

        // Update local state with the returned data, ensuring proper structure
        const updatedDatesData: EmployeeImportantDates = {
          id: updated.id,
          employee_no: employeeNo,
          date_of_current_appointment: updated.date_of_current_appointment || "",
          date_of_leaveing: updated.date_of_leaveing || null,
        };

        setDatesData(updatedDatesData);
        toast.success("Important dates saved successfully");

        // Force refresh the data after save to ensure UI is up to date
        await fetchImportantDatesData();

        // Call the onSaveSuccess callback if provided
        if (onSaveSuccess) {
          onSaveSuccess();
        }
      } else {
        toast.error("Failed to save important dates - no data returned");
      }
    } catch (error) {
      console.error("Error saving Important Dates:", error);
      toast.error("Failed to save Important dates");
    }
  }

  if (loading) {
    return null;
  }
  if (!datesData) return null;

  return (
    <TabLayout
      title="Important Dates"
      description="Manage employee important dates and milestones"
      employeeNo={employeeNo}
      loading={loading}
      actions={
        <Button
          onClick={handleSave}
          className="flex items-center gap-2"
        >
          <Calendar className="h-4 w-4" />
          Save Dates
        </Button>
      }
    >
      <FormGrid>
        <FormField>
          <Label>Date of Current Appointment *</Label>
          <Input
            type="date"
            value={datesData.date_of_current_appointment}
            onChange={(e) =>
              setDatesData((prev) =>
                prev && { ...prev, date_of_current_appointment: e.target.value }
              )
            }
            className="w-full"
          />
          <p className="text-xs text-muted-foreground">
            The date when the employee was appointed to their current position
          </p>
        </FormField>

        <FormField>
          <Label>Date of Leaving</Label>
          <Input
            type="date"
            value={datesData.date_of_leaveing || ""}
            onChange={(e) =>
              setDatesData((prev) => prev && { ...prev, date_of_leaveing: e.target.value || null })
            }
            className="w-full"
          />
          <p className="text-xs text-muted-foreground">
            The date when the employee left or will leave the organization (optional)
          </p>
        </FormField>
      </FormGrid>
    </TabLayout>
  );
}
