
// import { useCreateCampaignTabsStore } from '../lib/store/TabStore'
import { ReactNode } from 'react'

interface props {
    label: string,
    content: ReactNode
}

interface tabs {
    tabs: props[]
    a:string
}

const CreateTabs = (props: tabs) => {
    // const b = useCreateCampaignTabsStore((state) => state.label)

    return (
        <div>
            {
                props.tabs.map((tab, index) =>
                    tab.label === props.a && <span key={index}>{tab.content}</span>
                )
            }
        </div>
    )
}

export default CreateTabs