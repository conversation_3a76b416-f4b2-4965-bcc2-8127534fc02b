import React from 'react';
import { Button } from '@/components/ui/button'; // Shadcn Button
import { Filter, ChevronDown } from 'lucide-react'; // Use Lucide icons

type CycleType = 'Monthly' | 'Quarterly' | 'Yearly';

interface AppraisalPeriodFiltersProps {
  selectedType: CycleType | 'all';
  onTypeChange: (type: CycleType | 'all') => void;
  onAdvancedFilter: () => void;
}

const periodTypes: Array<CycleType | 'all'> = [
  'all',
  'Monthly',
  'Quarterly',
  'Yearly'
];

export const AppraisalPeriodFilters: React.FC<AppraisalPeriodFiltersProps> = ({
  selectedType,
  onTypeChange,
  onAdvancedFilter
}) => {
  return (
    <div className="flex items-center gap-4 flex-wrap justify-between">
      <div className="flex gap-4">
        {periodTypes.map((type) => (
          <Button
            key={type}
            onClick={() => onTypeChange(type)}
            variant={selectedType === type ? 'default' : 'outline'}
            className={`capitalize transition-all duration-300 ease-in-out ${selectedType === type ? 'bg-green-600 text-white' : 'text-green-600'}`}
          >
            {type === 'all' ? 'All Periods' : type}
          </Button>
        ))}
      </div>

      <Button
        onClick={onAdvancedFilter}
        variant="outline"
        className="flex items-center text-green-600 hover:text-green-800 border-green-300 hover:border-green-400 transition-all duration-200"
      >
        <Filter size={16} className="mr-2" />
        Advanced Filters
        <ChevronDown size={16} className="ml-2" />
      </Button>
    </div>
  );
};
