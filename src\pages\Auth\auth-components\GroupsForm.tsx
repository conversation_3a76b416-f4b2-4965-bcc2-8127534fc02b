// src/components/auth-components/GroupsForm.tsx
"use client";

import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";
import { createGroup } from "@/redux/features/setup/setupSlice";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface GroupForm {
  id: number;
  name: string;
  description: string;
}

interface GroupsFormProps {
  onNext: () => void;
  onSkip: () => void;
}

const GroupsForm: React.FC<GroupsFormProps> = ({ onNext, onSkip }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { organization, groupLoading, groupError, groups } = useSelector(
    (state: RootState) => state.setup
  );
  const { toast } = useToast();

  const [groupForms, setGroupForms] = useState<GroupForm[]>([
    { id: Date.now(), name: "", description: "" },
  ]);

  const [alertOpen, setAlertOpen] = useState(false);
  const [alertTitle, setAlertTitle] = useState("");
  const [alertDescription, setAlertDescription] = useState("");

  useEffect(() => {
    if (groupError) {
      setAlertTitle("Error");
      setAlertDescription(groupError);
      setAlertOpen(true);
    }
  }, [groupError]);

  const handleAddRow = () => {
    setGroupForms((prev) => [...prev, { id: Date.now(), name: "", description: "" }]);
  };

  const handleRemoveRow = (id: number) => {
    setGroupForms((prev) => prev.filter((row) => row.id !== id));
  };

  const handleRowChange = (id: number, field: "name" | "description", value: string) => {
    setGroupForms((prev) =>
      prev.map((row) => (row.id === id ? { ...row, [field]: value } : row))
    );
  };

  const handleSaveAll = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!organization) {
      toast({
        title: "Organization not found",
        description: "Please create an organization first",
        variant: "destructive",
      });
      return;
    }
    let savedCount = 0;
    for (const grp of groupForms) {
      if (grp.name.trim() !== "") {
        const result = await dispatch(
          createGroup({
            name: grp.name,
            description: grp.description,
            organisation: organization.id,
          })
        );
        if (result.meta.requestStatus === "fulfilled") savedCount++;
      }
    }
    setAlertTitle("Groups Saved");
    setAlertDescription(`Total groups added: ${savedCount}`);
    setAlertOpen(true);
    setGroupForms([{ id: Date.now(), name: "", description: "" }]);
  };

  const handleAlertClose = () => {
    setAlertOpen(false);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Add Groups (Optional)</h2>
      <form onSubmit={handleSaveAll} className="space-y-4">
        {groupForms.map((grp) => (
          <div key={grp.id} className="flex flex-col gap-2 border p-2 rounded">
            <div>
              <Label>Group Name</Label>
              <Input
                value={grp.name}
                onChange={(e) => handleRowChange(grp.id, "name", e.target.value)}
                placeholder="e.g. Managers"
              />
            </div>
            <div>
              <Label>Description</Label>
              <Input
                value={grp.description}
                onChange={(e) => handleRowChange(grp.id, "description", e.target.value)}
                placeholder="Short description"
              />
            </div>
            {groupForms.length > 1 && (
              <Button variant="secondary" onClick={() => handleRemoveRow(grp.id)}>
                Remove
              </Button>
            )}
          </div>
        ))}
        <div className="flex gap-2">
          <Button type="button" onClick={handleAddRow}>
            + Add Another
          </Button>
          <Button type="submit" disabled={groupLoading}>
            {groupLoading ? "Saving..." : "Save All Groups"}
          </Button>
        </div>
      </form>
      <div className="mt-4">
        <h3 className="font-medium">Existing Groups</h3>
        <ul className="list-disc pl-6">
          {groups.map((grp) => (
            <li key={grp.id}>{grp.name}</li>
          ))}
        </ul>
      </div>
      <div className="flex gap-2">
        <Button variant="secondary" onClick={onSkip}>
          Skip Groups
        </Button>
        <Button onClick={onNext}>Next: Employment Types</Button>
      </div>
      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{alertTitle}</AlertDialogTitle>
            <AlertDialogDescription>{alertDescription}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleAlertClose}>OK</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default GroupsForm;
