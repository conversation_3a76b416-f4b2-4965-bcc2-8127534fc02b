import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { BASE_URL } from '@/config';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Search,
  Filter,
  Download,
  Building2,
  Users,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  X,
  FileText
} from 'lucide-react';
import * as XLSX from 'xlsx';

// Types
interface DepartmentPerformanceData {
  department_id: number;
  department_name: string;
  total_employees: number;
  completed_appraisals: number;
  pending_appraisals: number;
  average_self_rating: number;
  average_supervisor_rating: number;
  completion_rate: number;
  top_performers: number;
  needs_improvement: number;
  department_head?: string;
  department_assistant?: string;
}

interface Department {
  id: number;
  name: string;
  title?: string;
  dep_head?: string;
  dep_head_assistant?: string;
}

interface DepartmentFilters {
  department?: string;
  search?: string;
  date_from?: string;
  date_to?: string;
  min_completion_rate?: number;
  performance_threshold?: string;
}

interface DepartmentPerformanceTabProps {
  onDataChange?: (data: DepartmentPerformanceData[]) => void;
}

const DepartmentPerformanceTab: React.FC<DepartmentPerformanceTabProps> = ({ onDataChange }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  // State
  const [data, setData] = useState<DepartmentPerformanceData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [filters, setFilters] = useState<DepartmentFilters>({});
  const [isExporting, setIsExporting] = useState(false);
  
  // Debounced filters for API calls
  const [debouncedFilters, setDebouncedFilters] = useState<DepartmentFilters>({});

  // Simple debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 500);

    return () => clearTimeout(timer);
  }, [filters]);

  // Fetch departments
  useEffect(() => {
    const fetchDepartments = async () => {
      if (!token) return;

      try {
        const deptRes = await fetch(`${BASE_URL}/users/departments`, {
          headers: { Authorization: `Token ${token}` },
        });

        if (deptRes.ok) {
          const deptData = await deptRes.json();
          setDepartments(deptData);
        }
      } catch (err) {
        console.error('Error fetching departments:', err);
      }
    };

    fetchDepartments();
  }, [token]);

  // Fetch department performance data
  const fetchDepartmentPerformance = useCallback(async (filterParams: DepartmentFilters) => {
    if (!token) return;

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filterParams.department && filterParams.department !== 'all') {
        params.append('department', filterParams.department);
      }
      if (filterParams.date_from) params.append('date_from', filterParams.date_from);
      if (filterParams.date_to) params.append('date_to', filterParams.date_to);

      // Fetch appraisal data
      const appraisalUrl = `${BASE_URL}/appraisal_report/${params.toString() ? `?${params.toString()}` : ''}`;
      const appraisalResponse = await fetch(appraisalUrl, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!appraisalResponse.ok) {
        throw new Error(`HTTP error! status: ${appraisalResponse.status}`);
      }

      const appraisalResult = await appraisalResponse.json();
      
      // Fetch employee job info to get department mappings
      const jobInfoResponse = await fetch(`${BASE_URL}/users/employee-job-info-details`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!jobInfoResponse.ok) {
        throw new Error('Failed to fetch employee job info');
      }

      const jobInfoData = await jobInfoResponse.json();
      
      // Process and aggregate data by department
      const departmentStats = aggregateByDepartment(appraisalResult.results || [], jobInfoData, departments);
      
      // Apply client-side filters
      const filteredData = applyDepartmentFilters(departmentStats, filterParams);
      
      setData(filteredData);
      
      // Notify parent component of data change
      if (onDataChange) {
        onDataChange(filteredData);
      }
      
    } catch (err: any) {
      console.error('Error fetching department performance data:', err);
      setError(err.message || 'Failed to fetch department performance data');
      toast({
        title: "Error",
        description: "Failed to fetch department performance data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [token, toast, onDataChange, departments]);

  // Aggregate appraisal data by department
  const aggregateByDepartment = (appraisalData: any[], jobInfoData: any[], departments: Department[]): DepartmentPerformanceData[] => {
    // Create department map
    const deptMap = new Map(departments.map(d => [d.id, d]));
    
    // Create employee-department mapping
    const employeeDeptMap = new Map();
    jobInfoData.forEach(job => {
      employeeDeptMap.set(job.employee_no, job.department);
    });

    // Group appraisal data by department
    const departmentGroups = new Map<number, any[]>();
    
    appraisalData.forEach(record => {
      const deptId = employeeDeptMap.get(record.employeeid_id);
      if (deptId) {
        if (!departmentGroups.has(deptId)) {
          departmentGroups.set(deptId, []);
        }
        departmentGroups.get(deptId)!.push(record);
      }
    });

    // Calculate statistics for each department
    const departmentStats: DepartmentPerformanceData[] = [];
    
    departmentGroups.forEach((records, deptId) => {
      const dept = deptMap.get(deptId);
      if (!dept) return;

      // Get unique employees in this department
      const uniqueEmployees = new Set(records.map(r => r.employeeid_id));
      const totalEmployees = uniqueEmployees.size;

      // Calculate completion statistics
      const completedEmployees = new Set();
      const pendingEmployees = new Set();
      
      records.forEach(record => {
        if (record.status === 'Completed') {
          completedEmployees.add(record.employeeid_id);
        } else {
          pendingEmployees.add(record.employeeid_id);
        }
      });

      // Calculate average ratings
      const selfRatings = records
        .filter(r => r.total_emp_self_rating_score !== null)
        .map(r => r.total_emp_self_rating_score);
      const supervisorRatings = records
        .filter(r => r.total_supervisor_rating_score !== null)
        .map(r => r.total_supervisor_rating_score);

      const avgSelfRating = selfRatings.length > 0 
        ? selfRatings.reduce((a, b) => a + b, 0) / selfRatings.length 
        : 0;
      const avgSupervisorRating = supervisorRatings.length > 0 
        ? supervisorRatings.reduce((a, b) => a + b, 0) / supervisorRatings.length 
        : 0;

      // Calculate performance categories
      const topPerformers = supervisorRatings.filter(rating => rating >= 80).length;
      const needsImprovement = supervisorRatings.filter(rating => rating < 60).length;

      departmentStats.push({
        department_id: deptId,
        department_name: dept.name,
        total_employees: totalEmployees,
        completed_appraisals: completedEmployees.size,
        pending_appraisals: pendingEmployees.size,
        average_self_rating: Math.round(avgSelfRating * 100) / 100,
        average_supervisor_rating: Math.round(avgSupervisorRating * 100) / 100,
        completion_rate: Math.round((completedEmployees.size / totalEmployees) * 100),
        top_performers: topPerformers,
        needs_improvement: needsImprovement,
        department_head: dept.dep_head,
        department_assistant: dept.dep_head_assistant,
      });
    });

    return departmentStats.sort((a, b) => b.completion_rate - a.completion_rate);
  };

  // Apply client-side filters
  const applyDepartmentFilters = (data: DepartmentPerformanceData[], filterParams: DepartmentFilters): DepartmentPerformanceData[] => {
    return data.filter(record => {
      // Search filter
      if (filterParams.search) {
        const searchTerm = filterParams.search.toLowerCase();
        const searchableText = [
          record.department_name,
          record.department_head,
          record.department_assistant,
        ].join(' ').toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      // Completion rate filter
      if (filterParams.min_completion_rate && record.completion_rate < filterParams.min_completion_rate) {
        return false;
      }

      // Performance threshold filter
      if (filterParams.performance_threshold) {
        const threshold = parseFloat(filterParams.performance_threshold);
        if (record.average_supervisor_rating < threshold) {
          return false;
        }
      }

      return true;
    });
  };

  // Fetch data when debounced filters change
  useEffect(() => {
    if (departments.length > 0) {
      fetchDepartmentPerformance(debouncedFilters);
    }
  }, [debouncedFilters, fetchDepartmentPerformance, departments]);

  // Filter handlers
  const handleFilterChange = (key: keyof DepartmentFilters, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value || undefined
    }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const refreshData = () => {
    fetchDepartmentPerformance(debouncedFilters);
  };

  return {
    data,
    loading,
    error,
    departments,
    filters,
    setFilters,
    isExporting,
    setIsExporting,
    debouncedFilters,
    fetchDepartmentPerformance,
    handleFilterChange,
    clearFilters,
    refreshData,
    aggregateByDepartment,
    applyDepartmentFilters
  };
};

export default DepartmentPerformanceTab;
