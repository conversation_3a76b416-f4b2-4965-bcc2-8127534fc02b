"use client"

import { useEffect, useState, use<PERSON><PERSON>back, useMemo, memo } from "react"
import axios from "axios"
import dayjs from "dayjs"
import isBetween from "dayjs/plugin/isBetween"
import isSameOrAfter from "dayjs/plugin/isSameOrAfter"
import isSameOrBefore from "dayjs/plugin/isSameOrBefore"
dayjs.extend(isBetween)
dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

import { useSelector } from "react-redux"
import { RootState } from "../../../redux/store"
import { BASE_URL } from "../../../config"

// shadcn/ui components
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"

import { Screen } from "@/app-components/layout/screen"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Calendar } from "@/components/ui/calendar"
import { motion, AnimatePresence } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Skeleton } from "@/components/ui/skeleton"
import { Loader } from "@/components/ui/loader"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { useToast } from "@/hooks/use-toast"
import { useDebounce } from "@/hooks/use-debounce"

// Export utilities
import { 
  exportStaffOnLeaveToPDF, 
  exportStaffOnLeaveToExcel, 
  transformLeaveDataForExport 
} from "@/utils/exportUtils"

// Recharts
import {
  BarChart,
  Bar,
  CartesianGrid,
  XAxis,
  Tooltip,
  PieChart,
  Pie,
  Label,
  TooltipProps,
  ResponsiveContainer,
} from "recharts"

// Lucide icons
import {
  Trash,
  Edit,
  Plus,
  TrendingUp,
  TrendingDown,
  Search,
  Calendar as CalendarIcon,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Eye,
  Filter,
  Download,
  X,
  FileText,
  FileSpreadsheet,
} from "lucide-react"

// ------------------------------------------------------------------
// Types
// ------------------------------------------------------------------
export interface LeaveType {
  id: number
  leave_type_name: string
  days_allocated: number
  escalation_days: number
  paid: boolean
  carryover_limit: number
  requires_approval: boolean
}

export interface LeaveApplication {
  id: number
  no_of_days_applied: number
  start_date: string
  end_date: string
  return_date: string
  acknowledger: string | null
  acknowledged_by: string | null
  note_to_acknowledger: string | null
  reliver_acknowledged: boolean
  supervisor_acknowledged: boolean
  hr_acknowledged: boolean
  leave_status: string
  comments: string | null
  document: string | null
  document_name: string | null
  document_url: string | null
  employee_no: string
  leave_type_id: number
  leave_type_name: string | null
  first_acknowledger: string | null
  second_acknowledger: string | null
}

interface BarChartData {
  dayLabel: string
  staffOnLeave: number
}

interface DonutChartData {
  category: string
  value: number
  fill: string
}

// For employee bio info
interface EmployeeBioItem {
  employee_no: string
  first_name: string
  middle_name: string
  last_name: string
}

// For employee contact info
interface EmployeeContactItem {
  employee_no: string
  company_email: string
  personal_email: string
}

// For all-employees (active/inactive status)
interface EmployeeStatusItem {
  employee_no: string
  is_active: boolean
}

// Combined employee data
interface EmployeeItem {
  employee_no: string
  first_name: string
  middle_name: string
  last_name: string
  email: string
  is_active: boolean
}

// For leave balances
interface LeaveBalance {
  id: number
  LeaveBalance: number
  employee_no: string
  LeaveType_id: number
}

// ------------------------------------------------------------------
// MEMOIZED COMPONENTS
// ------------------------------------------------------------------
interface LeaveTypeTableProps {
  leaveTypes: LeaveType[]
  loading: boolean
  onEdit: (lt: LeaveType) => void
  onDelete: (lt: LeaveType) => void
}

const LeaveTypeTable = memo(function LeaveTypeTable({
  leaveTypes,
  loading,
  onEdit,
  onDelete,
}: LeaveTypeTableProps) {
  if (loading) {
    return (
      <Card className="shadow-sm border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
        <CardContent className="p-6">
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-4 w-[80px]" />
                <Skeleton className="h-4 w-[120px]" />
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-8 w-[80px]" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="shadow-sm border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gradient-to-r from-gray-50 to-gray-100/50 dark:from-gray-800 dark:to-gray-700/50 hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-150/50 dark:hover:from-gray-700 dark:hover:to-gray-600/50">
              <TableHead className="font-semibold text-gray-700 dark:text-gray-300">Leave Type Name</TableHead>
              <TableHead className="font-semibold text-gray-700 dark:text-gray-300">Days Allocated</TableHead>
              <TableHead className="font-semibold text-gray-700 dark:text-gray-300">Carryover Limit</TableHead>
              <TableHead className="font-semibold text-gray-700 dark:text-gray-300">Paid</TableHead>
              <TableHead className="font-semibold text-gray-700 dark:text-gray-300">Requires Approval</TableHead>
              <TableHead className="font-semibold text-gray-700 dark:text-gray-300">Escalation (Days)</TableHead>
              <TableHead className="font-semibold text-gray-700 dark:text-gray-300 text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {leaveTypes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex flex-col items-center space-y-2">
                    <AlertCircle className="h-8 w-8 text-gray-400 dark:text-gray-500" />
                    <p className="text-gray-500 dark:text-gray-400">No leave types found.</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              leaveTypes.map((lt) => (
                <TableRow
                  key={lt.id}
                  className="hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-200"
                >
                  <TableCell className="font-medium text-gray-900 dark:text-gray-100">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500"></div>
                      <span>{lt.leave_type_name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-900/50">
                      {lt.days_allocated} days
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="border-gray-300 dark:border-gray-600">
                      {lt.carryover_limit} days
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={lt.paid ? "default" : "secondary"}
                      className={lt.paid
                        ? "bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-900/50"
                        : "bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                      }
                    >
                      {lt.paid ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Paid
                        </>
                      ) : (
                        <>
                          <XCircle className="w-3 h-3 mr-1" />
                          Unpaid
                        </>
                      )}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={lt.requires_approval ? "default" : "secondary"}
                      className={lt.requires_approval
                        ? "bg-orange-100 text-orange-800 hover:bg-orange-200 dark:bg-orange-900/30 dark:text-orange-300 dark:hover:bg-orange-900/50"
                        : "bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                      }
                    >
                      {lt.requires_approval ? (
                        <>
                          <AlertCircle className="w-3 h-3 mr-1" />
                          Required
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Not Required
                        </>
                      )}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="border-purple-300 text-purple-700 dark:border-purple-600 dark:text-purple-400">
                      {lt.escalation_days} days
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(lt)}
                        className="h-8 w-8 p-0 hover:bg-green-100 hover:text-green-700 dark:hover:bg-green-900/30 dark:hover:text-green-400 transition-colors"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(lt)}
                        className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-900/30 dark:hover:text-red-400 transition-colors"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </Card>
  )
})

interface LeaveTypeDialogProps {
  open: boolean
  mode: "create" | "edit"
  initialData?: Partial<LeaveType>
  onClose: () => void
  onSave: (payload: Partial<LeaveType>) => Promise<void>
}

const LeaveTypeDialog = memo(function LeaveTypeDialog({
  open,
  mode,
  initialData,
  onClose,
  onSave,
}: LeaveTypeDialogProps) {
  const [typeName, setTypeName] = useState("")
  const [daysAllocated, setDaysAllocated] = useState("")
  const [carryoverLimit, setCarryoverLimit] = useState("")
  const [escalationDays, setEscalationDays] = useState("2")
  const [paid, setPaid] = useState(true)
  const [requiresApproval, setRequiresApproval] = useState(true)

  useEffect(() => {
    if (open) {
      setTypeName(initialData?.leave_type_name || "")
      setDaysAllocated(initialData?.days_allocated?.toString() || "")
      setCarryoverLimit(initialData?.carryover_limit?.toString() || "")
      setEscalationDays(initialData?.escalation_days?.toString() || "2")
      setPaid(initialData?.paid ?? true)
      setRequiresApproval(initialData?.requires_approval ?? true)
    }
  }, [open, initialData])

  const handleSave = async () => {
    const days = parseInt(daysAllocated, 10)
    const escalation = parseInt(escalationDays, 10)
    const carryover = carryoverLimit ? parseInt(carryoverLimit, 10) : 0
    if (
      !typeName.trim() ||
      isNaN(days) ||
      isNaN(escalation) ||
      (carryoverLimit && isNaN(carryover))
    ) {
      return
    }
    const payload: Partial<LeaveType> = {
      leave_type_name: typeName.trim(),
      days_allocated: days,
      escalation_days: escalation,
      paid,
      carryover_limit: carryover,
      requires_approval: requiresApproval,
    }
    await onSave(payload)
  }

  return (
    <Dialog open={open} onOpenChange={() => {}}>
      <DialogContent className="max-w-lg" onPointerDownOutside={(e) => e.preventDefault()} onEscapeKeyDown={(e) => e.preventDefault()}>
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>
              {mode === "create" ? "Create Leave Type" : "Edit Leave Type"}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 my-4">
          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-700">Leave Type Name</label>
            <Input
              value={typeName}
              onChange={(e) => setTypeName(e.target.value)}
              className="mt-1"
              placeholder="Enter leave type name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Days Allocated</label>
            <Input
              type="number"
              value={daysAllocated}
              onChange={(e) => setDaysAllocated(e.target.value)}
              className="mt-1"
              placeholder="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Carryover Limit</label>
            <Input
              type="number"
              value={carryoverLimit}
              onChange={(e) => setCarryoverLimit(e.target.value)}
              className="mt-1"
              placeholder="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Escalation Days</label>
            <Input
              type="number"
              value={escalationDays}
              onChange={(e) => setEscalationDays(e.target.value)}
              className="mt-1"
              placeholder="2"
            />
          </div>
          <div className="flex items-center">
            <Checkbox
              id="paid"
              checked={paid}
              onCheckedChange={(chk) => setPaid(chk as boolean)}
            />
            <label htmlFor="paid" className="ml-2 text-sm text-gray-700">
              Paid leave?
            </label>
          </div>
          <div className="flex items-center">
            <Checkbox
              id="requiresApproval"
              checked={requiresApproval}
              onCheckedChange={(chk) => setRequiresApproval(chk as boolean)}
            />
            <label htmlFor="requiresApproval" className="ml-2 text-sm text-gray-700">
              Requires Approval?
            </label>
          </div>
        </div>
        <DialogFooter>
          <div className="flex justify-end space-x-2">
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button variant="secondary" onClick={handleSave}>
              {mode === "create" ? "Create" : "Update"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
})

interface DeleteConfirmationDialogProps {
  open: boolean
  leaveTypeName?: string
  onCancel: () => void
  onConfirm: () => Promise<void>
}

const DeleteConfirmationDialog = memo(function DeleteConfirmationDialog({
  open,
  leaveTypeName,
  onCancel,
  onConfirm,
}: DeleteConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={() => {}}>
      <DialogContent className="max-w-xs" onPointerDownOutside={(e) => e.preventDefault()} onEscapeKeyDown={(e) => e.preventDefault()}>
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>Delete Leave Type</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        <div className="my-4 text-sm text-gray-700">
          Are you sure you want to delete "{leaveTypeName}"?
        </div>
        <DialogFooter>
          <div className="flex justify-end space-x-2">
            <Button variant="secondary" onClick={onCancel}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={onConfirm}>
              Delete
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
})

// Memoized chart components
const CustomBarTooltip = memo(function CustomBarTooltip(props: TooltipProps<any, any>) {
  if (props.active && props.payload && props.payload.length) {
    const day = props.label
    const count = props.payload[0].value
    return (
      <div className="p-2 bg-white border shadow text-sm text-gray-700">
        <p className="font-medium">
          Day {day} : {count} staff
        </p>
      </div>
    )
  }
  return null
})

// ------------------------------------------------------------------
// MAIN DASHBOARD PAGE
// ------------------------------------------------------------------
export default function LeaveManagementPage() {
  const { user, token } = useSelector((state: RootState) => state.auth)
  const { toast } = useToast()
  
  // Memoized user data
  const { userName, orgName } = useMemo(() => ({
    userName: `${user?.UserBio?.first_name ?? ""} ${user?.UserBio?.middle_name ?? ""} ${
      user?.UserBio?.last_name ?? ""
    }`.trim(),
    orgName: user?.userOrgData?.organisation_name || "Your Organization"
  }), [user])

  // Leave Types
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([])
  const [loadingTypes, setLoadingTypes] = useState(false)

  // Leave Applications
  const [leaveApps, setLeaveApps] = useState<LeaveApplication[]>([])
  const [loadingApps, setLoadingApps] = useState(false)

  // Leave Type CRUD dialogs
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogMode, setDialogMode] = useState<"create" | "edit">("create")
  const [selectedLeaveType, setSelectedLeaveType] = useState<LeaveType | undefined>()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [leaveTypeToDelete, setLeaveTypeToDelete] = useState<LeaveType | undefined>()

  // Calendar & Chart filter
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [viewMode, setViewMode] = useState<"day" | "week" | "month">("day")

  // Employee Balance Management
  const [allLeaveTypes, setAllLeaveTypes] = useState<LeaveType[]>([])
  const [employeeLimitData, setEmployeeLimitData] = useState<any[]>([])
  const [loadingEB, setLoadingEB] = useState(false)
  const [errorEB, setErrorEB] = useState("")

  // Employee pagination and search
  const [empPage, setEmpPage] = useState(1)
  const [empTotalPages, setEmpTotalPages] = useState(1)
  const [empSearch, setEmpSearch] = useState("")
  const [allEmployeesData, setAllEmployeesData] = useState<any[]>([])
  const empPageSize = 10

  // Use debounced search
  const debouncedEmpSearch = useDebounce(empSearch, 500)

  // Download loading states
  const [downloadingPDF, setDownloadingPDF] = useState(false)
  const [downloadingExcel, setDownloadingExcel] = useState(false)

  // Multi-lang greeting - optimized with useMemo
  const [languageIndex, setLanguageIndex] = useState(0)
  const languages = useMemo(() => ["en", "sw", "fr"], [])

  // Memoized greeting calculation
  const { currentLang, greeting } = useMemo(() => {
    const currentHour = new Date().getHours()
    const greetings: { en: string; sw: string; fr: string } = {
      en:
        currentHour < 12
          ? "Good morning"
          : currentHour < 18
          ? "Good afternoon"
          : "Good evening",
      sw:
        currentHour < 12
          ? "Habari ya asubuhi"
          : currentHour < 18
          ? "Habari ya mchana"
          : "Habari ya jioni",
      fr:
        currentHour < 12
          ? "Bonjour"
          : currentHour < 18
          ? "Bon après-midi"
          : "Bonsoir",
    }
    const currentLang = languages[languageIndex] as "en" | "sw" | "fr"
    return { currentLang, greeting: greetings[currentLang] }
  }, [languageIndex, languages])

  // Helper: Count how many staff are on leave for a given date
  const getStaffCountOnDate = useCallback((date: dayjs.Dayjs, apps: LeaveApplication[]) => {
    return apps.filter((app) =>
      date.isBetween(dayjs(app.start_date), dayjs(app.end_date), "day", "[]")
    ).length
  }, [])

  // Helper: Get staff on leave for a specific date
  const getStaffOnDate = useCallback((date: dayjs.Dayjs, apps: LeaveApplication[]) => {
    return apps.filter((app) =>
      date.isBetween(dayjs(app.start_date), dayjs(app.end_date), "day", "[]")
    )
  }, [])

  // Language rotation effect
  useEffect(() => {
    const intervalId = setInterval(() => {
      setLanguageIndex((prev) => (prev + 1) % languages.length)
    }, 5000)
    return () => clearInterval(intervalId)
  }, [languages.length])

  // Optimized API calls with useCallback
  const fetchLeaveTypes = useCallback(async () => {
    if (!token) return
    setLoadingTypes(true)
    try {
      const resp = await axios.get<LeaveType[]>(`${BASE_URL}/hrm/leave-type-details`, {
        headers: { Authorization: `Token ${token}` },
      })
      setLeaveTypes(resp.data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch leave types. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoadingTypes(false)
    }
  }, [token, toast])

  const fetchLeaveApps = useCallback(async () => {
    if (!token) return
    setLoadingApps(true)
    try {
      const resp = await axios.get<LeaveApplication[]>(
        `${BASE_URL}/hrm/leave-application-details`,
        {
          headers: { Authorization: `Token ${token}` },
        }
      )
      // Sort descending by start_date (newest first)
      const sorted = resp.data.sort(
        (a, b) => dayjs(b.start_date).valueOf() - dayjs(a.start_date).valueOf()
      )
      setLeaveApps(sorted)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch leave applications. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoadingApps(false)
    }
  }, [token, toast])

  // Initial data fetch
  useEffect(() => {
    if (token) {
      fetchLeaveTypes()
      fetchLeaveApps()
    }
  }, [token, fetchLeaveTypes, fetchLeaveApps])

  // Memoized filtered data
  const { filteredApps, staffOnLeaveToday, currentCount, barChartData, donutChartData } = useMemo(() => {
    const filteredApps = leaveApps.filter((app) => {
      const d = dayjs(selectedDate)
      return d.isBetween(dayjs(app.start_date), dayjs(app.end_date), "day", "[]")
    })

    // Staff on Leave Today
    const staffOnLeaveToday = leaveApps.filter((app) => {
      const today = dayjs()
      return today.isBetween(dayjs(app.start_date), dayjs(app.end_date), "day", "[]")
    })

    // Current count for selected date
    const selectedDay = dayjs(selectedDate)
    const currentCount = getStaffCountOnDate(selectedDay, leaveApps)

    // Bar chart data
    function countStaff(dayObj: dayjs.Dayjs) {
      return getStaffCountOnDate(dayObj, leaveApps)
    }

    const base = dayjs(selectedDate)
    let bars: BarChartData[] = []

    if (viewMode === "day") {
      bars = [{ dayLabel: base.format("MM-DD"), staffOnLeave: countStaff(base) }]
    } else if (viewMode === "week") {
      const dow = base.day()
      const monday = base.subtract(dow === 0 ? 6 : dow - 1, "day")
      const arr = Array.from({ length: 7 }, (_, i) => monday.add(i, "day"))
      bars = arr.map((dt) => ({
        dayLabel: dt.format("MM-DD"),
        staffOnLeave: countStaff(dt),
      }))
    } else {
      const startM = base.startOf("month")
      const endM = base.endOf("month")
      let cur = startM
      const arr: dayjs.Dayjs[] = []
      while (cur.isSameOrBefore(endM)) {
        arr.push(cur)
        cur = cur.add(1, "day")
      }
      bars = arr.map((dt) => ({
        dayLabel: dt.format("DD"),
        staffOnLeave: countStaff(dt),
      }))
    }

    // Donut chart data
    const countMap: Record<string, number> = {}
    filteredApps.forEach((app) => {
      const cat = app.leave_type_name || "Unknown"
      countMap[cat] = (countMap[cat] || 0) + 1
    })
    const colorMap: Record<string, string> = {
      Paid: "hsl(var(--chart-1))",
      Educational: "hsl(var(--chart-2))",
      Medical: "hsl(var(--chart-3))",
      Unpaid: "hsl(var(--chart-4))",
      Maternity: "hsl(var(--chart-5))",
      Unknown: "#ccc",
    }
    const donut = Object.entries(countMap).map(([cat, val], i) => ({
      category: cat,
      value: val,
      fill: colorMap[cat] || `hsl(${i * 30}, 70%, 50%)`,
    }))

    return {
      filteredApps,
      staffOnLeaveToday,
      currentCount,
      barChartData: bars,
      donutChartData: donut
    }
  }, [selectedDate, viewMode, leaveApps, getStaffCountOnDate])

  // Week-over-week trend calculation
  const { percentChange, isUp, trendColor, trendIcon } = useMemo(() => {
    const selectedDay = dayjs(selectedDate)
    const lastWeekDate = selectedDay.subtract(7, "day")
    const lastWeekCount = getStaffCountOnDate(lastWeekDate, leaveApps)

    const difference = currentCount - lastWeekCount
    const percentChange = lastWeekCount > 0 ? (difference / lastWeekCount) * 100 : 0
    const isUp = difference >= 0
    const trendColor = isUp ? "text-green-600" : "text-red-600"
    const trendIcon = isUp ? <TrendingUp className="w-4 h-4 mr-1" /> : <TrendingDown className="w-4 h-4 mr-1" />

    return { percentChange, isUp, trendColor, trendIcon }
  }, [selectedDate, currentCount, leaveApps, getStaffCountOnDate])

  // Employee mapping for names - optimized
  const [employeeMap, setEmployeeMap] = useState<Record<string, string>>({})
  
  const fetchEmployeesForMap = useCallback(async () => {
    if (!token) return
    try {
      const bioRes = await axios.get<EmployeeBioItem[]>(
        `${BASE_URL}/users/employee-bio-details`,
        {
          headers: { Authorization: `Token ${token}` },
        }
      )
      const map: Record<string, string> = {}
      bioRes.data.forEach((emp) => {
        map[emp.employee_no] = `${emp.first_name} ${emp.middle_name || ""} ${emp.last_name}`.trim()
      })
      setEmployeeMap(map)
    } catch (err) {
      console.error("Error fetching employees for map:", err)
    }
  }, [token])

  useEffect(() => {
    if (token) {
      fetchEmployeesForMap()
    }
  }, [token, fetchEmployeesForMap])

  // Download handlers
  const handleDownloadPDF = useCallback(async () => {
    if (downloadingPDF) return
    
    setDownloadingPDF(true)
    try {
      const today = dayjs()
      const todayStaff = leaveApps.filter((app) => {
        return today.isBetween(dayjs(app.start_date), dayjs(app.end_date), "day", "[]")
      })
      
      if (todayStaff.length === 0) {
        toast({
          title: "No Data",
          description: "No staff are on leave today.",
          variant: "destructive",
        })
        return
      }
      
      const exportData = transformLeaveDataForExport(todayStaff, employeeMap)
      exportStaffOnLeaveToPDF(exportData, today.format('YYYY-MM-DD'), orgName)
      
      toast({
        title: "PDF Downloaded",
        description: `Staff on leave report for ${today.format('MMMM DD, YYYY')} has been downloaded as PDF.`,
      })
    } catch (error) {
      console.error('PDF download error:', error)
      toast({
        title: "Download Failed",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      })
    } finally {
      setDownloadingPDF(false)
    }
  }, [leaveApps, employeeMap, orgName, toast, downloadingPDF])

  const handleDownloadExcel = useCallback(async () => {
    if (downloadingExcel) return
    
    setDownloadingExcel(true)
    try {
      const today = dayjs()
      const todayStaff = leaveApps.filter((app) => {
        return today.isBetween(dayjs(app.start_date), dayjs(app.end_date), "day", "[]")
      })
      
      if (todayStaff.length === 0) {
        toast({
          title: "No Data",
          description: "No staff are on leave today.",
          variant: "destructive",
        })
        return
      }
      
      const exportData = transformLeaveDataForExport(todayStaff, employeeMap)
      exportStaffOnLeaveToExcel(exportData, today.format('YYYY-MM-DD'), orgName)
      
      toast({
        title: "Excel Downloaded",
        description: `Staff on leave report for ${today.format('MMMM DD, YYYY')} has been downloaded as Excel file.`,
      })
    } catch (error) {
      console.error('Excel download error:', error)
      toast({
        title: "Download Failed",
        description: "Failed to generate Excel file. Please try again.",
        variant: "destructive",
      })
    } finally {
      setDownloadingExcel(false)
    }
  }, [leaveApps, employeeMap, orgName, toast, downloadingExcel])

  // LEAVE TYPE CRUD handlers
  const openCreateDialog = useCallback(() => {
    setDialogMode("create")
    setSelectedLeaveType(undefined)
    setDialogOpen(true)
  }, [])

  const openEditDialog = useCallback((lt: LeaveType) => {
    setDialogMode("edit")
    setSelectedLeaveType(lt)
    setDialogOpen(true)
  }, [])

  const handleSave = useCallback(async (payload: Partial<LeaveType>) => {
    try {
      if (dialogMode === "create") {
        await axios.post(`${BASE_URL}/hrm/leave-type-details`, payload, {
          headers: { Authorization: `Token ${token}` },
        })
        toast({
          title: "Success",
          description: "Leave type created successfully!",
        })
      } else if (selectedLeaveType) {
        await axios.patch(
          `${BASE_URL}/hrm/leave-type-details/${selectedLeaveType.id}`,
          payload,
          {
            headers: { Authorization: `Token ${token}` },
          }
        )
        toast({
          title: "Success",
          description: "Leave type updated successfully!",
        })
      }
      setDialogOpen(false)
      fetchLeaveTypes()
    } catch (error) {
      toast({
        title: "Error",
        description: "Save operation failed. Please try again.",
        variant: "destructive",
      })
    }
  }, [dialogMode, selectedLeaveType, token, toast, fetchLeaveTypes])

  const openDeleteDialog = useCallback((lt: LeaveType) => {
    setLeaveTypeToDelete(lt)
    setDeleteDialogOpen(true)
  }, [])

  const handleDelete = useCallback(async () => {
    if (!token || !leaveTypeToDelete) return
    try {
      await axios.delete(
        `${BASE_URL}/hrm/leave-type-details/${leaveTypeToDelete.id}`,
        {
          headers: { Authorization: `Token ${token}` },
        }
      )
      toast({
        title: "Success",
        description: "Leave type deleted successfully!",
      })
      setDeleteDialogOpen(false)
      fetchLeaveTypes()
    } catch (error) {
      toast({
        title: "Error",
        description: "Delete operation failed. Please try again.",
        variant: "destructive",
      })
    }
  }, [token, leaveTypeToDelete, toast, fetchLeaveTypes])

  // ----------------------------------------------------------------
  // EMPLOYEE BALANCE MANAGEMENT
  // ----------------------------------------------------------------
  
  // Fetch all leave types for columns (optimized)
  const fetchAllTypes = useCallback(async () => {
    if (!token) return
    try {
      const resp = await axios.get<LeaveType[]>(`${BASE_URL}/hrm/leave-type-details`, {
        headers: { Authorization: `Token ${token}` },
      })
      setAllLeaveTypes(resp.data)
    } catch (err) {
      console.error("Failed to fetch all leave types for columns:", err)
    }
  }, [token])

  useEffect(() => {
    if (token) {
      fetchAllTypes()
    }
  }, [token, fetchAllTypes])

  // Helper: Build employee rows from a list of employees and balances (optimized)
  const buildEmployeeRows = useCallback((
    employees: EmployeeItem[],
    balances: LeaveBalance[],
    types: LeaveType[]
  ) => {
    const typeMap: Record<number, string> = {}
    types.forEach((t) => {
      typeMap[t.id] = t.leave_type_name
    })
    
    return employees.map((emp) => {
      const row: any = {
        employee_no: emp.employee_no,
        first_name: emp.first_name,
        middle_name: emp.middle_name,
        last_name: emp.last_name,
        email: emp.email || "N/A",
      }
      let total = 0
      const rel = balances.filter((b) => b.employee_no === emp.employee_no)
      rel.forEach((b) => {
        const tName = typeMap[b.LeaveType_id] || "Unknown"
        row[tName] = (row[tName] || 0) + b.LeaveBalance
        total += b.LeaveBalance
      })
      row["Total"] = total
      return row
    })
  }, [])

  // Fetch all employees and their balances (optimized)
  const fetchAllEmployees = useCallback(async () => {
    if (!token || allLeaveTypes.length === 0) return

    setLoadingEB(true)
    setErrorEB("")
    try {
      // Fetch data from multiple endpoints
      const [bioResp, contactResp, statusResp, balResp] = await Promise.all([
        axios.get<EmployeeBioItem[]>(`${BASE_URL}/users/employee-bio-details`, {
          headers: { Authorization: `Token ${token}` },
        }),
        axios.get<EmployeeContactItem[]>(`${BASE_URL}/users/employee-contact-info-details`, {
          headers: { Authorization: `Token ${token}` },
        }),
        axios.get<EmployeeStatusItem[]>(`${BASE_URL}/users/all-employees`, {
          headers: { Authorization: `Token ${token}` },
        }),
        axios.get<LeaveBalance[]>(`${BASE_URL}/hrm/leave-balance`, {
          headers: { Authorization: `Token ${token}` },
        }),
      ])

      // Combine employee data efficiently
      const bioMap: Record<string, EmployeeBioItem> = {}
      bioResp.data.forEach((emp) => {
        bioMap[emp.employee_no] = emp
      })

      const contactMap: Record<string, EmployeeContactItem> = {}
      contactResp.data.forEach((emp) => {
        contactMap[emp.employee_no] = emp
      })

      const statusMap: Record<string, EmployeeStatusItem> = {}
      statusResp.data.forEach((emp) => {
        statusMap[emp.employee_no] = emp
      })

      // Build combined employee list
      const allEmployees: EmployeeItem[] = Object.keys(bioMap).map((empNo) => {
        const bio = bioMap[empNo]
        const contact = contactMap[empNo]
        const status = statusMap[empNo]
        return {
          employee_no: empNo,
          first_name: bio.first_name,
          middle_name: bio.middle_name,
          last_name: bio.last_name,
          email: contact?.company_email || contact?.personal_email || "N/A",
          is_active: status?.is_active ?? true,
        }
      })

      // Build employee rows with leave balances
      const allRows = buildEmployeeRows(allEmployees, balResp.data, allLeaveTypes)
      setAllEmployeesData(allRows)
      
      // Set first 5 for display
      setEmployeeLimitData(allRows.slice(0, 5))
      
      // Calculate total pages
      setEmpTotalPages(Math.ceil(allRows.length / empPageSize))
    } catch (err) {
      console.error("fetchAllEmployees error:", err)
      setErrorEB("Failed to fetch employee data. Please try again.")
      toast({
        title: "Error",
        description: "Failed to fetch employee leave balances. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoadingEB(false)
    }
  }, [token, allLeaveTypes, buildEmployeeRows, toast, empPageSize])

  useEffect(() => {
    if (token && allLeaveTypes.length > 0) {
      fetchAllEmployees()
    }
  }, [token, allLeaveTypes, fetchAllEmployees])

  // Memoized filtered and paginated employee data
  const { filteredEmployeeData, displayedEmployeeData } = useMemo(() => {
    if (!debouncedEmpSearch.trim()) {
      const startIndex = (empPage - 1) * empPageSize
      const endIndex = startIndex + empPageSize
      return {
        filteredEmployeeData: allEmployeesData,
        displayedEmployeeData: allEmployeesData.slice(startIndex, endIndex)
      }
    }

    const searchTerm = debouncedEmpSearch.toLowerCase()
    const filtered = allEmployeesData.filter((emp) => {
      const fullName = `${emp.first_name} ${emp.middle_name || ""} ${emp.last_name}`.toLowerCase()
      return (
        fullName.includes(searchTerm) ||
        emp.employee_no.toLowerCase().includes(searchTerm) ||
        emp.email.toLowerCase().includes(searchTerm)
      )
    })

    const startIndex = (empPage - 1) * empPageSize
    const endIndex = startIndex + empPageSize
    
    return {
      filteredEmployeeData: filtered,
      displayedEmployeeData: filtered.slice(startIndex, endIndex)
    }
  }, [allEmployeesData, debouncedEmpSearch, empPage, empPageSize])

  // Update total pages when filtered data changes
  useEffect(() => {
    setEmpTotalPages(Math.ceil(filteredEmployeeData.length / empPageSize))
    if (empPage > Math.ceil(filteredEmployeeData.length / empPageSize)) {
      setEmpPage(1)
    }
  }, [filteredEmployeeData.length, empPageSize, empPage])

  // ----------------------------------------------------------------
  // Render
  // ----------------------------------------------------------------
  return (
    <Screen
      headerContent
    >
      <div className="p-4 space-y-6">
        {/* Modern Header Section */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 dark:from-blue-800 dark:via-blue-900 dark:to-indigo-900 p-8 text-white shadow-xl">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-transparent dark:from-blue-800/20"></div>
          <div className="relative z-10">
            <div className="flex flex-col gap-3">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-white/20 dark:bg-white/30 p-3 backdrop-blur-sm">
                  <Users className="h-6 w-6" />
                </div>
                <h1 className="text-2xl md:text-3xl font-bold">
                  {orgName} Leave Management
                </h1>
              </div>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentLang}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.5 }}
                  className="text-blue-100 dark:text-blue-200 text-lg"
                >
                  {greeting} {userName || "User"}
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-medium text-green-600 dark:text-green-400 mb-1">Staff on Leave Today</p>
                  <p className="text-3xl font-bold text-green-900 dark:text-green-100">{staffOnLeaveToday.length}</p>
                  <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                    {dayjs().format("MMMM DD, YYYY")}
                  </p>
                </div>
                <div className="rounded-full bg-green-200 dark:bg-green-800 p-3">
                  <Users className="h-6 w-6 text-green-700 dark:text-green-300" />
                </div>
              </div>
              
              {/* Download Buttons */}
              {staffOnLeaveToday.length > 0 && (
                <div className="flex items-center gap-2 pt-3 border-t border-green-200 dark:border-green-700">
                  <p className="text-xs text-green-600 dark:text-green-400 mr-2">Download:</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownloadPDF}
                    disabled={downloadingPDF}
                    className="h-7 px-2 text-xs border-green-300 text-green-700 hover:bg-green-100 hover:border-green-400 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/30 disabled:opacity-50"
                  >
                    {downloadingPDF ? (
                      <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <FileText className="h-3 w-3 mr-1" />
                    )}
                    PDF
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownloadExcel}
                    disabled={downloadingExcel}
                    className="h-7 px-2 text-xs border-green-300 text-green-700 hover:bg-green-100 hover:border-green-400 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/30 disabled:opacity-50"
                  >
                    {downloadingExcel ? (
                      <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <FileSpreadsheet className="h-3 w-3 mr-1" />
                    )}
                    Excel
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-blue-50 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400 mb-1">Weekly Trend</p>
                  <div className="flex items-center gap-2">
                    <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">
                      {Math.abs(percentChange).toFixed(1)}%
                    </p>
                    <div className={`flex items-center ${trendColor}`}>
                      {trendIcon}
                    </div>
                  </div>
                  <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">vs last week</p>
                </div>
                <div className="rounded-full bg-blue-200 dark:bg-blue-800 p-3">
                  <TrendingUp className="h-6 w-6 text-blue-700 dark:text-blue-300" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-900/20 dark:to-violet-900/20 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600 dark:text-purple-400 mb-1">Leave Types</p>
                  <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">{leaveTypes.length}</p>
                  <p className="text-xs text-purple-700 dark:text-purple-300 mt-1">configured</p>
                </div>
                <div className="rounded-full bg-purple-200 dark:bg-purple-800 p-3">
                  <CalendarIcon className="h-6 w-6 text-purple-700 dark:text-purple-300" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Modern Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2 border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500"></div>
                    Leave Analytics
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Staff leave patterns based on selected timeframe
                  </CardDescription>
                </div>
                <div className="flex items-center gap-1 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <Button
                    variant={viewMode === "day" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("day")}
                    className={viewMode === "day" ? "bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800" : "hover:bg-gray-200 dark:hover:bg-gray-700"}
                  >
                    Day
                  </Button>
                  <Button
                    variant={viewMode === "week" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("week")}
                    className={viewMode === "week" ? "bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800" : "hover:bg-gray-200 dark:hover:bg-gray-700"}
                  >
                    Week
                  </Button>
                  <Button
                    variant={viewMode === "month" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("month")}
                    className={viewMode === "month" ? "bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800" : "hover:bg-gray-200 dark:hover:bg-gray-700"}
                  >
                    Month
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {loadingApps ? (
                <div className="flex items-center justify-center h-[300px]">
                  <div className="flex flex-col items-center space-y-3">
                    <Loader size={32} color="#3b82f6" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">Loading analytics...</p>
                  </div>
                </div>
              ) : barChartData.length === 0 ? (
                <div className="flex items-center justify-center h-[300px]">
                  <div className="flex flex-col items-center space-y-3">
                    <AlertCircle className="h-12 w-12 text-gray-400 dark:text-gray-500" />
                    <p className="text-gray-500 dark:text-gray-400 text-center">
                      No leave data available for the selected timeframe
                    </p>
                  </div>
                </div>
              ) : (
                <div className="w-full h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={barChartData}>
                      <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f1f5f9" />
                      <XAxis
                        dataKey="dayLabel"
                        axisLine={false}
                        tickLine={false}
                        tickMargin={10}
                        tick={{ fill: '#64748b', fontSize: 12 }}
                      />
                      <Tooltip
                        cursor={{ fill: "rgba(59, 130, 246, 0.1)" }}
                        content={<CustomBarTooltip />}
                      />
                      <Bar
                        dataKey="staffOnLeave"
                        fill="url(#barGradient)"
                        radius={[6, 6, 0, 0]}
                      />
                      <defs>
                        <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="#3b82f6" />
                          <stop offset="100%" stopColor="#1d4ed8" />
                        </linearGradient>
                      </defs>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"></div>
                Leave Categories
              </CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400">
                Distribution by leave type
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              {loadingApps ? (
                <div className="flex items-center justify-center h-[220px]">
                  <div className="flex flex-col items-center space-y-3">
                    <Loader size={24} color="#8b5cf6" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">Loading categories...</p>
                  </div>
                </div>
              ) : donutChartData.length === 0 ? (
                <div className="flex items-center justify-center h-[220px]">
                  <div className="flex flex-col items-center space-y-3">
                    <AlertCircle className="h-8 w-8 text-gray-400 dark:text-gray-500" />
                    <p className="text-gray-500 dark:text-gray-400 text-center text-sm">
                      No leave applications for selected date
                    </p>
                  </div>
                </div>
              ) : (
                <div className="flex justify-center">
                  <ResponsiveContainer width={220} height={220}>
                    <PieChart>
                      <Pie
                        data={donutChartData}
                        dataKey="value"
                        nameKey="category"
                        innerRadius={60}
                        outerRadius={80}
                        strokeWidth={2}
                        stroke="#ffffff"
                      >
                        <Label
                          content={({ viewBox }) => {
                            if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                              const total = donutChartData.reduce(
                                (acc, c) => acc + c.value,
                                0
                              )
                              return (
                                <text
                                  x={viewBox.cx}
                                  y={viewBox.cy}
                                  textAnchor="middle"
                                  dominantBaseline="middle"
                                >
                                  <tspan className="fill-foreground text-2xl font-bold">
                                    {total}
                                  </tspan>
                                  <tspan className="fill-muted-foreground text-sm" x={viewBox.cx} dy={20}>
                                    Total
                                  </tspan>
                                </text>
                              )
                            }
                          }}
                        />
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Calendar and Recent Leave Requests */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Calendar</CardTitle>
              <CardDescription>Pick a date to filter results</CardDescription>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(d) => d && setSelectedDate(d)}
                className="rounded-md border"
              />
            </CardContent>
          </Card>

          {/* Staff Leave Requests */}
          <Card className="lg:col-span-2 border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500"></div>
                    Recent Leave Requests
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    Latest staff leave applications
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {loadingApps ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 rounded-lg bg-gray-50">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-[200px]" />
                        <Skeleton className="h-3 w-[150px]" />
                      </div>
                      <Skeleton className="h-6 w-[80px]" />
                    </div>
                  ))}
                </div>
              ) : filteredApps.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="flex flex-col items-center space-y-3">
                    <AlertCircle className="h-12 w-12 text-gray-400" />
                    <p className="text-gray-500 text-center">
                      No leave requests found for the selected date
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredApps.slice(0, 5).map((app) => {
                    const name = employeeMap[app.employee_no] || app.employee_no
                    const initials = name.split(' ').map(n => n[0]).join('').toUpperCase()
                    return (
                      <motion.div
                        key={app.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center space-x-4 p-4 rounded-xl bg-white border border-gray-100 hover:border-blue-200 hover:shadow-md transition-all duration-200"
                      >
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-sm font-semibold">
                            {initials}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-gray-900 truncate">{name}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {app.leave_type_name}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {dayjs(app.start_date).format("MMM DD")} - {dayjs(app.end_date).format("MMM DD")}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={
                              app.leave_status.toLowerCase().includes('approved') ? 'default' :
                              app.leave_status.toLowerCase().includes('pending') ? 'secondary' :
                              'destructive'
                            }
                            className={
                              app.leave_status.toLowerCase().includes('approved')
                                ? 'bg-green-100 text-green-800 hover:bg-green-200' :
                              app.leave_status.toLowerCase().includes('pending')
                                ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' :
                                'bg-red-100 text-red-800 hover:bg-red-200'
                            }
                          >
                            {app.leave_status.toLowerCase().includes('approved') && <CheckCircle className="w-3 h-3 mr-1" />}
                            {app.leave_status.toLowerCase().includes('pending') && <Clock className="w-3 h-3 mr-1" />}
                            {app.leave_status.toLowerCase().includes('rejected') && <XCircle className="w-3 h-3 mr-1" />}
                            {app.leave_status}
                          </Badge>
                        </div>
                      </motion.div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Staff On Leave Today */}
        <Card>
          <CardHeader>
            <CardTitle>Staff on Leave Today</CardTitle>
            <CardDescription>
              These are all employees whose leave covers today's date
            </CardDescription>
          </CardHeader>
          <CardContent>
            {staffOnLeaveToday.length === 0 ? (
              <div className="text-sm text-gray-500">No one is on leave today.</div>
            ) : (
              staffOnLeaveToday.map((app) => {
                const name = employeeMap[app.employee_no] || app.employee_no
                return (
                  <div
                    key={app.id}
                    className="flex flex-col md:flex-row md:justify-between p-2 border-b last:border-b-0"
                  >
                    <div className="font-medium">{name}</div>
                    <div className="text-sm text-gray-500">{app.leave_type_name}</div>
                  </div>
                )
              })
            )}
          </CardContent>
        </Card>

        {/* Employee Leave Balances */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-3">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-emerald-500 to-teal-600"></div>
                Employee Leave Balances
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mt-1">View and manage employee leave balance allocations</p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={fetchAllEmployees}
                disabled={loadingEB}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${loadingEB ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
            <CardHeader className="pb-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">
                    Employee Leave Balance Overview
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Current leave balance status for all employees
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search employees..."
                      value={empSearch}
                      onChange={(e) => setEmpSearch(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {loadingEB ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex flex-col items-center space-y-3">
                    <Loader size={32} color="#10b981" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">Loading employee balances...</p>
                  </div>
                </div>
              ) : errorEB ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex flex-col items-center space-y-3">
                    <AlertCircle className="h-12 w-12 text-red-400" />
                    <p className="text-red-600 text-center">{errorEB}</p>
                    <Button onClick={fetchAllEmployees} variant="outline" size="sm">
                      Try Again
                    </Button>
                  </div>
                </div>
              ) : displayedEmployeeData.length === 0 ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex flex-col items-center space-y-3">
                    <Users className="h-12 w-12 text-gray-400" />
                    <p className="text-gray-500 text-center">
                      {debouncedEmpSearch ? 'No employees found matching your search.' : 'No employee data available.'}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-gradient-to-r from-gray-50 to-gray-100/50 dark:from-gray-800 dark:to-gray-700/50">
                          <TableHead className="font-semibold text-gray-700 dark:text-gray-300">Employee</TableHead>
                          <TableHead className="font-semibold text-gray-700 dark:text-gray-300">Email</TableHead>
                          {allLeaveTypes.map((type) => (
                            <TableHead key={type.id} className="font-semibold text-gray-700 dark:text-gray-300 text-center">
                              {type.leave_type_name}
                            </TableHead>
                          ))}
                          <TableHead className="font-semibold text-gray-700 dark:text-gray-300 text-center">Total</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {displayedEmployeeData.map((emp, index) => {
                          const fullName = `${emp.first_name} ${emp.middle_name || ""} ${emp.last_name}`.trim()
                          const initials = fullName.split(' ').map(n => n[0]).join('').toUpperCase()
                          
                          return (
                            <TableRow
                              key={emp.employee_no}
                              className="hover:bg-gradient-to-r hover:from-emerald-50/50 hover:to-teal-50/50 dark:hover:from-emerald-900/20 dark:hover:to-teal-900/20 transition-all duration-200"
                            >
                              <TableCell className="font-medium">
                                <div className="flex items-center space-x-3">
                                  <Avatar className="h-8 w-8">
                                    <AvatarFallback className="bg-gradient-to-br from-emerald-500 to-teal-600 text-white text-xs font-semibold">
                                      {initials}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <p className="font-semibold text-gray-900 dark:text-gray-100">{fullName}</p>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">{emp.employee_no}</p>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell className="text-gray-600 dark:text-gray-400">
                                {emp.email}
                              </TableCell>
                              {allLeaveTypes.map((type) => (
                                <TableCell key={type.id} className="text-center">
                                  <Badge 
                                    variant="outline" 
                                    className="border-emerald-300 text-emerald-700 dark:border-emerald-600 dark:text-emerald-400"
                                  >
                                    {emp[type.leave_type_name] || 0} days
                                  </Badge>
                                </TableCell>
                              ))}
                              <TableCell className="text-center">
                                <Badge 
                                  variant="default"
                                  className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:hover:bg-emerald-900/50"
                                >
                                  {emp.Total || 0} days
                                </Badge>
                              </TableCell>
                            </TableRow>
                          )
                        })}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  {empTotalPages > 1 && (
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Showing {((empPage - 1) * empPageSize) + 1} to {Math.min(empPage * empPageSize, filteredEmployeeData.length)} of {filteredEmployeeData.length} employees
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEmpPage(Math.max(1, empPage - 1))}
                          disabled={empPage === 1}
                        >
                          Previous
                        </Button>
                        <div className="flex items-center space-x-1">
                          {Array.from({ length: Math.min(5, empTotalPages) }, (_, i) => {
                            const pageNum = i + 1
                            return (
                              <Button
                                key={pageNum}
                                variant={empPage === pageNum ? "default" : "outline"}
                                size="sm"
                                onClick={() => setEmpPage(pageNum)}
                                className="w-8 h-8 p-0"
                              >
                                {pageNum}
                              </Button>
                            )
                          })}
                          {empTotalPages > 5 && (
                            <>
                              <span className="text-gray-400">...</span>
                              <Button
                                variant={empPage === empTotalPages ? "default" : "outline"}
                                size="sm"
                                onClick={() => setEmpPage(empTotalPages)}
                                className="w-8 h-8 p-0"
                              >
                                {empTotalPages}
                              </Button>
                            </>
                          )}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEmpPage(Math.min(empTotalPages, empPage + 1))}
                          disabled={empPage === empTotalPages}
                        >
                          Next
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Leave Type Management */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-3">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600"></div>
                Leave Types Management
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mt-1">Configure and manage organizational leave policies</p>
            </div>
            <Button
              onClick={openCreateDialog}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 dark:from-blue-700 dark:to-indigo-700 dark:hover:from-blue-800 dark:hover:to-indigo-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add New Leave Type
            </Button>
          </div>
          <LeaveTypeTable
            leaveTypes={leaveTypes}
            loading={loadingTypes}
            onEdit={openEditDialog}
            onDelete={openDeleteDialog}
          />
        </div>

      
      </div>

      {/* LEAVE TYPE DIALOGS */}
      <LeaveTypeDialog
        open={dialogOpen}
        mode={dialogMode}
        initialData={selectedLeaveType}
        onClose={() => setDialogOpen(false)}
        onSave={handleSave}
      />
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        leaveTypeName={leaveTypeToDelete?.leave_type_name}
        onCancel={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
      />
    </Screen>
  )
}